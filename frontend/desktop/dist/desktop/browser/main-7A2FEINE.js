import{a as gn,b as pn}from"./chunk-HXMTDF5Q.js";import{A as Zt,B as Si,C as Ri,D as Pi,E as xi,F as Ci,G as Vi,Ga as Wi,H as <PERSON>,Ha as <PERSON>,I as <PERSON>,Ia as <PERSON>,J as ki,K as Mi,L as Fi,M as Oi,N as Li,O as qi,P as Bi,Q as Ui,R as zi,S as $i,e as si,f as oi,g as ai,h as ui,i as ci,j as li,k as hi,l as di,m as fi,n as mi,o as gi,oa as ji,p as pi,q as yi,qa as mn,r as _i,s as vi,t as Re,ta as Gi,u as wi,ua as Ki,v as Ti,va as Qi,w as Ii,x as Ei,y as Ai,z as bi}from"./chunk-RDAQWP7I.js";import{$a as hn,$b as Jt,Ca as Xt,Fb as Hr,Gb as St,Ha as zr,Hb as Rt,Ib as dn,Na as $r,Ob as Yr,Pb as fn,Qb as Xr,Sb as <PERSON>,Tb as Zr,Zb as ti,_b as ei,ac as ni,ba as Or,cb as jr,da as Lr,e as lt,ea as qr,fb as Gr,ga as Te,hb as Kr,hc as ri,ia as J,ja as Ie,lb as be,mb as Qr,mc as ii,nb as Wr,ra as Ee,sa as Ae,tb as Se,wa as Br,za as Ur}from"./chunk-OZCVVD7X.js";var Vo="@",Do=(()=>{class o{doc;delegate;zone;animationType;moduleImpl;_rendererFactoryPromise=null;scheduler=null;injector=J(Ee);loadingSchedulerFn=J(No,{optional:!0});_engine;constructor(i,a,d,_,w){this.doc=i,this.delegate=a,this.zone=d,this.animationType=_,this.moduleImpl=w}ngOnDestroy(){this._engine?.flush()}loadImpl(){let i=()=>this.moduleImpl??import("./chunk-GORDR25F.js").then(d=>d),a;return this.loadingSchedulerFn?a=this.loadingSchedulerFn(i):a=i(),a.catch(d=>{throw new Or(5300,!1)}).then(({\u0275createEngine:d,\u0275AnimationRendererFactory:_})=>{this._engine=d(this.animationType,this.doc);let w=new _(this.delegate,this._engine,this.zone);return this.delegate=w,w})}createRenderer(i,a){let d=this.delegate.createRenderer(i,a);if(d.\u0275type===0)return d;typeof d.throwOnSyntheticProps=="boolean"&&(d.throwOnSyntheticProps=!1);let _=new yn(d);return a?.data?.animation&&!this._rendererFactoryPromise&&(this._rendererFactoryPromise=this.loadImpl()),this._rendererFactoryPromise?.then(w=>{let T=w.createRenderer(i,a);_.use(T),this.scheduler??=this.injector.get(Ur,null,{optional:!0}),this.scheduler?.notify(10)}).catch(w=>{_.use(d)}),_}begin(){this.delegate.begin?.()}end(){this.delegate.end?.()}whenRenderingDone(){return this.delegate.whenRenderingDone?.()??Promise.resolve()}componentReplaced(i){this._engine?.flush(),this.delegate.componentReplaced?.(i)}static \u0275fac=function(a){Gr()};static \u0275prov=Lr({token:o,factory:o.\u0275fac})}return o})(),yn=class{delegate;replay=[];\u0275type=1;constructor(n){this.delegate=n}use(n){if(this.delegate=n,this.replay!==null){for(let i of this.replay)i(n);this.replay=null}}get data(){return this.delegate.data}destroy(){this.replay=null,this.delegate.destroy()}createElement(n,i){return this.delegate.createElement(n,i)}createComment(n){return this.delegate.createComment(n)}createText(n){return this.delegate.createText(n)}get destroyNode(){return this.delegate.destroyNode}appendChild(n,i){this.delegate.appendChild(n,i)}insertBefore(n,i,a,d){this.delegate.insertBefore(n,i,a,d)}removeChild(n,i,a){this.delegate.removeChild(n,i,a)}selectRootElement(n,i){return this.delegate.selectRootElement(n,i)}parentNode(n){return this.delegate.parentNode(n)}nextSibling(n){return this.delegate.nextSibling(n)}setAttribute(n,i,a,d){this.delegate.setAttribute(n,i,a,d)}removeAttribute(n,i,a){this.delegate.removeAttribute(n,i,a)}addClass(n,i){this.delegate.addClass(n,i)}removeClass(n,i){this.delegate.removeClass(n,i)}setStyle(n,i,a,d){this.delegate.setStyle(n,i,a,d)}removeStyle(n,i,a){this.delegate.removeStyle(n,i,a)}setProperty(n,i,a){this.shouldReplay(i)&&this.replay.push(d=>d.setProperty(n,i,a)),this.delegate.setProperty(n,i,a)}setValue(n,i){this.delegate.setValue(n,i)}listen(n,i,a,d){return this.shouldReplay(i)&&this.replay.push(_=>_.listen(n,i,a,d)),this.delegate.listen(n,i,a,d)}shouldReplay(n){return this.replay!==null&&n.startsWith(Vo)}},No=new Te("");function Ji(o="animations"){return Kr("NgAsyncAnimations"),Ie([{provide:jr,useFactory:(n,i,a)=>new Do(n,i,a,o),deps:[Ae,oi,Se]},{provide:$r,useValue:o==="noop"?"NoopAnimations":"BrowserAnimations"}])}var Zi=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ts={};var Pe,es;(function(){var o;function n(g,u){function l(){}l.prototype=u.prototype,g.D=u.prototype,g.prototype=new l,g.prototype.constructor=g,g.C=function(h,f,p){for(var c=Array(arguments.length-2),et=2;et<arguments.length;et++)c[et-2]=arguments[et];return u.prototype[f].apply(h,c)}}function i(){this.blockSize=-1}function a(){this.blockSize=-1,this.blockSize=64,this.g=Array(4),this.B=Array(this.blockSize),this.o=this.h=0,this.s()}n(a,i),a.prototype.s=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.o=this.h=0};function d(g,u,l){l||(l=0);var h=Array(16);if(typeof u=="string")for(var f=0;16>f;++f)h[f]=u.charCodeAt(l++)|u.charCodeAt(l++)<<8|u.charCodeAt(l++)<<16|u.charCodeAt(l++)<<24;else for(f=0;16>f;++f)h[f]=u[l++]|u[l++]<<8|u[l++]<<16|u[l++]<<24;u=g.g[0],l=g.g[1],f=g.g[2];var p=g.g[3],c=u+(p^l&(f^p))+h[0]+3614090360&4294967295;u=l+(c<<7&4294967295|c>>>25),c=p+(f^u&(l^f))+h[1]+3905402710&4294967295,p=u+(c<<12&4294967295|c>>>20),c=f+(l^p&(u^l))+h[2]+606105819&4294967295,f=p+(c<<17&4294967295|c>>>15),c=l+(u^f&(p^u))+h[3]+3250441966&4294967295,l=f+(c<<22&4294967295|c>>>10),c=u+(p^l&(f^p))+h[4]+4118548399&4294967295,u=l+(c<<7&4294967295|c>>>25),c=p+(f^u&(l^f))+h[5]+1200080426&4294967295,p=u+(c<<12&4294967295|c>>>20),c=f+(l^p&(u^l))+h[6]+2821735955&4294967295,f=p+(c<<17&4294967295|c>>>15),c=l+(u^f&(p^u))+h[7]+4249261313&4294967295,l=f+(c<<22&4294967295|c>>>10),c=u+(p^l&(f^p))+h[8]+1770035416&4294967295,u=l+(c<<7&4294967295|c>>>25),c=p+(f^u&(l^f))+h[9]+2336552879&4294967295,p=u+(c<<12&4294967295|c>>>20),c=f+(l^p&(u^l))+h[10]+4294925233&4294967295,f=p+(c<<17&4294967295|c>>>15),c=l+(u^f&(p^u))+h[11]+2304563134&4294967295,l=f+(c<<22&4294967295|c>>>10),c=u+(p^l&(f^p))+h[12]+1804603682&4294967295,u=l+(c<<7&4294967295|c>>>25),c=p+(f^u&(l^f))+h[13]+4254626195&4294967295,p=u+(c<<12&4294967295|c>>>20),c=f+(l^p&(u^l))+h[14]+2792965006&4294967295,f=p+(c<<17&4294967295|c>>>15),c=l+(u^f&(p^u))+h[15]+1236535329&4294967295,l=f+(c<<22&4294967295|c>>>10),c=u+(f^p&(l^f))+h[1]+4129170786&4294967295,u=l+(c<<5&4294967295|c>>>27),c=p+(l^f&(u^l))+h[6]+3225465664&4294967295,p=u+(c<<9&4294967295|c>>>23),c=f+(u^l&(p^u))+h[11]+643717713&4294967295,f=p+(c<<14&4294967295|c>>>18),c=l+(p^u&(f^p))+h[0]+3921069994&4294967295,l=f+(c<<20&4294967295|c>>>12),c=u+(f^p&(l^f))+h[5]+3593408605&4294967295,u=l+(c<<5&4294967295|c>>>27),c=p+(l^f&(u^l))+h[10]+38016083&4294967295,p=u+(c<<9&4294967295|c>>>23),c=f+(u^l&(p^u))+h[15]+3634488961&4294967295,f=p+(c<<14&4294967295|c>>>18),c=l+(p^u&(f^p))+h[4]+3889429448&4294967295,l=f+(c<<20&4294967295|c>>>12),c=u+(f^p&(l^f))+h[9]+568446438&4294967295,u=l+(c<<5&4294967295|c>>>27),c=p+(l^f&(u^l))+h[14]+3275163606&4294967295,p=u+(c<<9&4294967295|c>>>23),c=f+(u^l&(p^u))+h[3]+4107603335&4294967295,f=p+(c<<14&4294967295|c>>>18),c=l+(p^u&(f^p))+h[8]+1163531501&4294967295,l=f+(c<<20&4294967295|c>>>12),c=u+(f^p&(l^f))+h[13]+2850285829&4294967295,u=l+(c<<5&4294967295|c>>>27),c=p+(l^f&(u^l))+h[2]+4243563512&4294967295,p=u+(c<<9&4294967295|c>>>23),c=f+(u^l&(p^u))+h[7]+1735328473&4294967295,f=p+(c<<14&4294967295|c>>>18),c=l+(p^u&(f^p))+h[12]+2368359562&4294967295,l=f+(c<<20&4294967295|c>>>12),c=u+(l^f^p)+h[5]+4294588738&4294967295,u=l+(c<<4&4294967295|c>>>28),c=p+(u^l^f)+h[8]+2272392833&4294967295,p=u+(c<<11&4294967295|c>>>21),c=f+(p^u^l)+h[11]+1839030562&4294967295,f=p+(c<<16&4294967295|c>>>16),c=l+(f^p^u)+h[14]+4259657740&4294967295,l=f+(c<<23&4294967295|c>>>9),c=u+(l^f^p)+h[1]+2763975236&4294967295,u=l+(c<<4&4294967295|c>>>28),c=p+(u^l^f)+h[4]+1272893353&4294967295,p=u+(c<<11&4294967295|c>>>21),c=f+(p^u^l)+h[7]+4139469664&4294967295,f=p+(c<<16&4294967295|c>>>16),c=l+(f^p^u)+h[10]+3200236656&4294967295,l=f+(c<<23&4294967295|c>>>9),c=u+(l^f^p)+h[13]+681279174&4294967295,u=l+(c<<4&4294967295|c>>>28),c=p+(u^l^f)+h[0]+3936430074&4294967295,p=u+(c<<11&4294967295|c>>>21),c=f+(p^u^l)+h[3]+3572445317&4294967295,f=p+(c<<16&4294967295|c>>>16),c=l+(f^p^u)+h[6]+76029189&4294967295,l=f+(c<<23&4294967295|c>>>9),c=u+(l^f^p)+h[9]+3654602809&4294967295,u=l+(c<<4&4294967295|c>>>28),c=p+(u^l^f)+h[12]+3873151461&4294967295,p=u+(c<<11&4294967295|c>>>21),c=f+(p^u^l)+h[15]+530742520&4294967295,f=p+(c<<16&4294967295|c>>>16),c=l+(f^p^u)+h[2]+3299628645&4294967295,l=f+(c<<23&4294967295|c>>>9),c=u+(f^(l|~p))+h[0]+4096336452&4294967295,u=l+(c<<6&4294967295|c>>>26),c=p+(l^(u|~f))+h[7]+1126891415&4294967295,p=u+(c<<10&4294967295|c>>>22),c=f+(u^(p|~l))+h[14]+2878612391&4294967295,f=p+(c<<15&4294967295|c>>>17),c=l+(p^(f|~u))+h[5]+4237533241&4294967295,l=f+(c<<21&4294967295|c>>>11),c=u+(f^(l|~p))+h[12]+1700485571&4294967295,u=l+(c<<6&4294967295|c>>>26),c=p+(l^(u|~f))+h[3]+2399980690&4294967295,p=u+(c<<10&4294967295|c>>>22),c=f+(u^(p|~l))+h[10]+4293915773&4294967295,f=p+(c<<15&4294967295|c>>>17),c=l+(p^(f|~u))+h[1]+2240044497&4294967295,l=f+(c<<21&4294967295|c>>>11),c=u+(f^(l|~p))+h[8]+1873313359&4294967295,u=l+(c<<6&4294967295|c>>>26),c=p+(l^(u|~f))+h[15]+4264355552&4294967295,p=u+(c<<10&4294967295|c>>>22),c=f+(u^(p|~l))+h[6]+2734768916&4294967295,f=p+(c<<15&4294967295|c>>>17),c=l+(p^(f|~u))+h[13]+1309151649&4294967295,l=f+(c<<21&4294967295|c>>>11),c=u+(f^(l|~p))+h[4]+4149444226&4294967295,u=l+(c<<6&4294967295|c>>>26),c=p+(l^(u|~f))+h[11]+3174756917&4294967295,p=u+(c<<10&4294967295|c>>>22),c=f+(u^(p|~l))+h[2]+718787259&4294967295,f=p+(c<<15&4294967295|c>>>17),c=l+(p^(f|~u))+h[9]+3951481745&4294967295,g.g[0]=g.g[0]+u&4294967295,g.g[1]=g.g[1]+(f+(c<<21&4294967295|c>>>11))&4294967295,g.g[2]=g.g[2]+f&4294967295,g.g[3]=g.g[3]+p&4294967295}a.prototype.u=function(g,u){u===void 0&&(u=g.length);for(var l=u-this.blockSize,h=this.B,f=this.h,p=0;p<u;){if(f==0)for(;p<=l;)d(this,g,p),p+=this.blockSize;if(typeof g=="string"){for(;p<u;)if(h[f++]=g.charCodeAt(p++),f==this.blockSize){d(this,h),f=0;break}}else for(;p<u;)if(h[f++]=g[p++],f==this.blockSize){d(this,h),f=0;break}}this.h=f,this.o+=u},a.prototype.v=function(){var g=Array((56>this.h?this.blockSize:2*this.blockSize)-this.h);g[0]=128;for(var u=1;u<g.length-8;++u)g[u]=0;var l=8*this.o;for(u=g.length-8;u<g.length;++u)g[u]=l&255,l/=256;for(this.u(g),g=Array(16),u=l=0;4>u;++u)for(var h=0;32>h;h+=8)g[l++]=this.g[u]>>>h&255;return g};function _(g,u){var l=T;return Object.prototype.hasOwnProperty.call(l,g)?l[g]:l[g]=u(g)}function w(g,u){this.h=u;for(var l=[],h=!0,f=g.length-1;0<=f;f--){var p=g[f]|0;h&&p==u||(l[f]=p,h=!1)}this.g=l}var T={};function R(g){return-128<=g&&128>g?_(g,function(u){return new w([u|0],0>u?-1:0)}):new w([g|0],0>g?-1:0)}function P(g){if(isNaN(g)||!isFinite(g))return G;if(0>g)return V(P(-g));for(var u=[],l=1,h=0;g>=l;h++)u[h]=g/l|0,l*=4294967296;return new w(u,0)}function H(g,u){if(g.length==0)throw Error("number format error: empty string");if(u=u||10,2>u||36<u)throw Error("radix out of range: "+u);if(g.charAt(0)=="-")return V(H(g.substring(1),u));if(0<=g.indexOf("-"))throw Error('number format error: interior "-" character');for(var l=P(Math.pow(u,8)),h=G,f=0;f<g.length;f+=8){var p=Math.min(8,g.length-f),c=parseInt(g.substring(f,f+p),u);8>p?(p=P(Math.pow(u,p)),h=h.j(p).add(P(c))):(h=h.j(l),h=h.add(P(c)))}return h}var G=R(0),C=R(1),ft=R(16777216);o=w.prototype,o.m=function(){if(M(this))return-V(this).m();for(var g=0,u=1,l=0;l<this.g.length;l++){var h=this.i(l);g+=(0<=h?h:4294967296+h)*u,u*=4294967296}return g},o.toString=function(g){if(g=g||10,2>g||36<g)throw Error("radix out of range: "+g);if(b(this))return"0";if(M(this))return"-"+V(this).toString(g);for(var u=P(Math.pow(g,6)),l=this,h="";;){var f=st(l,u).g;l=Tt(l,f.j(u));var p=((0<l.g.length?l.g[0]:l.h)>>>0).toString(g);if(l=f,b(l))return p+h;for(;6>p.length;)p="0"+p;h=p+h}},o.i=function(g){return 0>g?0:g<this.g.length?this.g[g]:this.h};function b(g){if(g.h!=0)return!1;for(var u=0;u<g.g.length;u++)if(g.g[u]!=0)return!1;return!0}function M(g){return g.h==-1}o.l=function(g){return g=Tt(this,g),M(g)?-1:b(g)?0:1};function V(g){for(var u=g.g.length,l=[],h=0;h<u;h++)l[h]=~g.g[h];return new w(l,~g.h).add(C)}o.abs=function(){return M(this)?V(this):this},o.add=function(g){for(var u=Math.max(this.g.length,g.g.length),l=[],h=0,f=0;f<=u;f++){var p=h+(this.i(f)&65535)+(g.i(f)&65535),c=(p>>>16)+(this.i(f)>>>16)+(g.i(f)>>>16);h=c>>>16,p&=65535,c&=65535,l[f]=c<<16|p}return new w(l,l[l.length-1]&-2147483648?-1:0)};function Tt(g,u){return g.add(V(u))}o.j=function(g){if(b(this)||b(g))return G;if(M(this))return M(g)?V(this).j(V(g)):V(V(this).j(g));if(M(g))return V(this.j(V(g)));if(0>this.l(ft)&&0>g.l(ft))return P(this.m()*g.m());for(var u=this.g.length+g.g.length,l=[],h=0;h<2*u;h++)l[h]=0;for(h=0;h<this.g.length;h++)for(var f=0;f<g.g.length;f++){var p=this.i(h)>>>16,c=this.i(h)&65535,et=g.i(f)>>>16,Dt=g.i(f)&65535;l[2*h+2*f]+=c*Dt,Y(l,2*h+2*f),l[2*h+2*f+1]+=p*Dt,Y(l,2*h+2*f+1),l[2*h+2*f+1]+=c*et,Y(l,2*h+2*f+1),l[2*h+2*f+2]+=p*et,Y(l,2*h+2*f+2)}for(h=0;h<u;h++)l[h]=l[2*h+1]<<16|l[2*h];for(h=u;h<2*u;h++)l[h]=0;return new w(l,0)};function Y(g,u){for(;(g[u]&65535)!=g[u];)g[u+1]+=g[u]>>>16,g[u]&=65535,u++}function K(g,u){this.g=g,this.h=u}function st(g,u){if(b(u))throw Error("division by zero");if(b(g))return new K(G,G);if(M(g))return u=st(V(g),u),new K(V(u.g),V(u.h));if(M(u))return u=st(g,V(u)),new K(V(u.g),u.h);if(30<g.g.length){if(M(g)||M(u))throw Error("slowDivide_ only works with positive integers.");for(var l=C,h=u;0>=h.l(g);)l=re(l),h=re(h);var f=X(l,1),p=X(h,1);for(h=X(h,2),l=X(l,2);!b(h);){var c=p.add(h);0>=c.l(g)&&(f=f.add(l),p=c),h=X(h,1),l=X(l,1)}return u=Tt(g,f.j(u)),new K(f,u)}for(f=G;0<=g.l(u);){for(l=Math.max(1,Math.floor(g.m()/u.m())),h=Math.ceil(Math.log(l)/Math.LN2),h=48>=h?1:Math.pow(2,h-48),p=P(l),c=p.j(u);M(c)||0<c.l(g);)l-=h,p=P(l),c=p.j(u);b(p)&&(p=C),f=f.add(p),g=Tt(g,c)}return new K(f,g)}o.A=function(g){return st(this,g).h},o.and=function(g){for(var u=Math.max(this.g.length,g.g.length),l=[],h=0;h<u;h++)l[h]=this.i(h)&g.i(h);return new w(l,this.h&g.h)},o.or=function(g){for(var u=Math.max(this.g.length,g.g.length),l=[],h=0;h<u;h++)l[h]=this.i(h)|g.i(h);return new w(l,this.h|g.h)},o.xor=function(g){for(var u=Math.max(this.g.length,g.g.length),l=[],h=0;h<u;h++)l[h]=this.i(h)^g.i(h);return new w(l,this.h^g.h)};function re(g){for(var u=g.g.length+1,l=[],h=0;h<u;h++)l[h]=g.i(h)<<1|g.i(h-1)>>>31;return new w(l,g.h)}function X(g,u){var l=u>>5;u%=32;for(var h=g.g.length-l,f=[],p=0;p<h;p++)f[p]=0<u?g.i(p+l)>>>u|g.i(p+l+1)<<32-u:g.i(p+l);return new w(f,g.h)}a.prototype.digest=a.prototype.v,a.prototype.reset=a.prototype.s,a.prototype.update=a.prototype.u,es=ts.Md5=a,w.prototype.add=w.prototype.add,w.prototype.multiply=w.prototype.j,w.prototype.modulo=w.prototype.A,w.prototype.compare=w.prototype.l,w.prototype.toNumber=w.prototype.m,w.prototype.toString=w.prototype.toString,w.prototype.getBits=w.prototype.i,w.fromNumber=P,w.fromString=H,Pe=ts.Integer=w}).apply(typeof Zi<"u"?Zi:typeof self<"u"?self:typeof window<"u"?window:{});var xe=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},it={};var ns,ko,rs,is,ss,os,as,us,cs;(function(){var o,n=typeof Object.defineProperties=="function"?Object.defineProperty:function(t,e,r){return t==Array.prototype||t==Object.prototype||(t[e]=r.value),t};function i(t){t=[typeof globalThis=="object"&&globalThis,t,typeof window=="object"&&window,typeof self=="object"&&self,typeof xe=="object"&&xe];for(var e=0;e<t.length;++e){var r=t[e];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")}var a=i(this);function d(t,e){if(e)t:{var r=a;t=t.split(".");for(var s=0;s<t.length-1;s++){var m=t[s];if(!(m in r))break t;r=r[m]}t=t[t.length-1],s=r[t],e=e(s),e!=s&&e!=null&&n(r,t,{configurable:!0,writable:!0,value:e})}}function _(t,e){t instanceof String&&(t+="");var r=0,s=!1,m={next:function(){if(!s&&r<t.length){var y=r++;return{value:e(y,t[y]),done:!1}}return s=!0,{done:!0,value:void 0}}};return m[Symbol.iterator]=function(){return m},m}d("Array.prototype.values",function(t){return t||function(){return _(this,function(e,r){return r})}});var w=w||{},T=this||self;function R(t){var e=typeof t;return e=e!="object"?e:t?Array.isArray(t)?"array":e:"null",e=="array"||e=="object"&&typeof t.length=="number"}function P(t){var e=typeof t;return e=="object"&&t!=null||e=="function"}function H(t,e,r){return t.call.apply(t.bind,arguments)}function G(t,e,r){if(!t)throw Error();if(2<arguments.length){var s=Array.prototype.slice.call(arguments,2);return function(){var m=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(m,s),t.apply(e,m)}}return function(){return t.apply(e,arguments)}}function C(t,e,r){return C=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?H:G,C.apply(null,arguments)}function ft(t,e){var r=Array.prototype.slice.call(arguments,1);return function(){var s=r.slice();return s.push.apply(s,arguments),t.apply(this,s)}}function b(t,e){function r(){}r.prototype=e.prototype,t.aa=e.prototype,t.prototype=new r,t.prototype.constructor=t,t.Qb=function(s,m,y){for(var v=Array(arguments.length-2),A=2;A<arguments.length;A++)v[A-2]=arguments[A];return e.prototype[m].apply(s,v)}}function M(t){let e=t.length;if(0<e){let r=Array(e);for(let s=0;s<e;s++)r[s]=t[s];return r}return[]}function V(t,e){for(let r=1;r<arguments.length;r++){let s=arguments[r];if(R(s)){let m=t.length||0,y=s.length||0;t.length=m+y;for(let v=0;v<y;v++)t[m+v]=s[v]}else t.push(s)}}class Tt{constructor(e,r){this.i=e,this.j=r,this.h=0,this.g=null}get(){let e;return 0<this.h?(this.h--,e=this.g,this.g=e.next,e.next=null):e=this.i(),e}}function Y(t){return/^[\s\xa0]*$/.test(t)}function K(){var t=T.navigator;return t&&(t=t.userAgent)?t:""}function st(t){return st[" "](t),t}st[" "]=function(){};var re=K().indexOf("Gecko")!=-1&&!(K().toLowerCase().indexOf("webkit")!=-1&&K().indexOf("Edge")==-1)&&!(K().indexOf("Trident")!=-1||K().indexOf("MSIE")!=-1)&&K().indexOf("Edge")==-1;function X(t,e,r){for(let s in t)e.call(r,t[s],s,t)}function g(t,e){for(let r in t)e.call(void 0,t[r],r,t)}function u(t){let e={};for(let r in t)e[r]=t[r];return e}let l="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function h(t,e){let r,s;for(let m=1;m<arguments.length;m++){s=arguments[m];for(r in s)t[r]=s[r];for(let y=0;y<l.length;y++)r=l[y],Object.prototype.hasOwnProperty.call(s,r)&&(t[r]=s[r])}}function f(t){var e=1;t=t.split(":");let r=[];for(;0<e&&t.length;)r.push(t.shift()),e--;return t.length&&r.push(t.join(":")),r}function p(t){T.setTimeout(()=>{throw t},0)}function c(){var t=qe;let e=null;return t.g&&(e=t.g,t.g=t.g.next,t.g||(t.h=null),e.next=null),e}class et{constructor(){this.h=this.g=null}add(e,r){let s=Dt.get();s.set(e,r),this.h?this.h.next=s:this.g=s,this.h=s}}var Dt=new Tt(()=>new Ys,t=>t.reset());class Ys{constructor(){this.next=this.g=this.h=null}set(e,r){this.h=e,this.g=r,this.next=null}reset(){this.next=this.g=this.h=null}}let Nt,kt=!1,qe=new et,Fn=()=>{let t=T.Promise.resolve(void 0);Nt=()=>{t.then(Xs)}};var Xs=()=>{for(var t;t=c();){try{t.h.call(t.g)}catch(r){p(r)}var e=Dt;e.j(t),100>e.h&&(e.h++,t.next=e.g,e.g=t)}kt=!1};function ot(){this.s=this.s,this.C=this.C}ot.prototype.s=!1,ot.prototype.ma=function(){this.s||(this.s=!0,this.N())},ot.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()};function F(t,e){this.type=t,this.g=this.target=e,this.defaultPrevented=!1}F.prototype.h=function(){this.defaultPrevented=!0};var Js=function(){if(!T.addEventListener||!Object.defineProperty)return!1;var t=!1,e=Object.defineProperty({},"passive",{get:function(){t=!0}});try{let r=()=>{};T.addEventListener("test",r,e),T.removeEventListener("test",r,e)}catch{}return t}();function Mt(t,e){if(F.call(this,t?t.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,t){var r=this.type=t.type,s=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:null;if(this.target=t.target||t.srcElement,this.g=e,e=t.relatedTarget){if(re){t:{try{st(e.nodeName);var m=!0;break t}catch{}m=!1}m||(e=null)}}else r=="mouseover"?e=t.fromElement:r=="mouseout"&&(e=t.toElement);this.relatedTarget=e,s?(this.clientX=s.clientX!==void 0?s.clientX:s.pageX,this.clientY=s.clientY!==void 0?s.clientY:s.pageY,this.screenX=s.screenX||0,this.screenY=s.screenY||0):(this.clientX=t.clientX!==void 0?t.clientX:t.pageX,this.clientY=t.clientY!==void 0?t.clientY:t.pageY,this.screenX=t.screenX||0,this.screenY=t.screenY||0),this.button=t.button,this.key=t.key||"",this.ctrlKey=t.ctrlKey,this.altKey=t.altKey,this.shiftKey=t.shiftKey,this.metaKey=t.metaKey,this.pointerId=t.pointerId||0,this.pointerType=typeof t.pointerType=="string"?t.pointerType:Zs[t.pointerType]||"",this.state=t.state,this.i=t,t.defaultPrevented&&Mt.aa.h.call(this)}}b(Mt,F);var Zs={2:"touch",3:"pen",4:"mouse"};Mt.prototype.h=function(){Mt.aa.h.call(this);var t=this.i;t.preventDefault?t.preventDefault():t.returnValue=!1};var Ft="closure_listenable_"+(1e6*Math.random()|0),to=0;function eo(t,e,r,s,m){this.listener=t,this.proxy=null,this.src=e,this.type=r,this.capture=!!s,this.ha=m,this.key=++to,this.da=this.fa=!1}function ie(t){t.da=!0,t.listener=null,t.proxy=null,t.src=null,t.ha=null}function se(t){this.src=t,this.g={},this.h=0}se.prototype.add=function(t,e,r,s,m){var y=t.toString();t=this.g[y],t||(t=this.g[y]=[],this.h++);var v=Ue(t,e,s,m);return-1<v?(e=t[v],r||(e.fa=!1)):(e=new eo(e,this.src,y,!!s,m),e.fa=r,t.push(e)),e};function Be(t,e){var r=e.type;if(r in t.g){var s=t.g[r],m=Array.prototype.indexOf.call(s,e,void 0),y;(y=0<=m)&&Array.prototype.splice.call(s,m,1),y&&(ie(e),t.g[r].length==0&&(delete t.g[r],t.h--))}}function Ue(t,e,r,s){for(var m=0;m<t.length;++m){var y=t[m];if(!y.da&&y.listener==e&&y.capture==!!r&&y.ha==s)return m}return-1}var ze="closure_lm_"+(1e6*Math.random()|0),$e={};function On(t,e,r,s,m){if(s&&s.once)return qn(t,e,r,s,m);if(Array.isArray(e)){for(var y=0;y<e.length;y++)On(t,e[y],r,s,m);return null}return r=Qe(r),t&&t[Ft]?t.K(e,r,P(s)?!!s.capture:!!s,m):Ln(t,e,r,!1,s,m)}function Ln(t,e,r,s,m,y){if(!e)throw Error("Invalid event type");var v=P(m)?!!m.capture:!!m,A=Ge(t);if(A||(t[ze]=A=new se(t)),r=A.add(e,r,s,v,y),r.proxy)return r;if(s=no(),r.proxy=s,s.src=t,s.listener=r,t.addEventListener)Js||(m=v),m===void 0&&(m=!1),t.addEventListener(e.toString(),s,m);else if(t.attachEvent)t.attachEvent(Un(e.toString()),s);else if(t.addListener&&t.removeListener)t.addListener(s);else throw Error("addEventListener and attachEvent are unavailable.");return r}function no(){function t(r){return e.call(t.src,t.listener,r)}let e=ro;return t}function qn(t,e,r,s,m){if(Array.isArray(e)){for(var y=0;y<e.length;y++)qn(t,e[y],r,s,m);return null}return r=Qe(r),t&&t[Ft]?t.L(e,r,P(s)?!!s.capture:!!s,m):Ln(t,e,r,!0,s,m)}function Bn(t,e,r,s,m){if(Array.isArray(e))for(var y=0;y<e.length;y++)Bn(t,e[y],r,s,m);else s=P(s)?!!s.capture:!!s,r=Qe(r),t&&t[Ft]?(t=t.i,e=String(e).toString(),e in t.g&&(y=t.g[e],r=Ue(y,r,s,m),-1<r&&(ie(y[r]),Array.prototype.splice.call(y,r,1),y.length==0&&(delete t.g[e],t.h--)))):t&&(t=Ge(t))&&(e=t.g[e.toString()],t=-1,e&&(t=Ue(e,r,s,m)),(r=-1<t?e[t]:null)&&je(r))}function je(t){if(typeof t!="number"&&t&&!t.da){var e=t.src;if(e&&e[Ft])Be(e.i,t);else{var r=t.type,s=t.proxy;e.removeEventListener?e.removeEventListener(r,s,t.capture):e.detachEvent?e.detachEvent(Un(r),s):e.addListener&&e.removeListener&&e.removeListener(s),(r=Ge(e))?(Be(r,t),r.h==0&&(r.src=null,e[ze]=null)):ie(t)}}}function Un(t){return t in $e?$e[t]:$e[t]="on"+t}function ro(t,e){if(t.da)t=!0;else{e=new Mt(e,this);var r=t.listener,s=t.ha||t.src;t.fa&&je(t),t=r.call(s,e)}return t}function Ge(t){return t=t[ze],t instanceof se?t:null}var Ke="__closure_events_fn_"+(1e9*Math.random()>>>0);function Qe(t){return typeof t=="function"?t:(t[Ke]||(t[Ke]=function(e){return t.handleEvent(e)}),t[Ke])}function O(){ot.call(this),this.i=new se(this),this.M=this,this.F=null}b(O,ot),O.prototype[Ft]=!0,O.prototype.removeEventListener=function(t,e,r,s){Bn(this,t,e,r,s)};function B(t,e){var r,s=t.F;if(s)for(r=[];s;s=s.F)r.push(s);if(t=t.M,s=e.type||e,typeof e=="string")e=new F(e,t);else if(e instanceof F)e.target=e.target||t;else{var m=e;e=new F(s,t),h(e,m)}if(m=!0,r)for(var y=r.length-1;0<=y;y--){var v=e.g=r[y];m=oe(v,s,!0,e)&&m}if(v=e.g=t,m=oe(v,s,!0,e)&&m,m=oe(v,s,!1,e)&&m,r)for(y=0;y<r.length;y++)v=e.g=r[y],m=oe(v,s,!1,e)&&m}O.prototype.N=function(){if(O.aa.N.call(this),this.i){var t=this.i,e;for(e in t.g){for(var r=t.g[e],s=0;s<r.length;s++)ie(r[s]);delete t.g[e],t.h--}}this.F=null},O.prototype.K=function(t,e,r,s){return this.i.add(String(t),e,!1,r,s)},O.prototype.L=function(t,e,r,s){return this.i.add(String(t),e,!0,r,s)};function oe(t,e,r,s){if(e=t.i.g[String(e)],!e)return!0;e=e.concat();for(var m=!0,y=0;y<e.length;++y){var v=e[y];if(v&&!v.da&&v.capture==r){var A=v.listener,D=v.ha||v.src;v.fa&&Be(t.i,v),m=A.call(D,s)!==!1&&m}}return m&&!s.defaultPrevented}function zn(t,e,r){if(typeof t=="function")r&&(t=C(t,r));else if(t&&typeof t.handleEvent=="function")t=C(t.handleEvent,t);else throw Error("Invalid listener argument");return 2147483647<Number(e)?-1:T.setTimeout(t,e||0)}function $n(t){t.g=zn(()=>{t.g=null,t.i&&(t.i=!1,$n(t))},t.l);let e=t.h;t.h=null,t.m.apply(null,e)}class io extends ot{constructor(e,r){super(),this.m=e,this.l=r,this.h=null,this.i=!1,this.g=null}j(e){this.h=arguments,this.g?this.i=!0:$n(this)}N(){super.N(),this.g&&(T.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}}function Ot(t){ot.call(this),this.h=t,this.g={}}b(Ot,ot);var jn=[];function Gn(t){X(t.g,function(e,r){this.g.hasOwnProperty(r)&&je(e)},t),t.g={}}Ot.prototype.N=function(){Ot.aa.N.call(this),Gn(this)},Ot.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var We=T.JSON.stringify,so=T.JSON.parse,oo=class{stringify(t){return T.JSON.stringify(t,void 0)}parse(t){return T.JSON.parse(t,void 0)}};function He(){}He.prototype.h=null;function Kn(t){return t.h||(t.h=t.i())}function Qn(){}var Lt={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function Ye(){F.call(this,"d")}b(Ye,F);function Xe(){F.call(this,"c")}b(Xe,F);var mt={},Wn=null;function ae(){return Wn=Wn||new O}mt.La="serverreachability";function Hn(t){F.call(this,mt.La,t)}b(Hn,F);function qt(t){let e=ae();B(e,new Hn(e))}mt.STAT_EVENT="statevent";function Yn(t,e){F.call(this,mt.STAT_EVENT,t),this.stat=e}b(Yn,F);function U(t){let e=ae();B(e,new Yn(e,t))}mt.Ma="timingevent";function Xn(t,e){F.call(this,mt.Ma,t),this.size=e}b(Xn,F);function Bt(t,e){if(typeof t!="function")throw Error("Fn must not be null and must be a function");return T.setTimeout(function(){t()},e)}function Ut(){this.g=!0}Ut.prototype.xa=function(){this.g=!1};function ao(t,e,r,s,m,y){t.info(function(){if(t.g)if(y)for(var v="",A=y.split("&"),D=0;D<A.length;D++){var E=A[D].split("=");if(1<E.length){var L=E[0];E=E[1];var q=L.split("_");v=2<=q.length&&q[1]=="type"?v+(L+"="+E+"&"):v+(L+"=redacted&")}}else v=null;else v=y;return"XMLHTTP REQ ("+s+") [attempt "+m+"]: "+e+`
`+r+`
`+v})}function uo(t,e,r,s,m,y,v){t.info(function(){return"XMLHTTP RESP ("+s+") [ attempt "+m+"]: "+e+`
`+r+`
`+y+" "+v})}function It(t,e,r,s){t.info(function(){return"XMLHTTP TEXT ("+e+"): "+lo(t,r)+(s?" "+s:"")})}function co(t,e){t.info(function(){return"TIMEOUT: "+e})}Ut.prototype.info=function(){};function lo(t,e){if(!t.g)return e;if(!e)return null;try{var r=JSON.parse(e);if(r){for(t=0;t<r.length;t++)if(Array.isArray(r[t])){var s=r[t];if(!(2>s.length)){var m=s[1];if(Array.isArray(m)&&!(1>m.length)){var y=m[0];if(y!="noop"&&y!="stop"&&y!="close")for(var v=1;v<m.length;v++)m[v]=""}}}}return We(r)}catch{return e}}var ue={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},Jn={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"},Je;function ce(){}b(ce,He),ce.prototype.g=function(){return new XMLHttpRequest},ce.prototype.i=function(){return{}},Je=new ce;function at(t,e,r,s){this.j=t,this.i=e,this.l=r,this.R=s||1,this.U=new Ot(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new Zn}function Zn(){this.i=null,this.g="",this.h=!1}var tr={},Ze={};function tn(t,e,r){t.L=1,t.v=fe(nt(e)),t.m=r,t.P=!0,er(t,null)}function er(t,e){t.F=Date.now(),le(t),t.A=nt(t.v);var r=t.A,s=t.R;Array.isArray(s)||(s=[String(s)]),gr(r.i,"t",s),t.C=0,r=t.j.J,t.h=new Zn,t.g=Nr(t.j,r?e:null,!t.m),0<t.O&&(t.M=new io(C(t.Y,t,t.g),t.O)),e=t.U,r=t.g,s=t.ca;var m="readystatechange";Array.isArray(m)||(m&&(jn[0]=m.toString()),m=jn);for(var y=0;y<m.length;y++){var v=On(r,m[y],s||e.handleEvent,!1,e.h||e);if(!v)break;e.g[v.key]=v}e=t.H?u(t.H):{},t.m?(t.u||(t.u="POST"),e["Content-Type"]="application/x-www-form-urlencoded",t.g.ea(t.A,t.u,t.m,e)):(t.u="GET",t.g.ea(t.A,t.u,null,e)),qt(),ao(t.i,t.u,t.A,t.l,t.R,t.m)}at.prototype.ca=function(t){t=t.target;let e=this.M;e&&rt(t)==3?e.j():this.Y(t)},at.prototype.Y=function(t){try{if(t==this.g)t:{let q=rt(this.g);var e=this.g.Ba();let bt=this.g.Z();if(!(3>q)&&(q!=3||this.g&&(this.h.h||this.g.oa()||Ir(this.g)))){this.J||q!=4||e==7||(e==8||0>=bt?qt(3):qt(2)),en(this);var r=this.g.Z();this.X=r;e:if(nr(this)){var s=Ir(this.g);t="";var m=s.length,y=rt(this.g)==4;if(!this.h.i){if(typeof TextDecoder>"u"){gt(this),zt(this);var v="";break e}this.h.i=new T.TextDecoder}for(e=0;e<m;e++)this.h.h=!0,t+=this.h.i.decode(s[e],{stream:!(y&&e==m-1)});s.length=0,this.h.g+=t,this.C=0,v=this.h.g}else v=this.g.oa();if(this.o=r==200,uo(this.i,this.u,this.A,this.l,this.R,q,r),this.o){if(this.T&&!this.K){e:{if(this.g){var A,D=this.g;if((A=D.g?D.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!Y(A)){var E=A;break e}}E=null}if(r=E)It(this.i,this.l,r,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,nn(this,r);else{this.o=!1,this.s=3,U(12),gt(this),zt(this);break t}}if(this.P){r=!0;let Q;for(;!this.J&&this.C<v.length;)if(Q=ho(this,v),Q==Ze){q==4&&(this.s=4,U(14),r=!1),It(this.i,this.l,null,"[Incomplete Response]");break}else if(Q==tr){this.s=4,U(15),It(this.i,this.l,v,"[Invalid Chunk]"),r=!1;break}else It(this.i,this.l,Q,null),nn(this,Q);if(nr(this)&&this.C!=0&&(this.h.g=this.h.g.slice(this.C),this.C=0),q!=4||v.length!=0||this.h.h||(this.s=1,U(16),r=!1),this.o=this.o&&r,!r)It(this.i,this.l,v,"[Invalid Chunked Response]"),gt(this),zt(this);else if(0<v.length&&!this.W){this.W=!0;var L=this.j;L.g==this&&L.ba&&!L.M&&(L.j.info("Great, no buffering proxy detected. Bytes received: "+v.length),cn(L),L.M=!0,U(11))}}else It(this.i,this.l,v,null),nn(this,v);q==4&&gt(this),this.o&&!this.J&&(q==4?xr(this.j,this):(this.o=!1,le(this)))}else xo(this.g),r==400&&0<v.indexOf("Unknown SID")?(this.s=3,U(12)):(this.s=0,U(13)),gt(this),zt(this)}}}catch{}finally{}};function nr(t){return t.g?t.u=="GET"&&t.L!=2&&t.j.Ca:!1}function ho(t,e){var r=t.C,s=e.indexOf(`
`,r);return s==-1?Ze:(r=Number(e.substring(r,s)),isNaN(r)?tr:(s+=1,s+r>e.length?Ze:(e=e.slice(s,s+r),t.C=s+r,e)))}at.prototype.cancel=function(){this.J=!0,gt(this)};function le(t){t.S=Date.now()+t.I,rr(t,t.I)}function rr(t,e){if(t.B!=null)throw Error("WatchDog timer not null");t.B=Bt(C(t.ba,t),e)}function en(t){t.B&&(T.clearTimeout(t.B),t.B=null)}at.prototype.ba=function(){this.B=null;let t=Date.now();0<=t-this.S?(co(this.i,this.A),this.L!=2&&(qt(),U(17)),gt(this),this.s=2,zt(this)):rr(this,this.S-t)};function zt(t){t.j.G==0||t.J||xr(t.j,t)}function gt(t){en(t);var e=t.M;e&&typeof e.ma=="function"&&e.ma(),t.M=null,Gn(t.U),t.g&&(e=t.g,t.g=null,e.abort(),e.ma())}function nn(t,e){try{var r=t.j;if(r.G!=0&&(r.g==t||rn(r.h,t))){if(!t.K&&rn(r.h,t)&&r.G==3){try{var s=r.Da.g.parse(e)}catch{s=null}if(Array.isArray(s)&&s.length==3){var m=s;if(m[0]==0){t:if(!r.u){if(r.g)if(r.g.F+3e3<t.F)_e(r),pe(r);else break t;un(r),U(18)}}else r.za=m[1],0<r.za-r.T&&37500>m[2]&&r.F&&r.v==0&&!r.C&&(r.C=Bt(C(r.Za,r),6e3));if(1>=or(r.h)&&r.ca){try{r.ca()}catch{}r.ca=void 0}}else yt(r,11)}else if((t.K||r.g==t)&&_e(r),!Y(e))for(m=r.Da.g.parse(e),e=0;e<m.length;e++){let E=m[e];if(r.T=E[0],E=E[1],r.G==2)if(E[0]=="c"){r.K=E[1],r.ia=E[2];let L=E[3];L!=null&&(r.la=L,r.j.info("VER="+r.la));let q=E[4];q!=null&&(r.Aa=q,r.j.info("SVER="+r.Aa));let bt=E[5];bt!=null&&typeof bt=="number"&&0<bt&&(s=1.5*bt,r.L=s,r.j.info("backChannelRequestTimeoutMs_="+s)),s=r;let Q=t.g;if(Q){let we=Q.g?Q.g.getResponseHeader("X-Client-Wire-Protocol"):null;if(we){var y=s.h;y.g||we.indexOf("spdy")==-1&&we.indexOf("quic")==-1&&we.indexOf("h2")==-1||(y.j=y.l,y.g=new Set,y.h&&(sn(y,y.h),y.h=null))}if(s.D){let ln=Q.g?Q.g.getResponseHeader("X-HTTP-Session-Id"):null;ln&&(s.ya=ln,S(s.I,s.D,ln))}}r.G=3,r.l&&r.l.ua(),r.ba&&(r.R=Date.now()-t.F,r.j.info("Handshake RTT: "+r.R+"ms")),s=r;var v=t;if(s.qa=Dr(s,s.J?s.ia:null,s.W),v.K){ar(s.h,v);var A=v,D=s.L;D&&(A.I=D),A.B&&(en(A),le(A)),s.g=v}else Rr(s);0<r.i.length&&ye(r)}else E[0]!="stop"&&E[0]!="close"||yt(r,7);else r.G==3&&(E[0]=="stop"||E[0]=="close"?E[0]=="stop"?yt(r,7):an(r):E[0]!="noop"&&r.l&&r.l.ta(E),r.v=0)}}qt(4)}catch{}}var fo=class{constructor(t,e){this.g=t,this.map=e}};function ir(t){this.l=t||10,T.PerformanceNavigationTiming?(t=T.performance.getEntriesByType("navigation"),t=0<t.length&&(t[0].nextHopProtocol=="hq"||t[0].nextHopProtocol=="h2")):t=!!(T.chrome&&T.chrome.loadTimes&&T.chrome.loadTimes()&&T.chrome.loadTimes().wasFetchedViaSpdy),this.j=t?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function sr(t){return t.h?!0:t.g?t.g.size>=t.j:!1}function or(t){return t.h?1:t.g?t.g.size:0}function rn(t,e){return t.h?t.h==e:t.g?t.g.has(e):!1}function sn(t,e){t.g?t.g.add(e):t.h=e}function ar(t,e){t.h&&t.h==e?t.h=null:t.g&&t.g.has(e)&&t.g.delete(e)}ir.prototype.cancel=function(){if(this.i=ur(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&this.g.size!==0){for(let t of this.g.values())t.cancel();this.g.clear()}};function ur(t){if(t.h!=null)return t.i.concat(t.h.D);if(t.g!=null&&t.g.size!==0){let e=t.i;for(let r of t.g.values())e=e.concat(r.D);return e}return M(t.i)}function mo(t){if(t.V&&typeof t.V=="function")return t.V();if(typeof Map<"u"&&t instanceof Map||typeof Set<"u"&&t instanceof Set)return Array.from(t.values());if(typeof t=="string")return t.split("");if(R(t)){for(var e=[],r=t.length,s=0;s<r;s++)e.push(t[s]);return e}e=[],r=0;for(s in t)e[r++]=t[s];return e}function go(t){if(t.na&&typeof t.na=="function")return t.na();if(!t.V||typeof t.V!="function"){if(typeof Map<"u"&&t instanceof Map)return Array.from(t.keys());if(!(typeof Set<"u"&&t instanceof Set)){if(R(t)||typeof t=="string"){var e=[];t=t.length;for(var r=0;r<t;r++)e.push(r);return e}e=[],r=0;for(let s in t)e[r++]=s;return e}}}function cr(t,e){if(t.forEach&&typeof t.forEach=="function")t.forEach(e,void 0);else if(R(t)||typeof t=="string")Array.prototype.forEach.call(t,e,void 0);else for(var r=go(t),s=mo(t),m=s.length,y=0;y<m;y++)e.call(void 0,s[y],r&&r[y],t)}var lr=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function po(t,e){if(t){t=t.split("&");for(var r=0;r<t.length;r++){var s=t[r].indexOf("="),m=null;if(0<=s){var y=t[r].substring(0,s);m=t[r].substring(s+1)}else y=t[r];e(y,m?decodeURIComponent(m.replace(/\+/g," ")):"")}}}function pt(t){if(this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,t instanceof pt){this.h=t.h,he(this,t.j),this.o=t.o,this.g=t.g,de(this,t.s),this.l=t.l;var e=t.i,r=new Gt;r.i=e.i,e.g&&(r.g=new Map(e.g),r.h=e.h),hr(this,r),this.m=t.m}else t&&(e=String(t).match(lr))?(this.h=!1,he(this,e[1]||"",!0),this.o=$t(e[2]||""),this.g=$t(e[3]||"",!0),de(this,e[4]),this.l=$t(e[5]||"",!0),hr(this,e[6]||"",!0),this.m=$t(e[7]||"")):(this.h=!1,this.i=new Gt(null,this.h))}pt.prototype.toString=function(){var t=[],e=this.j;e&&t.push(jt(e,dr,!0),":");var r=this.g;return(r||e=="file")&&(t.push("//"),(e=this.o)&&t.push(jt(e,dr,!0),"@"),t.push(encodeURIComponent(String(r)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),r=this.s,r!=null&&t.push(":",String(r))),(r=this.l)&&(this.g&&r.charAt(0)!="/"&&t.push("/"),t.push(jt(r,r.charAt(0)=="/"?vo:_o,!0))),(r=this.i.toString())&&t.push("?",r),(r=this.m)&&t.push("#",jt(r,To)),t.join("")};function nt(t){return new pt(t)}function he(t,e,r){t.j=r?$t(e,!0):e,t.j&&(t.j=t.j.replace(/:$/,""))}function de(t,e){if(e){if(e=Number(e),isNaN(e)||0>e)throw Error("Bad port number "+e);t.s=e}else t.s=null}function hr(t,e,r){e instanceof Gt?(t.i=e,Io(t.i,t.h)):(r||(e=jt(e,wo)),t.i=new Gt(e,t.h))}function S(t,e,r){t.i.set(e,r)}function fe(t){return S(t,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),t}function $t(t,e){return t?e?decodeURI(t.replace(/%25/g,"%2525")):decodeURIComponent(t):""}function jt(t,e,r){return typeof t=="string"?(t=encodeURI(t).replace(e,yo),r&&(t=t.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),t):null}function yo(t){return t=t.charCodeAt(0),"%"+(t>>4&15).toString(16)+(t&15).toString(16)}var dr=/[#\/\?@]/g,_o=/[#\?:]/g,vo=/[#\?]/g,wo=/[#\?@]/g,To=/#/g;function Gt(t,e){this.h=this.g=null,this.i=t||null,this.j=!!e}function ut(t){t.g||(t.g=new Map,t.h=0,t.i&&po(t.i,function(e,r){t.add(decodeURIComponent(e.replace(/\+/g," ")),r)}))}o=Gt.prototype,o.add=function(t,e){ut(this),this.i=null,t=Et(this,t);var r=this.g.get(t);return r||this.g.set(t,r=[]),r.push(e),this.h+=1,this};function fr(t,e){ut(t),e=Et(t,e),t.g.has(e)&&(t.i=null,t.h-=t.g.get(e).length,t.g.delete(e))}function mr(t,e){return ut(t),e=Et(t,e),t.g.has(e)}o.forEach=function(t,e){ut(this),this.g.forEach(function(r,s){r.forEach(function(m){t.call(e,m,s,this)},this)},this)},o.na=function(){ut(this);let t=Array.from(this.g.values()),e=Array.from(this.g.keys()),r=[];for(let s=0;s<e.length;s++){let m=t[s];for(let y=0;y<m.length;y++)r.push(e[s])}return r},o.V=function(t){ut(this);let e=[];if(typeof t=="string")mr(this,t)&&(e=e.concat(this.g.get(Et(this,t))));else{t=Array.from(this.g.values());for(let r=0;r<t.length;r++)e=e.concat(t[r])}return e},o.set=function(t,e){return ut(this),this.i=null,t=Et(this,t),mr(this,t)&&(this.h-=this.g.get(t).length),this.g.set(t,[e]),this.h+=1,this},o.get=function(t,e){return t?(t=this.V(t),0<t.length?String(t[0]):e):e};function gr(t,e,r){fr(t,e),0<r.length&&(t.i=null,t.g.set(Et(t,e),M(r)),t.h+=r.length)}o.toString=function(){if(this.i)return this.i;if(!this.g)return"";let t=[],e=Array.from(this.g.keys());for(var r=0;r<e.length;r++){var s=e[r];let y=encodeURIComponent(String(s)),v=this.V(s);for(s=0;s<v.length;s++){var m=y;v[s]!==""&&(m+="="+encodeURIComponent(String(v[s]))),t.push(m)}}return this.i=t.join("&")};function Et(t,e){return e=String(e),t.j&&(e=e.toLowerCase()),e}function Io(t,e){e&&!t.j&&(ut(t),t.i=null,t.g.forEach(function(r,s){var m=s.toLowerCase();s!=m&&(fr(this,s),gr(this,m,r))},t)),t.j=e}function Eo(t,e){let r=new Ut;if(T.Image){let s=new Image;s.onload=ft(ct,r,"TestLoadImage: loaded",!0,e,s),s.onerror=ft(ct,r,"TestLoadImage: error",!1,e,s),s.onabort=ft(ct,r,"TestLoadImage: abort",!1,e,s),s.ontimeout=ft(ct,r,"TestLoadImage: timeout",!1,e,s),T.setTimeout(function(){s.ontimeout&&s.ontimeout()},1e4),s.src=t}else e(!1)}function Ao(t,e){let r=new Ut,s=new AbortController,m=setTimeout(()=>{s.abort(),ct(r,"TestPingServer: timeout",!1,e)},1e4);fetch(t,{signal:s.signal}).then(y=>{clearTimeout(m),y.ok?ct(r,"TestPingServer: ok",!0,e):ct(r,"TestPingServer: server error",!1,e)}).catch(()=>{clearTimeout(m),ct(r,"TestPingServer: error",!1,e)})}function ct(t,e,r,s,m){try{m&&(m.onload=null,m.onerror=null,m.onabort=null,m.ontimeout=null),s(r)}catch{}}function bo(){this.g=new oo}function So(t,e,r){let s=r||"";try{cr(t,function(m,y){let v=m;P(m)&&(v=We(m)),e.push(s+y+"="+encodeURIComponent(v))})}catch(m){throw e.push(s+"type="+encodeURIComponent("_badmap")),m}}function Kt(t){this.l=t.Ub||null,this.j=t.eb||!1}b(Kt,He),Kt.prototype.g=function(){return new me(this.l,this.j)},Kt.prototype.i=function(t){return function(){return t}}({});function me(t,e){O.call(this),this.D=t,this.o=e,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}b(me,O),o=me.prototype,o.open=function(t,e){if(this.readyState!=0)throw this.abort(),Error("Error reopening a connection");this.B=t,this.A=e,this.readyState=1,Wt(this)},o.send=function(t){if(this.readyState!=1)throw this.abort(),Error("need to call open() first. ");this.g=!0;let e={headers:this.u,method:this.B,credentials:this.m,cache:void 0};t&&(e.body=t),(this.D||T).fetch(new Request(this.A,e)).then(this.Sa.bind(this),this.ga.bind(this))},o.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch(()=>{}),1<=this.readyState&&this.g&&this.readyState!=4&&(this.g=!1,Qt(this)),this.readyState=0},o.Sa=function(t){if(this.g&&(this.l=t,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=t.headers,this.readyState=2,Wt(this)),this.g&&(this.readyState=3,Wt(this),this.g)))if(this.responseType==="arraybuffer")t.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(typeof T.ReadableStream<"u"&&"body"in t){if(this.j=t.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;pr(this)}else t.text().then(this.Ra.bind(this),this.ga.bind(this))};function pr(t){t.j.read().then(t.Pa.bind(t)).catch(t.ga.bind(t))}o.Pa=function(t){if(this.g){if(this.o&&t.value)this.response.push(t.value);else if(!this.o){var e=t.value?t.value:new Uint8Array(0);(e=this.v.decode(e,{stream:!t.done}))&&(this.response=this.responseText+=e)}t.done?Qt(this):Wt(this),this.readyState==3&&pr(this)}},o.Ra=function(t){this.g&&(this.response=this.responseText=t,Qt(this))},o.Qa=function(t){this.g&&(this.response=t,Qt(this))},o.ga=function(){this.g&&Qt(this)};function Qt(t){t.readyState=4,t.l=null,t.j=null,t.v=null,Wt(t)}o.setRequestHeader=function(t,e){this.u.append(t,e)},o.getResponseHeader=function(t){return this.h&&this.h.get(t.toLowerCase())||""},o.getAllResponseHeaders=function(){if(!this.h)return"";let t=[],e=this.h.entries();for(var r=e.next();!r.done;)r=r.value,t.push(r[0]+": "+r[1]),r=e.next();return t.join(`\r
`)};function Wt(t){t.onreadystatechange&&t.onreadystatechange.call(t)}Object.defineProperty(me.prototype,"withCredentials",{get:function(){return this.m==="include"},set:function(t){this.m=t?"include":"same-origin"}});function yr(t){let e="";return X(t,function(r,s){e+=s,e+=":",e+=r,e+=`\r
`}),e}function on(t,e,r){t:{for(s in r){var s=!1;break t}s=!0}s||(r=yr(r),typeof t=="string"?r!=null&&encodeURIComponent(String(r)):S(t,e,r))}function x(t){O.call(this),this.headers=new Map,this.o=t||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}b(x,O);var Ro=/^https?$/i,Po=["POST","PUT"];o=x.prototype,o.Ha=function(t){this.J=t},o.ea=function(t,e,r,s){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+t);e=e?e.toUpperCase():"GET",this.D=t,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=this.o?this.o.g():Je.g(),this.v=this.o?Kn(this.o):Kn(Je),this.g.onreadystatechange=C(this.Ea,this);try{this.B=!0,this.g.open(e,String(t),!0),this.B=!1}catch(y){_r(this,y);return}if(t=r||"",r=new Map(this.headers),s)if(Object.getPrototypeOf(s)===Object.prototype)for(var m in s)r.set(m,s[m]);else if(typeof s.keys=="function"&&typeof s.get=="function")for(let y of s.keys())r.set(y,s.get(y));else throw Error("Unknown input type for opt_headers: "+String(s));s=Array.from(r.keys()).find(y=>y.toLowerCase()=="content-type"),m=T.FormData&&t instanceof T.FormData,!(0<=Array.prototype.indexOf.call(Po,e,void 0))||s||m||r.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");for(let[y,v]of r)this.g.setRequestHeader(y,v);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{Tr(this),this.u=!0,this.g.send(t),this.u=!1}catch(y){_r(this,y)}};function _r(t,e){t.h=!1,t.g&&(t.j=!0,t.g.abort(),t.j=!1),t.l=e,t.m=5,vr(t),ge(t)}function vr(t){t.A||(t.A=!0,B(t,"complete"),B(t,"error"))}o.abort=function(t){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=t||7,B(this,"complete"),B(this,"abort"),ge(this))},o.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),ge(this,!0)),x.aa.N.call(this)},o.Ea=function(){this.s||(this.B||this.u||this.j?wr(this):this.bb())},o.bb=function(){wr(this)};function wr(t){if(t.h&&typeof w<"u"&&(!t.v[1]||rt(t)!=4||t.Z()!=2)){if(t.u&&rt(t)==4)zn(t.Ea,0,t);else if(B(t,"readystatechange"),rt(t)==4){t.h=!1;try{let v=t.Z();t:switch(v){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var e=!0;break t;default:e=!1}var r;if(!(r=e)){var s;if(s=v===0){var m=String(t.D).match(lr)[1]||null;!m&&T.self&&T.self.location&&(m=T.self.location.protocol.slice(0,-1)),s=!Ro.test(m?m.toLowerCase():"")}r=s}if(r)B(t,"complete"),B(t,"success");else{t.m=6;try{var y=2<rt(t)?t.g.statusText:""}catch{y=""}t.l=y+" ["+t.Z()+"]",vr(t)}}finally{ge(t)}}}}function ge(t,e){if(t.g){Tr(t);let r=t.g,s=t.v[0]?()=>{}:null;t.g=null,t.v=null,e||B(t,"ready");try{r.onreadystatechange=s}catch{}}}function Tr(t){t.I&&(T.clearTimeout(t.I),t.I=null)}o.isActive=function(){return!!this.g};function rt(t){return t.g?t.g.readyState:0}o.Z=function(){try{return 2<rt(this)?this.g.status:-1}catch{return-1}},o.oa=function(){try{return this.g?this.g.responseText:""}catch{return""}},o.Oa=function(t){if(this.g){var e=this.g.responseText;return t&&e.indexOf(t)==0&&(e=e.substring(t.length)),so(e)}};function Ir(t){try{if(!t.g)return null;if("response"in t.g)return t.g.response;switch(t.H){case"":case"text":return t.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in t.g)return t.g.mozResponseArrayBuffer}return null}catch{return null}}function xo(t){let e={};t=(t.g&&2<=rt(t)&&t.g.getAllResponseHeaders()||"").split(`\r
`);for(let s=0;s<t.length;s++){if(Y(t[s]))continue;var r=f(t[s]);let m=r[0];if(r=r[1],typeof r!="string")continue;r=r.trim();let y=e[m]||[];e[m]=y,y.push(r)}g(e,function(s){return s.join(", ")})}o.Ba=function(){return this.m},o.Ka=function(){return typeof this.l=="string"?this.l:String(this.l)};function Ht(t,e,r){return r&&r.internalChannelParams&&r.internalChannelParams[t]||e}function Er(t){this.Aa=0,this.i=[],this.j=new Ut,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=Ht("failFast",!1,t),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=Ht("baseRetryDelayMs",5e3,t),this.cb=Ht("retryDelaySeedMs",1e4,t),this.Wa=Ht("forwardChannelMaxRetries",2,t),this.wa=Ht("forwardChannelRequestTimeoutMs",2e4,t),this.pa=t&&t.xmlHttpFactory||void 0,this.Xa=t&&t.Tb||void 0,this.Ca=t&&t.useFetchStreams||!1,this.L=void 0,this.J=t&&t.supportsCrossDomainXhr||!1,this.K="",this.h=new ir(t&&t.concurrentRequestLimit),this.Da=new bo,this.P=t&&t.fastHandshake||!1,this.O=t&&t.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=t&&t.Rb||!1,t&&t.xa&&this.j.xa(),t&&t.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&t&&t.detectBufferingProxy||!1,this.ja=void 0,t&&t.longPollingTimeout&&0<t.longPollingTimeout&&(this.ja=t.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}o=Er.prototype,o.la=8,o.G=1,o.connect=function(t,e,r,s){U(0),this.W=t,this.H=e||{},r&&s!==void 0&&(this.H.OSID=r,this.H.OAID=s),this.F=this.X,this.I=Dr(this,null,this.W),ye(this)};function an(t){if(Ar(t),t.G==3){var e=t.U++,r=nt(t.I);if(S(r,"SID",t.K),S(r,"RID",e),S(r,"TYPE","terminate"),Yt(t,r),e=new at(t,t.j,e),e.L=2,e.v=fe(nt(r)),r=!1,T.navigator&&T.navigator.sendBeacon)try{r=T.navigator.sendBeacon(e.v.toString(),"")}catch{}!r&&T.Image&&(new Image().src=e.v,r=!0),r||(e.g=Nr(e.j,null),e.g.ea(e.v)),e.F=Date.now(),le(e)}Vr(t)}function pe(t){t.g&&(cn(t),t.g.cancel(),t.g=null)}function Ar(t){pe(t),t.u&&(T.clearTimeout(t.u),t.u=null),_e(t),t.h.cancel(),t.s&&(typeof t.s=="number"&&T.clearTimeout(t.s),t.s=null)}function ye(t){if(!sr(t.h)&&!t.s){t.s=!0;var e=t.Ga;Nt||Fn(),kt||(Nt(),kt=!0),qe.add(e,t),t.B=0}}function Co(t,e){return or(t.h)>=t.h.j-(t.s?1:0)?!1:t.s?(t.i=e.D.concat(t.i),!0):t.G==1||t.G==2||t.B>=(t.Va?0:t.Wa)?!1:(t.s=Bt(C(t.Ga,t,e),Cr(t,t.B)),t.B++,!0)}o.Ga=function(t){if(this.s)if(this.s=null,this.G==1){if(!t){this.U=Math.floor(1e5*Math.random()),t=this.U++;let m=new at(this,this.j,t),y=this.o;if(this.S&&(y?(y=u(y),h(y,this.S)):y=this.S),this.m!==null||this.O||(m.H=y,y=null),this.P)t:{for(var e=0,r=0;r<this.i.length;r++){e:{var s=this.i[r];if("__data__"in s.map&&(s=s.map.__data__,typeof s=="string")){s=s.length;break e}s=void 0}if(s===void 0)break;if(e+=s,4096<e){e=r;break t}if(e===4096||r===this.i.length-1){e=r+1;break t}}e=1e3}else e=1e3;e=Sr(this,m,e),r=nt(this.I),S(r,"RID",t),S(r,"CVER",22),this.D&&S(r,"X-HTTP-Session-Id",this.D),Yt(this,r),y&&(this.O?e="headers="+encodeURIComponent(String(yr(y)))+"&"+e:this.m&&on(r,this.m,y)),sn(this.h,m),this.Ua&&S(r,"TYPE","init"),this.P?(S(r,"$req",e),S(r,"SID","null"),m.T=!0,tn(m,r,null)):tn(m,r,e),this.G=2}}else this.G==3&&(t?br(this,t):this.i.length==0||sr(this.h)||br(this))};function br(t,e){var r;e?r=e.l:r=t.U++;let s=nt(t.I);S(s,"SID",t.K),S(s,"RID",r),S(s,"AID",t.T),Yt(t,s),t.m&&t.o&&on(s,t.m,t.o),r=new at(t,t.j,r,t.B+1),t.m===null&&(r.H=t.o),e&&(t.i=e.D.concat(t.i)),e=Sr(t,r,1e3),r.I=Math.round(.5*t.wa)+Math.round(.5*t.wa*Math.random()),sn(t.h,r),tn(r,s,e)}function Yt(t,e){t.H&&X(t.H,function(r,s){S(e,s,r)}),t.l&&cr({},function(r,s){S(e,s,r)})}function Sr(t,e,r){r=Math.min(t.i.length,r);var s=t.l?C(t.l.Na,t.l,t):null;t:{var m=t.i;let y=-1;for(;;){let v=["count="+r];y==-1?0<r?(y=m[0].g,v.push("ofs="+y)):y=0:v.push("ofs="+y);let A=!0;for(let D=0;D<r;D++){let E=m[D].g,L=m[D].map;if(E-=y,0>E)y=Math.max(0,m[D].g-100),A=!1;else try{So(L,v,"req"+E+"_")}catch{s&&s(L)}}if(A){s=v.join("&");break t}}}return t=t.i.splice(0,r),e.D=t,s}function Rr(t){if(!t.g&&!t.u){t.Y=1;var e=t.Fa;Nt||Fn(),kt||(Nt(),kt=!0),qe.add(e,t),t.v=0}}function un(t){return t.g||t.u||3<=t.v?!1:(t.Y++,t.u=Bt(C(t.Fa,t),Cr(t,t.v)),t.v++,!0)}o.Fa=function(){if(this.u=null,Pr(this),this.ba&&!(this.M||this.g==null||0>=this.R)){var t=2*this.R;this.j.info("BP detection timer enabled: "+t),this.A=Bt(C(this.ab,this),t)}},o.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,U(10),pe(this),Pr(this))};function cn(t){t.A!=null&&(T.clearTimeout(t.A),t.A=null)}function Pr(t){t.g=new at(t,t.j,"rpc",t.Y),t.m===null&&(t.g.H=t.o),t.g.O=0;var e=nt(t.qa);S(e,"RID","rpc"),S(e,"SID",t.K),S(e,"AID",t.T),S(e,"CI",t.F?"0":"1"),!t.F&&t.ja&&S(e,"TO",t.ja),S(e,"TYPE","xmlhttp"),Yt(t,e),t.m&&t.o&&on(e,t.m,t.o),t.L&&(t.g.I=t.L);var r=t.g;t=t.ia,r.L=1,r.v=fe(nt(e)),r.m=null,r.P=!0,er(r,t)}o.Za=function(){this.C!=null&&(this.C=null,pe(this),un(this),U(19))};function _e(t){t.C!=null&&(T.clearTimeout(t.C),t.C=null)}function xr(t,e){var r=null;if(t.g==e){_e(t),cn(t),t.g=null;var s=2}else if(rn(t.h,e))r=e.D,ar(t.h,e),s=1;else return;if(t.G!=0){if(e.o)if(s==1){r=e.m?e.m.length:0,e=Date.now()-e.F;var m=t.B;s=ae(),B(s,new Xn(s,r)),ye(t)}else Rr(t);else if(m=e.s,m==3||m==0&&0<e.X||!(s==1&&Co(t,e)||s==2&&un(t)))switch(r&&0<r.length&&(e=t.h,e.i=e.i.concat(r)),m){case 1:yt(t,5);break;case 4:yt(t,10);break;case 3:yt(t,6);break;default:yt(t,2)}}}function Cr(t,e){let r=t.Ta+Math.floor(Math.random()*t.cb);return t.isActive()||(r*=2),r*e}function yt(t,e){if(t.j.info("Error code "+e),e==2){var r=C(t.fb,t),s=t.Xa;let m=!s;s=new pt(s||"//www.google.com/images/cleardot.gif"),T.location&&T.location.protocol=="http"||he(s,"https"),fe(s),m?Eo(s.toString(),r):Ao(s.toString(),r)}else U(2);t.G=0,t.l&&t.l.sa(e),Vr(t),Ar(t)}o.fb=function(t){t?(this.j.info("Successfully pinged google.com"),U(2)):(this.j.info("Failed to ping google.com"),U(1))};function Vr(t){if(t.G=0,t.ka=[],t.l){let e=ur(t.h);(e.length!=0||t.i.length!=0)&&(V(t.ka,e),V(t.ka,t.i),t.h.i.length=0,M(t.i),t.i.length=0),t.l.ra()}}function Dr(t,e,r){var s=r instanceof pt?nt(r):new pt(r);if(s.g!="")e&&(s.g=e+"."+s.g),de(s,s.s);else{var m=T.location;s=m.protocol,e=e?e+"."+m.hostname:m.hostname,m=+m.port;var y=new pt(null);s&&he(y,s),e&&(y.g=e),m&&de(y,m),r&&(y.l=r),s=y}return r=t.D,e=t.ya,r&&e&&S(s,r,e),S(s,"VER",t.la),Yt(t,s),s}function Nr(t,e,r){if(e&&!t.J)throw Error("Can't create secondary domain capable XhrIo object.");return e=t.Ca&&!t.pa?new x(new Kt({eb:r})):new x(t.pa),e.Ha(t.J),e}o.isActive=function(){return!!this.l&&this.l.isActive(this)};function kr(){}o=kr.prototype,o.ua=function(){},o.ta=function(){},o.sa=function(){},o.ra=function(){},o.isActive=function(){return!0},o.Na=function(){};function ve(){}ve.prototype.g=function(t,e){return new $(t,e)};function $(t,e){O.call(this),this.g=new Er(e),this.l=t,this.h=e&&e.messageUrlParams||null,t=e&&e.messageHeaders||null,e&&e.clientProtocolHeaderRequired&&(t?t["X-Client-Protocol"]="webchannel":t={"X-Client-Protocol":"webchannel"}),this.g.o=t,t=e&&e.initMessageHeaders||null,e&&e.messageContentType&&(t?t["X-WebChannel-Content-Type"]=e.messageContentType:t={"X-WebChannel-Content-Type":e.messageContentType}),e&&e.va&&(t?t["X-WebChannel-Client-Profile"]=e.va:t={"X-WebChannel-Client-Profile":e.va}),this.g.S=t,(t=e&&e.Sb)&&!Y(t)&&(this.g.m=t),this.v=e&&e.supportsCrossDomainXhr||!1,this.u=e&&e.sendRawJson||!1,(e=e&&e.httpSessionIdParam)&&!Y(e)&&(this.g.D=e,t=this.h,t!==null&&e in t&&(t=this.h,e in t&&delete t[e])),this.j=new At(this)}b($,O),$.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},$.prototype.close=function(){an(this.g)},$.prototype.o=function(t){var e=this.g;if(typeof t=="string"){var r={};r.__data__=t,t=r}else this.u&&(r={},r.__data__=We(t),t=r);e.i.push(new fo(e.Ya++,t)),e.G==3&&ye(e)},$.prototype.N=function(){this.g.l=null,delete this.j,an(this.g),delete this.g,$.aa.N.call(this)};function Mr(t){Ye.call(this),t.__headers__&&(this.headers=t.__headers__,this.statusCode=t.__status__,delete t.__headers__,delete t.__status__);var e=t.__sm__;if(e){t:{for(let r in e){t=r;break t}t=void 0}(this.i=t)&&(t=this.i,e=e!==null&&t in e?e[t]:void 0),this.data=e}else this.data=t}b(Mr,Ye);function Fr(){Xe.call(this),this.status=1}b(Fr,Xe);function At(t){this.g=t}b(At,kr),At.prototype.ua=function(){B(this.g,"a")},At.prototype.ta=function(t){B(this.g,new Mr(t))},At.prototype.sa=function(t){B(this.g,new Fr)},At.prototype.ra=function(){B(this.g,"b")},ve.prototype.createWebChannel=ve.prototype.g,$.prototype.send=$.prototype.o,$.prototype.open=$.prototype.m,$.prototype.close=$.prototype.close,cs=it.createWebChannelTransport=function(){return new ve},us=it.getStatEventTarget=function(){return ae()},as=it.Event=mt,os=it.Stat={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},ue.NO_ERROR=0,ue.TIMEOUT=8,ue.HTTP_ERROR=6,ss=it.ErrorCode=ue,Jn.COMPLETE="complete",is=it.EventType=Jn,Qn.EventType=Lt,Lt.OPEN="a",Lt.CLOSE="b",Lt.ERROR="c",Lt.MESSAGE="d",O.prototype.listen=O.prototype.K,rs=it.WebChannel=Qn,ko=it.FetchXmlHttpFactory=Kt,x.prototype.listenOnce=x.prototype.L,x.prototype.getLastError=x.prototype.Ka,x.prototype.getLastErrorCode=x.prototype.Ba,x.prototype.getStatus=x.prototype.Z,x.prototype.getResponseJson=x.prototype.Oa,x.prototype.getResponseText=x.prototype.oa,x.prototype.send=x.prototype.ea,x.prototype.setWithCredentials=x.prototype.Ha,ns=it.XhrIo=x}).apply(typeof xe<"u"?xe:typeof self<"u"?self:typeof window<"u"?window:{});var ls="@firebase/firestore",hs="4.7.17";var N=class{constructor(n){this.uid=n}isAuthenticated(){return this.uid!=null}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(n){return n.uid===this.uid}};N.UNAUTHENTICATED=new N(null),N.GOOGLE_CREDENTIALS=new N("google-credentials-uid"),N.FIRST_PARTY=new N("first-party-uid"),N.MOCK_USER=new N("mock-user");var ee="11.9.0";var xt=new wi("@firebase/firestore");function W(o,...n){if(xt.logLevel<=Re.DEBUG){let i=n.map(Nn);xt.debug(`Firestore (${ee}): ${o}`,...i)}}function ws(o,...n){if(xt.logLevel<=Re.ERROR){let i=n.map(Nn);xt.error(`Firestore (${ee}): ${o}`,...i)}}function Mo(o,...n){if(xt.logLevel<=Re.WARN){let i=n.map(Nn);xt.warn(`Firestore (${ee}): ${o}`,...i)}}function Nn(o){if(typeof o=="string")return o;try{return function(i){return JSON.stringify(i)}(o)}catch{return o}}function j(o,n,i){let a="Unexpected state";typeof n=="string"?a=n:i=n,Ts(o,a,i)}function Ts(o,n,i){let a=`FIRESTORE (${ee}) INTERNAL ASSERTION FAILED: ${n} (ID: ${o.toString(16)})`;if(i!==void 0)try{a+=" CONTEXT: "+JSON.stringify(i)}catch{a+=" CONTEXT: "+i}throw ws(a),new Error(a)}function _t(o,n,i,a){let d="Unexpected state";typeof i=="string"?d=i:a=i,o||Ts(n,d,a)}var z={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"},k=class extends yi{constructor(n,i){super(n,i),this.code=n,this.message=i,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}};var vt=class{constructor(){this.promise=new Promise((n,i)=>{this.resolve=n,this.reject=i})}};var Ce=class{constructor(n,i){this.user=i,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization",`Bearer ${n}`)}},vn=class{getToken(){return Promise.resolve(null)}invalidateToken(){}start(n,i){n.enqueueRetryable(()=>i(N.UNAUTHENTICATED))}shutdown(){}},wn=class{constructor(n){this.token=n,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(n,i){this.changeListener=i,n.enqueueRetryable(()=>i(this.token.user))}shutdown(){this.changeListener=null}},Tn=class{constructor(n){this.t=n,this.currentUser=N.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(n,i){_t(this.o===void 0,42304);let a=this.i,d=R=>this.i!==a?(a=this.i,i(R)):Promise.resolve(),_=new vt;this.o=()=>{this.i++,this.currentUser=this.u(),_.resolve(),_=new vt,n.enqueueRetryable(()=>d(this.currentUser))};let w=()=>{let R=_;n.enqueueRetryable(()=>lt(this,null,function*(){yield R.promise,yield d(this.currentUser)}))},T=R=>{W("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=R,this.o&&(this.auth.addAuthTokenListener(this.o),w())};this.t.onInit(R=>T(R)),setTimeout(()=>{if(!this.auth){let R=this.t.getImmediate({optional:!0});R?T(R):(W("FirebaseAuthCredentialsProvider","Auth not yet detected"),_.resolve(),_=new vt)}},0),w()}getToken(){let n=this.i,i=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(i).then(a=>this.i!==n?(W("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):a?(_t(typeof a.accessToken=="string",31837,{l:a}),new Ce(a.accessToken,this.currentUser)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){let n=this.auth&&this.auth.getUid();return _t(n===null||typeof n=="string",2055,{h:n}),new N(n)}},In=class{constructor(n,i,a){this.P=n,this.T=i,this.I=a,this.type="FirstParty",this.user=N.FIRST_PARTY,this.A=new Map}R(){return this.I?this.I():null}get headers(){this.A.set("X-Goog-AuthUser",this.P);let n=this.R();return n&&this.A.set("Authorization",n),this.T&&this.A.set("X-Goog-Iam-Authorization-Token",this.T),this.A}},En=class{constructor(n,i,a){this.P=n,this.T=i,this.I=a}getToken(){return Promise.resolve(new In(this.P,this.T,this.I))}start(n,i){n.enqueueRetryable(()=>i(N.FIRST_PARTY))}shutdown(){}invalidateToken(){}},Ve=class{constructor(n){this.value=n,this.type="AppCheck",this.headers=new Map,n&&n.length>0&&this.headers.set("x-firebase-appcheck",this.value)}},An=class{constructor(n,i){this.V=i,this.forceRefresh=!1,this.appCheck=null,this.m=null,this.p=null,Ei(n)&&n.settings.appCheckToken&&(this.p=n.settings.appCheckToken)}start(n,i){_t(this.o===void 0,3512);let a=_=>{_.error!=null&&W("FirebaseAppCheckTokenProvider",`Error getting App Check token; using placeholder token instead. Error: ${_.error.message}`);let w=_.token!==this.m;return this.m=_.token,W("FirebaseAppCheckTokenProvider",`Received ${w?"new":"existing"} token.`),w?i(_.token):Promise.resolve()};this.o=_=>{n.enqueueRetryable(()=>a(_))};let d=_=>{W("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=_,this.o&&this.appCheck.addTokenListener(this.o)};this.V.onInit(_=>d(_)),setTimeout(()=>{if(!this.appCheck){let _=this.V.getImmediate({optional:!0});_?d(_):W("FirebaseAppCheckTokenProvider","AppCheck not yet detected")}},0)}getToken(){if(this.p)return Promise.resolve(new Ve(this.p));let n=this.forceRefresh;return this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(n).then(i=>i?(_t(typeof i.token=="string",44558,{tokenResult:i}),this.m=i.token,new Ve(i.token)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}};function Fo(){return new TextEncoder}function dt(o,n){return o<n?-1:o>n?1:0}function Oo(o,n){let i=0;for(;i<o.length&&i<n.length;){let a=o.codePointAt(i),d=n.codePointAt(i);if(a!==d){if(a<128&&d<128)return dt(a,d);{let _=Fo(),w=Lo(_.encode(ds(o,i)),_.encode(ds(n,i)));return w!==0?w:dt(a,d)}}i+=a>65535?2:1}return dt(o.length,n.length)}function ds(o,n){return o.codePointAt(n)>65535?o.substring(n,n+2):o.substring(n,n+1)}function Lo(o,n){for(let i=0;i<o.length&&i<n.length;++i)if(o[i]!==n[i])return dt(o[i],n[i]);return dt(o.length,n.length)}var bn=class o{constructor(n,i,a){i===void 0?i=0:i>n.length&&j(637,{offset:i,range:n.length}),a===void 0?a=n.length-i:a>n.length-i&&j(1746,{length:a,range:n.length-i}),this.segments=n,this.offset=i,this.len=a}get length(){return this.len}isEqual(n){return o.comparator(this,n)===0}child(n){let i=this.segments.slice(this.offset,this.limit());return n instanceof o?n.forEach(a=>{i.push(a)}):i.push(n),this.construct(i)}limit(){return this.offset+this.length}popFirst(n){return n=n===void 0?1:n,this.construct(this.segments,this.offset+n,this.length-n)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(n){return this.segments[this.offset+n]}isEmpty(){return this.length===0}isPrefixOf(n){if(n.length<this.length)return!1;for(let i=0;i<this.length;i++)if(this.get(i)!==n.get(i))return!1;return!0}isImmediateParentOf(n){if(this.length+1!==n.length)return!1;for(let i=0;i<this.length;i++)if(this.get(i)!==n.get(i))return!1;return!0}forEach(n){for(let i=this.offset,a=this.limit();i<a;i++)n(this.segments[i])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(n,i){let a=Math.min(n.length,i.length);for(let d=0;d<a;d++){let _=o.compareSegments(n.get(d),i.get(d));if(_!==0)return _}return dt(n.length,i.length)}static compareSegments(n,i){let a=o.isNumericId(n),d=o.isNumericId(i);return a&&!d?-1:!a&&d?1:a&&d?o.extractNumericId(n).compare(o.extractNumericId(i)):Oo(n,i)}static isNumericId(n){return n.startsWith("__id")&&n.endsWith("__")}static extractNumericId(n){return Pe.fromString(n.substring(4,n.length-2))}},ht=class o extends bn{construct(n,i,a){return new o(n,i,a)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...n){let i=[];for(let a of n){if(a.indexOf("//")>=0)throw new k(z.INVALID_ARGUMENT,`Invalid segment (${a}). Paths must not contain // in them.`);i.push(...a.split("/").filter(d=>d.length>0))}return new o(i)}static emptyPath(){return new o([])}};var wt=class o{constructor(n){this.path=n}static fromPath(n){return new o(ht.fromString(n))}static fromName(n){return new o(ht.fromString(n).popFirst(5))}static empty(){return new o(ht.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(n){return this.path.length>=2&&this.path.get(this.path.length-2)===n}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(n){return n!==null&&ht.comparator(this.path,n.path)===0}toString(){return this.path.toString()}static comparator(n,i){return ht.comparator(n.path,i.path)}static isDocumentKey(n){return n.length%2==0}static fromSegments(n){return new o(new ht(n.slice()))}};var Sn=class{constructor(n,i,a,d){this.indexId=n,this.collectionGroup=i,this.fields=a,this.indexState=d}};Sn.UNKNOWN_ID=-1;function qo(o){return o.name==="IndexedDbTransactionError"}function Bo(o){return o===0&&1/o==-1/0}var Uo="remoteDocuments",Is="owner";var Es="mutationQueues";var As="mutations";var bs="documentMutations",zo="remoteDocumentsV14";var Ss="remoteDocumentGlobal";var Rs="targets";var Ps="targetDocuments";var xs="targetGlobal",Cs="collectionParents";var Vs="clientMetadata";var Ds="bundles";var Ns="namedQueries";var $o="indexConfiguration";var jo="indexState";var Go="indexEntries";var ks="documentOverlays";var Ko="globals";var Qo=[Es,As,bs,Uo,Rs,Is,xs,Ps,Vs,Ss,Cs,Ds,Ns],Ua=[...Qo,ks],Wo=[Es,As,bs,zo,Rs,Is,xs,Ps,Vs,Ss,Cs,Ds,Ns,ks],Ho=Wo,Yo=[...Ho,$o,jo,Go];var za=[...Yo,Ko];var Ct=class o{constructor(n,i){this.comparator=n,this.root=i||tt.EMPTY}insert(n,i){return new o(this.comparator,this.root.insert(n,i,this.comparator).copy(null,null,tt.BLACK,null,null))}remove(n){return new o(this.comparator,this.root.remove(n,this.comparator).copy(null,null,tt.BLACK,null,null))}get(n){let i=this.root;for(;!i.isEmpty();){let a=this.comparator(n,i.key);if(a===0)return i.value;a<0?i=i.left:a>0&&(i=i.right)}return null}indexOf(n){let i=0,a=this.root;for(;!a.isEmpty();){let d=this.comparator(n,a.key);if(d===0)return i+a.left.size;d<0?a=a.left:(i+=a.left.size+1,a=a.right)}return-1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(n){return this.root.inorderTraversal(n)}forEach(n){this.inorderTraversal((i,a)=>(n(i,a),!1))}toString(){let n=[];return this.inorderTraversal((i,a)=>(n.push(`${i}:${a}`),!1)),`{${n.join(", ")}}`}reverseTraversal(n){return this.root.reverseTraversal(n)}getIterator(){return new Pt(this.root,null,this.comparator,!1)}getIteratorFrom(n){return new Pt(this.root,n,this.comparator,!1)}getReverseIterator(){return new Pt(this.root,null,this.comparator,!0)}getReverseIteratorFrom(n){return new Pt(this.root,n,this.comparator,!0)}},Pt=class{constructor(n,i,a,d){this.isReverse=d,this.nodeStack=[];let _=1;for(;!n.isEmpty();)if(_=i?a(n.key,i):1,i&&d&&(_*=-1),_<0)n=this.isReverse?n.left:n.right;else{if(_===0){this.nodeStack.push(n);break}this.nodeStack.push(n),n=this.isReverse?n.right:n.left}}getNext(){let n=this.nodeStack.pop(),i={key:n.key,value:n.value};if(this.isReverse)for(n=n.left;!n.isEmpty();)this.nodeStack.push(n),n=n.right;else for(n=n.right;!n.isEmpty();)this.nodeStack.push(n),n=n.left;return i}hasNext(){return this.nodeStack.length>0}peek(){if(this.nodeStack.length===0)return null;let n=this.nodeStack[this.nodeStack.length-1];return{key:n.key,value:n.value}}},tt=class o{constructor(n,i,a,d,_){this.key=n,this.value=i,this.color=a??o.RED,this.left=d??o.EMPTY,this.right=_??o.EMPTY,this.size=this.left.size+1+this.right.size}copy(n,i,a,d,_){return new o(n??this.key,i??this.value,a??this.color,d??this.left,_??this.right)}isEmpty(){return!1}inorderTraversal(n){return this.left.inorderTraversal(n)||n(this.key,this.value)||this.right.inorderTraversal(n)}reverseTraversal(n){return this.right.reverseTraversal(n)||n(this.key,this.value)||this.left.reverseTraversal(n)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(n,i,a){let d=this,_=a(n,d.key);return d=_<0?d.copy(null,null,null,d.left.insert(n,i,a),null):_===0?d.copy(null,i,null,null,null):d.copy(null,null,null,null,d.right.insert(n,i,a)),d.fixUp()}removeMin(){if(this.left.isEmpty())return o.EMPTY;let n=this;return n.left.isRed()||n.left.left.isRed()||(n=n.moveRedLeft()),n=n.copy(null,null,null,n.left.removeMin(),null),n.fixUp()}remove(n,i){let a,d=this;if(i(n,d.key)<0)d.left.isEmpty()||d.left.isRed()||d.left.left.isRed()||(d=d.moveRedLeft()),d=d.copy(null,null,null,d.left.remove(n,i),null);else{if(d.left.isRed()&&(d=d.rotateRight()),d.right.isEmpty()||d.right.isRed()||d.right.left.isRed()||(d=d.moveRedRight()),i(n,d.key)===0){if(d.right.isEmpty())return o.EMPTY;a=d.right.min(),d=d.copy(a.key,a.value,null,null,d.right.removeMin())}d=d.copy(null,null,null,null,d.right.remove(n,i))}return d.fixUp()}isRed(){return this.color}fixUp(){let n=this;return n.right.isRed()&&!n.left.isRed()&&(n=n.rotateLeft()),n.left.isRed()&&n.left.left.isRed()&&(n=n.rotateRight()),n.left.isRed()&&n.right.isRed()&&(n=n.colorFlip()),n}moveRedLeft(){let n=this.colorFlip();return n.right.left.isRed()&&(n=n.copy(null,null,null,null,n.right.rotateRight()),n=n.rotateLeft(),n=n.colorFlip()),n}moveRedRight(){let n=this.colorFlip();return n.left.left.isRed()&&(n=n.rotateRight(),n=n.colorFlip()),n}rotateLeft(){let n=this.copy(null,null,o.RED,null,this.right.left);return this.right.copy(null,null,this.color,n,null)}rotateRight(){let n=this.copy(null,null,o.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,n)}colorFlip(){let n=this.left.copy(null,null,!this.left.color,null,null),i=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,n,i)}checkMaxDepth(){let n=this.check();return Math.pow(2,n)<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw j(43730,{key:this.key,value:this.value});if(this.right.isRed())throw j(14113,{key:this.key,value:this.value});let n=this.left.check();if(n!==this.right.check())throw j(27949);return n+(this.isRed()?0:1)}};tt.EMPTY=null,tt.RED=!0,tt.BLACK=!1;tt.EMPTY=new class{constructor(){this.size=0}get key(){throw j(57766)}get value(){throw j(16141)}get color(){throw j(16727)}get left(){throw j(29726)}get right(){throw j(36894)}copy(n,i,a,d,_){return this}insert(n,i,a){return new tt(n,i)}remove(n,i){return this}isEmpty(){return!0}inorderTraversal(n){return!1}reverseTraversal(n){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};var De=class o{constructor(n){this.comparator=n,this.data=new Ct(this.comparator)}has(n){return this.data.get(n)!==null}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(n){return this.data.indexOf(n)}forEach(n){this.data.inorderTraversal((i,a)=>(n(i),!1))}forEachInRange(n,i){let a=this.data.getIteratorFrom(n[0]);for(;a.hasNext();){let d=a.getNext();if(this.comparator(d.key,n[1])>=0)return;i(d.key)}}forEachWhile(n,i){let a;for(a=i!==void 0?this.data.getIteratorFrom(i):this.data.getIterator();a.hasNext();)if(!n(a.getNext().key))return}firstAfterOrEqual(n){let i=this.data.getIteratorFrom(n);return i.hasNext()?i.getNext().key:null}getIterator(){return new Ne(this.data.getIterator())}getIteratorFrom(n){return new Ne(this.data.getIteratorFrom(n))}add(n){return this.copy(this.data.remove(n).insert(n,!0))}delete(n){return this.has(n)?this.copy(this.data.remove(n)):this}isEmpty(){return this.data.isEmpty()}unionWith(n){let i=this;return i.size<n.size&&(i=n,n=this),n.forEach(a=>{i=i.add(a)}),i}isEqual(n){if(!(n instanceof o)||this.size!==n.size)return!1;let i=this.data.getIterator(),a=n.data.getIterator();for(;i.hasNext();){let d=i.getNext().key,_=a.getNext().key;if(this.comparator(d,_)!==0)return!1}return!0}toArray(){let n=[];return this.forEach(i=>{n.push(i)}),n}toString(){let n=[];return this.forEach(i=>n.push(i)),"SortedSet("+n.toString()+")"}copy(n){let i=new o(this.comparator);return i.data=n,i}},Ne=class{constructor(n){this.iter=n}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}};var Rn=class extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}};var Vt=class o{constructor(n){this.binaryString=n}static fromBase64String(n){let i=function(d){try{return atob(d)}catch(_){throw typeof DOMException<"u"&&_ instanceof DOMException?new Rn("Invalid base64 string: "+_):_}}(n);return new o(i)}static fromUint8Array(n){let i=function(d){let _="";for(let w=0;w<d.length;++w)_+=String.fromCharCode(d[w]);return _}(n);return new o(i)}[Symbol.iterator](){let n=0;return{next:()=>n<this.binaryString.length?{value:this.binaryString.charCodeAt(n++),done:!1}:{value:void 0,done:!0}}}toBase64(){return function(i){return btoa(i)}(this.binaryString)}toUint8Array(){return function(i){let a=new Uint8Array(i.length);for(let d=0;d<i.length;d++)a[d]=i.charCodeAt(d);return a}(this.binaryString)}approximateByteSize(){return 2*this.binaryString.length}compareTo(n){return dt(this.binaryString,n.binaryString)}isEqual(n){return this.binaryString===n.binaryString}};Vt.EMPTY_BYTE_STRING=new Vt("");var Xo=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function Jo(o){if(_t(!!o,39018),typeof o=="string"){let n=0,i=Xo.exec(o);if(_t(!!i,46558,{timestamp:o}),i[1]){let d=i[1];d=(d+"000000000").substr(0,9),n=Number(d)}let a=new Date(o);return{seconds:Math.floor(a.getTime()/1e3),nanos:n}}return{seconds:te(o.seconds),nanos:te(o.nanos)}}function te(o){return typeof o=="number"?o:typeof o=="string"?Number(o):0}function Zo(o){return typeof o=="string"?Vt.fromBase64String(o):Vt.fromUint8Array(o)}var Pn="(default)",xn=class o{constructor(n,i){this.projectId=n,this.database=i||Pn}static empty(){return new o("","")}get isDefaultDatabase(){return this.database===Pn}isEqual(n){return n instanceof o&&n.projectId===this.projectId&&n.database===this.database}};var Ms="__type__",ta="__max__";var Fs="__vector__",Os="value";function ea(o){var n,i;return((i=(((n=o?.mapValue)===null||n===void 0?void 0:n.fields)||{})[Ms])===null||i===void 0?void 0:i.stringValue)===Fs}function na(o){return(((o.mapValue||{}).fields||{}).__type__||{}).stringValue===ta}var ja={mapValue:{fields:{[Ms]:{stringValue:Fs},[Os]:{arrayValue:{}}}}};var Ga=new Ct(wt.comparator);var Ka=new Ct(wt.comparator);var Qa=new Ct(wt.comparator),Wa=new De(wt.comparator);var Ha=new De(dt);var fs,I;(I=fs||(fs={}))[I.OK=0]="OK",I[I.CANCELLED=1]="CANCELLED",I[I.UNKNOWN=2]="UNKNOWN",I[I.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",I[I.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",I[I.NOT_FOUND=5]="NOT_FOUND",I[I.ALREADY_EXISTS=6]="ALREADY_EXISTS",I[I.PERMISSION_DENIED=7]="PERMISSION_DENIED",I[I.UNAUTHENTICATED=16]="UNAUTHENTICATED",I[I.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",I[I.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",I[I.ABORTED=10]="ABORTED",I[I.OUT_OF_RANGE=11]="OUT_OF_RANGE",I[I.UNIMPLEMENTED=12]="UNIMPLEMENTED",I[I.INTERNAL=13]="INTERNAL",I[I.UNAVAILABLE=14]="UNAVAILABLE",I[I.DATA_LOSS=15]="DATA_LOSS";var Ya=new Pe([4294967295,4294967295],0);var ke=class{constructor(){}vt(n,i){this.Ct(n,i),i.Ft()}Ct(n,i){if("nullValue"in n)this.Mt(i,5);else if("booleanValue"in n)this.Mt(i,10),i.xt(n.booleanValue?1:0);else if("integerValue"in n)this.Mt(i,15),i.xt(te(n.integerValue));else if("doubleValue"in n){let a=te(n.doubleValue);isNaN(a)?this.Mt(i,13):(this.Mt(i,15),Bo(a)?i.xt(0):i.xt(a))}else if("timestampValue"in n){let a=n.timestampValue;this.Mt(i,20),typeof a=="string"&&(a=Jo(a)),i.Ot(`${a.seconds||""}`),i.xt(a.nanos||0)}else if("stringValue"in n)this.Nt(n.stringValue,i),this.Bt(i);else if("bytesValue"in n)this.Mt(i,30),i.Lt(Zo(n.bytesValue)),this.Bt(i);else if("referenceValue"in n)this.kt(n.referenceValue,i);else if("geoPointValue"in n){let a=n.geoPointValue;this.Mt(i,45),i.xt(a.latitude||0),i.xt(a.longitude||0)}else"mapValue"in n?na(n)?this.Mt(i,Number.MAX_SAFE_INTEGER):ea(n)?this.qt(n.mapValue,i):(this.Qt(n.mapValue,i),this.Bt(i)):"arrayValue"in n?(this.$t(n.arrayValue,i),this.Bt(i)):j(19022,{Ut:n})}Nt(n,i){this.Mt(i,25),this.Kt(n,i)}Kt(n,i){i.Ot(n)}Qt(n,i){let a=n.fields||{};this.Mt(i,55);for(let d of Object.keys(a))this.Nt(d,i),this.Ct(a[d],i)}qt(n,i){var a,d;let _=n.fields||{};this.Mt(i,53);let w=Os,T=((d=(a=_[w].arrayValue)===null||a===void 0?void 0:a.values)===null||d===void 0?void 0:d.length)||0;this.Mt(i,15),i.xt(te(T)),this.Nt(w,i),this.Ct(_[w],i)}$t(n,i){let a=n.values||[];this.Mt(i,50);for(let d of a)this.Ct(d,i)}kt(n,i){this.Mt(i,37),wt.fromName(n).path.forEach(a=>{this.Mt(i,60),this.Kt(a,i)})}Mt(n,i){n.xt(i)}Bt(n){n.xt(2)}};ke.Wt=new ke;var Xa=new Uint8Array(0);var Ls=41943040,Z=class o{static withCacheSize(n){return new o(n,o.DEFAULT_COLLECTION_PERCENTILE,o.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}constructor(n,i,a){this.cacheSizeCollectionThreshold=n,this.percentileToCollect=i,this.maximumSequenceNumbersToCollect=a}};Z.DEFAULT_COLLECTION_PERCENTILE=10,Z.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,Z.DEFAULT=new Z(Ls,Z.DEFAULT_COLLECTION_PERCENTILE,Z.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),Z.DISABLED=new Z(-1,0,0);var ra=1048576;function _n(){return typeof document<"u"?document:null}var Cn=class{constructor(n,i,a=1e3,d=1.5,_=6e4){this.xi=n,this.timerId=i,this.A_=a,this.R_=d,this.V_=_,this.m_=0,this.f_=null,this.g_=Date.now(),this.reset()}reset(){this.m_=0}p_(){this.m_=this.V_}y_(n){this.cancel();let i=Math.floor(this.m_+this.w_()),a=Math.max(0,Date.now()-this.g_),d=Math.max(0,i-a);d>0&&W("ExponentialBackoff",`Backing off for ${d} ms (base delay: ${this.m_} ms, delay with jitter: ${i} ms, last attempt: ${a} ms ago)`),this.f_=this.xi.enqueueAfterDelay(this.timerId,d,()=>(this.g_=Date.now(),n())),this.m_*=this.R_,this.m_<this.A_&&(this.m_=this.A_),this.m_>this.V_&&(this.m_=this.V_)}b_(){this.f_!==null&&(this.f_.skipDelay(),this.f_=null)}cancel(){this.f_!==null&&(this.f_.cancel(),this.f_=null)}w_(){return(Math.random()-.5)*this.m_}};var Vn=class o{constructor(n,i,a,d,_){this.asyncQueue=n,this.timerId=i,this.targetTimeMs=a,this.op=d,this.removalCallback=_,this.deferred=new vt,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(w=>{})}get promise(){return this.deferred.promise}static createAndSchedule(n,i,a,d,_){let w=Date.now()+a,T=new o(n,i,w,d,_);return T.start(a),T}start(n){this.timerHandle=setTimeout(()=>this.handleDelayElapsed(),n)}skipDelay(){return this.handleDelayElapsed()}cancel(n){this.timerHandle!==null&&(this.clearTimeout(),this.deferred.reject(new k(z.CANCELLED,"Operation cancelled"+(n?": "+n:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget(()=>this.timerHandle!==null?(this.clearTimeout(),this.op().then(n=>this.deferred.resolve(n))):Promise.resolve())}clearTimeout(){this.timerHandle!==null&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}};var ms,gs;(gs=ms||(ms={})).xa="default",gs.Cache="cache";function ia(o){let n={};return o.timeoutSeconds!==void 0&&(n.timeoutSeconds=o.timeoutSeconds),n}var ps=new Map;function sa(o,n,i,a){if(n===!0&&a===!0)throw new k(z.INVALID_ARGUMENT,`${o} and ${i} cannot be used together.`)}function oa(o){if(o===void 0)return"undefined";if(o===null)return"null";if(typeof o=="string")return o.length>20&&(o=`${o.substring(0,20)}...`),JSON.stringify(o);if(typeof o=="number"||typeof o=="boolean")return""+o;if(typeof o=="object"){if(o instanceof Array)return"an array";{let n=function(a){return a.constructor?a.constructor.name:null}(o);return n?`a custom ${n} object`:"an object"}}return typeof o=="function"?"a function":j(12329,{type:typeof o})}function aa(o,n){if("_delegate"in o&&(o=o._delegate),!(o instanceof n)){if(n.name===o.constructor.name)throw new k(z.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");{let i=oa(o);throw new k(z.INVALID_ARGUMENT,`Expected type '${n.name}', but it was: ${i}`)}}return o}var qs="firestore.googleapis.com",ys=!0,Me=class{constructor(n){var i,a;if(n.host===void 0){if(n.ssl!==void 0)throw new k(z.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host=qs,this.ssl=ys}else this.host=n.host,this.ssl=(i=n.ssl)!==null&&i!==void 0?i:ys;if(this.isUsingEmulator=n.emulatorOptions!==void 0,this.credentials=n.credentials,this.ignoreUndefinedProperties=!!n.ignoreUndefinedProperties,this.localCache=n.localCache,n.cacheSizeBytes===void 0)this.cacheSizeBytes=Ls;else{if(n.cacheSizeBytes!==-1&&n.cacheSizeBytes<ra)throw new k(z.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=n.cacheSizeBytes}sa("experimentalForceLongPolling",n.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",n.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!n.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:n.experimentalAutoDetectLongPolling===void 0?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!n.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=ia((a=n.experimentalLongPollingOptions)!==null&&a!==void 0?a:{}),function(_){if(_.timeoutSeconds!==void 0){if(isNaN(_.timeoutSeconds))throw new k(z.INVALID_ARGUMENT,`invalid long polling timeout: ${_.timeoutSeconds} (must not be NaN)`);if(_.timeoutSeconds<5)throw new k(z.INVALID_ARGUMENT,`invalid long polling timeout: ${_.timeoutSeconds} (minimum allowed value is 5)`);if(_.timeoutSeconds>30)throw new k(z.INVALID_ARGUMENT,`invalid long polling timeout: ${_.timeoutSeconds} (maximum allowed value is 30)`)}}(this.experimentalLongPollingOptions),this.useFetchStreams=!!n.useFetchStreams}isEqual(n){return this.host===n.host&&this.ssl===n.ssl&&this.credentials===n.credentials&&this.cacheSizeBytes===n.cacheSizeBytes&&this.experimentalForceLongPolling===n.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===n.experimentalAutoDetectLongPolling&&function(a,d){return a.timeoutSeconds===d.timeoutSeconds}(this.experimentalLongPollingOptions,n.experimentalLongPollingOptions)&&this.ignoreUndefinedProperties===n.ignoreUndefinedProperties&&this.useFetchStreams===n.useFetchStreams}},Fe=class{constructor(n,i,a,d){this._authCredentials=n,this._appCheckCredentials=i,this._databaseId=a,this._app=d,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new Me({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(!this._app)throw new k(z.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app}get _initialized(){return this._settingsFrozen}get _terminated(){return this._terminateTask!=="notTerminated"}_setSettings(n){if(this._settingsFrozen)throw new k(z.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new Me(n),this._emulatorOptions=n.emulatorOptions||{},n.credentials!==void 0&&(this._authCredentials=function(a){if(!a)return new vn;switch(a.type){case"firstParty":return new En(a.sessionIndex||"0",a.iamToken||null,a.authTokenFactory||null);case"provider":return a.client;default:throw new k(z.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}}(n.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return this._terminateTask==="notTerminated"&&(this._terminateTask=this._terminate()),this._terminateTask}_restart(){return lt(this,null,function*(){this._terminateTask==="notTerminated"?yield this._terminate():this._terminateTask="notTerminated"})}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return function(i){let a=ps.get(i);a&&(W("ComponentProvider","Removing Datastore"),ps.delete(i),a.terminate())}(this),Promise.resolve()}};function Bs(o,n,i,a={}){var d;o=aa(o,Fe);let _=fi(n),w=o._getSettings(),T=Object.assign(Object.assign({},w),{emulatorOptions:o._getEmulatorOptions()}),R=`${n}:${i}`;_&&(mi(`https://${R}`),pi("Firestore",!0)),w.host!==qs&&w.host!==R&&Mo("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");let P=Object.assign(Object.assign({},w),{host:R,ssl:_,emulatorOptions:a});if(!_i(P,T)&&(o._setSettings(P),a.mockUserToken)){let H,G;if(typeof a.mockUserToken=="string")H=a.mockUserToken,G=N.MOCK_USER;else{H=gi(a.mockUserToken,(d=o._app)===null||d===void 0?void 0:d.options.projectId);let C=a.mockUserToken.sub||a.mockUserToken.user_id;if(!C)throw new k(z.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");G=new N(C)}o._authCredentials=new wn(new Ce(H,G))}}var _s="AsyncQueue",Oe=class{constructor(n=Promise.resolve()){this.Ju=[],this.Yu=!1,this.Zu=[],this.Xu=null,this.ec=!1,this.tc=!1,this.nc=[],this.x_=new Cn(this,"async_queue_retry"),this.rc=()=>{let a=_n();a&&W(_s,"Visibility state changed to "+a.visibilityState),this.x_.b_()},this.sc=n;let i=_n();i&&typeof i.addEventListener=="function"&&i.addEventListener("visibilitychange",this.rc)}get isShuttingDown(){return this.Yu}enqueueAndForget(n){this.enqueue(n)}enqueueAndForgetEvenWhileRestricted(n){this.oc(),this._c(n)}enterRestrictedMode(n){if(!this.Yu){this.Yu=!0,this.tc=n||!1;let i=_n();i&&typeof i.removeEventListener=="function"&&i.removeEventListener("visibilitychange",this.rc)}}enqueue(n){if(this.oc(),this.Yu)return new Promise(()=>{});let i=new vt;return this._c(()=>this.Yu&&this.tc?Promise.resolve():(n().then(i.resolve,i.reject),i.promise)).then(()=>i.promise)}enqueueRetryable(n){this.enqueueAndForget(()=>(this.Ju.push(n),this.ac()))}ac(){return lt(this,null,function*(){if(this.Ju.length!==0){try{yield this.Ju[0](),this.Ju.shift(),this.x_.reset()}catch(n){if(!qo(n))throw n;W(_s,"Operation failed with retryable error: "+n)}this.Ju.length>0&&this.x_.y_(()=>this.ac())}})}_c(n){let i=this.sc.then(()=>(this.ec=!0,n().catch(a=>{throw this.Xu=a,this.ec=!1,ws("INTERNAL UNHANDLED ERROR: ",vs(a)),a}).then(a=>(this.ec=!1,a))));return this.sc=i,i}enqueueAfterDelay(n,i,a){this.oc(),this.nc.indexOf(n)>-1&&(i=0);let d=Vn.createAndSchedule(this,n,i,a,_=>this.uc(_));return this.Zu.push(d),d}oc(){this.Xu&&j(47125,{cc:vs(this.Xu)})}verifyOperationInProgress(){}lc(){return lt(this,null,function*(){let n;do n=this.sc,yield n;while(n!==this.sc)})}hc(n){for(let i of this.Zu)if(i.timerId===n)return!0;return!1}Pc(n){return this.lc().then(()=>{this.Zu.sort((i,a)=>i.targetTimeMs-a.targetTimeMs);for(let i of this.Zu)if(i.skipDelay(),n!=="all"&&i.timerId===n)break;return this.lc()})}Tc(n){this.nc.push(n)}uc(n){let i=this.Zu.indexOf(n);this.Zu.splice(i,1)}};function vs(o){let n=o.message||"";return o.stack&&(n=o.stack.includes(o.message)?o.stack:o.message+`
`+o.stack),n}var Dn=class extends Fe{constructor(n,i,a,d){super(n,i,a,d),this.type="firestore",this._queue=new Oe,this._persistenceKey=d?.name||"[DEFAULT]"}_terminate(){return lt(this,null,function*(){if(this._firestoreClient){let n=this._firestoreClient.terminate();this._queue=new Oe(n),this._firestoreClient=void 0,yield n}})}};function Us(o,n){let i=typeof o=="object"?o:bi(),a=typeof o=="string"?o:n||Pn,d=Ii(i,"firestore").getImmediate({identifier:a});if(!d._initialized){let _=di("firestore");_&&Bs(d,..._)}return d}var Ja=new RegExp("[~\\*/\\[\\]]");(function(n,i=!0){(function(d){ee=d})(Ai),Ti(new vi("firestore",(a,{instanceIdentifier:d,options:_})=>{let w=a.getProvider("app").getImmediate(),T=new Dn(new Tn(a.getProvider("auth-internal")),new An(w,a.getProvider("app-check-internal")),function(P,H){if(!Object.prototype.hasOwnProperty.apply(P.options,["projectId"]))throw new k(z.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.');return new xn(P.options.projectId,H)}(w,d),w);return _=Object.assign({useFetchStreams:i},_),T._setSettings(_),T},"PUBLIC").setMultipleInstances(!0)),Zt(ls,hs,n),Zt(ls,hs,"esm2017")})();var ne=class{constructor(n){return n}},zs="firestore",kn=class{constructor(){return Pi(zs)}};var Mn=new Te("angularfire2.firestore-instances");function ua(o,n){let i=Ri(zs,o,n);return i&&new ne(i)}function ca(o){return(n,i)=>{let a=n.runOutsideAngular(()=>o(i));return new ne(a)}}var la={provide:kn,deps:[[new Xt,Mn]]},ha={provide:ne,useFactory:ua,deps:[[new Xt,Mn],Vi]};function $s(o,...n){return Zt("angularfire",Si.full,"fst"),Ie([ha,la,{provide:Mn,useFactory:ca(o),multi:!0,deps:[Se,Ee,xi,Di,[new Xt,Fi],[new Xt,Mi],...n]}])}var js=Ci(Us,!0);var Gs=[{path:"",loadComponent:()=>import("./chunk-4O5MBBM7.js").then(o=>o.CaptureComponent),title:"Capture Pigeon - Pigeon Analyzer"},{path:"deck",loadComponent:()=>import("./chunk-Y77PD6PZ.js").then(o=>o.PigeonDeckComponent),title:"My Pigeon Deck - Pigeon Analyzer"},{path:"**",redirectTo:""}];var Ks={production:!1,firebase:{apiKey:"AIzaSyD5QZG4F6rJMVNChrsxwHAdGFir-9cFzhk",authDomain:"pigeon-gogo.firebaseapp.com",projectId:"pigeon-gogo",storageBucket:"pigeon-gogo.firebasestorage.app",messagingSenderId:"201241450262",appId:"1:201241450262:web:9296bb09d8a06257556237"}};var Qs={providers:[Br(),ii({eventCoalescing:!0}),hi(Gs),Ji(),Ni(()=>ki(Ks.firebase)),Oi(()=>Li()),$s(()=>js()),qi(()=>Bi()),Ui(()=>zi())]};var da=["*",[["mat-toolbar-row"]]],fa=["*","mat-toolbar-row"],ma=(()=>{class o{static \u0275fac=function(a){return new(a||o)};static \u0275dir=Wr({type:o,selectors:[["mat-toolbar-row"]],hostAttrs:[1,"mat-toolbar-row"],exportAs:["matToolbarRow"]})}return o})(),Ws=(()=>{class o{_elementRef=J(zr);_platform=J($i);_document=J(Ae);color;_toolbarRows;constructor(){}ngAfterViewInit(){this._platform.isBrowser&&(this._checkToolbarMixedModes(),this._toolbarRows.changes.subscribe(()=>this._checkToolbarMixedModes()))}_checkToolbarMixedModes(){this._toolbarRows.length}static \u0275fac=function(a){return new(a||o)};static \u0275cmp=be({type:o,selectors:[["mat-toolbar"]],contentQueries:function(a,d,_){if(a&1&&Xr(_,ma,5),a&2){let w;Jr(w=Zr())&&(d._toolbarRows=w)}},hostAttrs:[1,"mat-toolbar"],hostVars:6,hostBindings:function(a,d){a&2&&(ei(d.color?"mat-"+d.color:""),ti("mat-toolbar-multiple-rows",d._toolbarRows.length>0)("mat-toolbar-single-row",d._toolbarRows.length===0))},inputs:{color:"color"},exportAs:["matToolbar"],ngContentSelectors:fa,decls:2,vars:0,template:function(a,d){a&1&&(Yr(da),fn(0),fn(1,1))},styles:[`.mat-toolbar{background:var(--mat-toolbar-container-background-color, var(--mat-sys-surface));color:var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar,.mat-toolbar h1,.mat-toolbar h2,.mat-toolbar h3,.mat-toolbar h4,.mat-toolbar h5,.mat-toolbar h6{font-family:var(--mat-toolbar-title-text-font, var(--mat-sys-title-large-font));font-size:var(--mat-toolbar-title-text-size, var(--mat-sys-title-large-size));line-height:var(--mat-toolbar-title-text-line-height, var(--mat-sys-title-large-line-height));font-weight:var(--mat-toolbar-title-text-weight, var(--mat-sys-title-large-weight));letter-spacing:var(--mat-toolbar-title-text-tracking, var(--mat-sys-title-large-tracking));margin:0}@media(forced-colors: active){.mat-toolbar{outline:solid 1px}}.mat-toolbar .mat-form-field-underline,.mat-toolbar .mat-form-field-ripple,.mat-toolbar .mat-focused .mat-form-field-ripple{background-color:currentColor}.mat-toolbar .mat-form-field-label,.mat-toolbar .mat-focused .mat-form-field-label,.mat-toolbar .mat-select-value,.mat-toolbar .mat-select-arrow,.mat-toolbar .mat-form-field.mat-focused .mat-select-arrow{color:inherit}.mat-toolbar .mat-input-element{caret-color:currentColor}.mat-toolbar .mat-mdc-button-base.mat-mdc-button-base.mat-unthemed{--mat-button-text-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface));--mat-button-outlined-label-text-color: var(--mat-toolbar-container-text-color, var(--mat-sys-on-surface))}.mat-toolbar-row,.mat-toolbar-single-row{display:flex;box-sizing:border-box;padding:0 16px;width:100%;flex-direction:row;align-items:center;white-space:nowrap;height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-row,.mat-toolbar-single-row{height:var(--mat-toolbar-mobile-height, 56px)}}.mat-toolbar-multiple-rows{display:flex;box-sizing:border-box;flex-direction:column;width:100%;min-height:var(--mat-toolbar-standard-height, 64px)}@media(max-width: 599px){.mat-toolbar-multiple-rows{min-height:var(--mat-toolbar-mobile-height, 56px)}}
`],encapsulation:2,changeDetection:0})}return o})();var Hs=(()=>{class o{static \u0275fac=function(a){return new(a||o)};static \u0275mod=Qr({type:o});static \u0275inj=qr({imports:[mn,mn]})}return o})();var ya=()=>({exact:!0}),Le=class o{firebaseService=J(Yi);snackBar=J(Wi);title="Pigeon Analyzer";ngOnInit(){this.signInUser()}signInUser(){return lt(this,null,function*(){try{yield this.firebaseService.signInAnonymously(),console.log("User signed in anonymously")}catch(n){console.error("Failed to sign in:",n),this.snackBar.open("Failed to initialize app. Please refresh the page.","Close",{duration:5e3})}})}static \u0275fac=function(i){return new(i||o)};static \u0275cmp=be({type:o,selectors:[["app-root"]],decls:15,vars:3,consts:[[1,"app-container"],["color","primary",1,"app-toolbar"],[1,"app-icon"],[1,"app-title"],[1,"spacer"],["mat-icon-button","","routerLink","/","routerLinkActive","active-nav-button","matTooltip","Capture Pigeon",3,"routerLinkActiveOptions"],["mat-icon-button","","routerLink","/deck","routerLinkActive","active-nav-button","matTooltip","My Pigeon Deck"],[1,"main-content"]],template:function(i,a){i&1&&(St(0,"div",0)(1,"mat-toolbar",1)(2,"mat-icon",2),Jt(3,"pets"),Rt(),St(4,"span",3),Jt(5),Rt(),dn(6,"span",4),St(7,"button",5)(8,"mat-icon"),Jt(9,"camera_alt"),Rt()(),St(10,"button",6)(11,"mat-icon"),Jt(12,"collections"),Rt()()(),St(13,"main",7),dn(14,"router-outlet"),Rt()()),i&2&&(hn(5),ni(a.title),hn(2),Hr("routerLinkActiveOptions",ri(2,ya)))},dependencies:[si,ui,ci,li,Hs,Ws,Qi,Ki,Gi,ji,pn,gn,Hi],styles:[".app-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;background-color:#f5f5f5}.app-toolbar[_ngcontent-%COMP%]{position:sticky;top:0;z-index:1000;box-shadow:0 2px 4px #0000001a}.app-icon[_ngcontent-%COMP%]{margin-right:8px}.app-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:500}.spacer[_ngcontent-%COMP%]{flex:1 1 auto}.active-nav-button[_ngcontent-%COMP%]{background-color:#ffffff1a;border-radius:4px}.main-content[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:20px}@media (max-width: 768px){.main-content[_ngcontent-%COMP%]{padding:16px}}"]})};ai(Le,Qs).catch(o=>console.error(o));
