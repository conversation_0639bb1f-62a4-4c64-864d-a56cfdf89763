import{$ as $l,$a as Tn,$b as Po,A as be,Aa as ql,Ab as lu,Ac as Y,B as bo,Ba as dr,Bb as uu,Bc as vu,C as mi,Ca as q,D as gi,Dc as Ci,E as Ul,Ea as ke,<PERSON>a as Yt,Ga as Co,Gb as Qt,H as ae,Ha as j,Hb as It,I as yo,Ia as So,Ib as Oe,J as et,Ja as Kl,K as yt,<PERSON> as Xt,Kb as du,L as _i,La as Yl,M as Eo,Ma as nt,Mb as fr,N as Be,Na as yi,Nb as Oo,O as wo,Oa as hr,Ob as en,P as Kt,Pa as To,Pb as at,Q as Et,Qa as Cn,Qb as hu,R as Io,Ra as Sn,Rb as wi,S as Bl,Sa as Xl,Sb as An,Ta as Zl,Tb as kn,U as jl,Ua as Jl,V as Vl,Va as Ql,Wa as eu,X as vi,Xa as tu,Y as lr,Ya as nu,Z as ye,Za as Ee,Zb as <PERSON>e,_ as je,_a as ru,_b as Ii,a as g,aa as V,ab as Rn,b as J,ba as O,bb as Zt,bc as Mo,cb as we,d as Gt,da as _,db as Ve,e as h,ea as N,eb as ue,f as me,fa as zl,g as Nl,ga as y,gb as rt,h as Se,ha as T,i as fo,ia as d,ib as Ei,j as po,ja as Te,jb as iu,k as A,ka as Hl,kb as Ro,l as oe,la as le,lb as K,lc as fu,ma as ge,mb as x,n as xl,na as Wl,nb as B,nc as No,o as Fl,oa as Gl,ob as su,p as Qe,pb as it,pc as pu,q as mo,qb as ou,qc as Di,r as go,ra as k,rb as au,s as _o,sa as w,sb as Q,sc as mu,t as ee,ta as bi,tb as D,u as b,ua as wt,ub as st,v as qt,va as ur,vb as Ao,vc as xo,w as vo,wb as ko,wc as gu,x as Ll,xa as Do,xb as cu,xc as Fo,y as E,ya as tt,yb as Jt,yc as Dt,z as cr,zb as ot,zc as _u}from"./chunk-OZCVVD7X.js";var Eu=null;function ct(){return Eu}function Lo(n){Eu??=n}var pr=class{},Uo=(()=>{class n{historyGo(e){throw new Error("")}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:()=>d(wu),providedIn:"platform"})}return n})();var wu=(()=>{class n extends Uo{_location;_history;_doc=d(w);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return ct().getBaseHref(this._doc)}onPopState(e){let r=ct().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",e,!1),()=>r.removeEventListener("popstate",e)}onHashChange(e){let r=ct().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",e,!1),()=>r.removeEventListener("hashchange",e)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(e){this._location.pathname=e}pushState(e,r,i){this._history.pushState(e,r,i)}replaceState(e,r,i){this._history.replaceState(e,r,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(e=0){this._history.go(e)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:()=>new n,providedIn:"platform"})}return n})();function Iu(n,t){return n?t?n.endsWith("/")?t.startsWith("/")?n+t.slice(1):n+t:t.startsWith("/")?n+t:`${n}/${t}`:n:t}function bu(n){let t=n.search(/#|\?|$/);return n[t-1]==="/"?n.slice(0,t-1)+n.slice(t):n}function Ct(n){return n&&n[0]!=="?"?`?${n}`:n}var On=(()=>{class n{historyGo(e){throw new Error("")}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:()=>d(Cu),providedIn:"root"})}return n})(),Du=new y(""),Cu=(()=>{class n extends On{_platformLocation;_baseHref;_removeListenerFns=[];constructor(e,r){super(),this._platformLocation=e,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??d(w).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(e){this._removeListenerFns.push(this._platformLocation.onPopState(e),this._platformLocation.onHashChange(e))}getBaseHref(){return this._baseHref}prepareExternalUrl(e){return Iu(this._baseHref,e)}path(e=!1){let r=this._platformLocation.pathname+Ct(this._platformLocation.search),i=this._platformLocation.hash;return i&&e?`${r}${i}`:r}pushState(e,r,i,s){let o=this.prepareExternalUrl(i+Ct(s));this._platformLocation.pushState(e,r,o)}replaceState(e,r,i,s){let o=this.prepareExternalUrl(i+Ct(s));this._platformLocation.replaceState(e,r,o)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(e=0){this._platformLocation.historyGo?.(e)}static \u0275fac=function(r){return new(r||n)(T(Uo),T(Du,8))};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),St=(()=>{class n{_subject=new A;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(e){this._locationStrategy=e;let r=this._locationStrategy.getBaseHref();this._basePath=pm(bu(yu(r))),this._locationStrategy.onPopState(i=>{this._subject.next({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(e=!1){return this.normalize(this._locationStrategy.path(e))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(e,r=""){return this.path()==this.normalize(e+Ct(r))}normalize(e){return n.stripTrailingSlash(fm(this._basePath,yu(e)))}prepareExternalUrl(e){return e&&e[0]!=="/"&&(e="/"+e),this._locationStrategy.prepareExternalUrl(e)}go(e,r="",i=null){this._locationStrategy.pushState(i,"",e,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(e+Ct(r)),i)}replaceState(e,r="",i=null){this._locationStrategy.replaceState(i,"",e,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(e+Ct(r)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(e=0){this._locationStrategy.historyGo?.(e)}onUrlChange(e){return this._urlChangeListeners.push(e),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(e);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(e="",r){this._urlChangeListeners.forEach(i=>i(e,r))}subscribe(e,r,i){return this._subject.subscribe({next:e,error:r??void 0,complete:i??void 0})}static normalizeQueryParams=Ct;static joinWithSlash=Iu;static stripTrailingSlash=bu;static \u0275fac=function(r){return new(r||n)(T(On))};static \u0275prov=_({token:n,factory:()=>hm(),providedIn:"root"})}return n})();function hm(){return new St(T(On))}function fm(n,t){if(!n||!t.startsWith(n))return t;let e=t.substring(n.length);return e===""||["/",";","?","#"].includes(e[0])?e:t}function yu(n){return n.replace(/\/index.html$/,"")}function pm(n){if(new RegExp("^(https?:)?//").test(n)){let[,e]=n.split(/\/\/[^\/]+/);return e}return n}var Bo=/\s+/,Su=[],_m=(()=>{class n{_ngEl;_renderer;initialClasses=Su;rawClass;stateMap=new Map;constructor(e,r){this._ngEl=e,this._renderer=r}set klass(e){this.initialClasses=e!=null?e.trim().split(Bo):Su}set ngClass(e){this.rawClass=typeof e=="string"?e.trim().split(Bo):e}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let e=this.rawClass;if(Array.isArray(e)||e instanceof Set)for(let r of e)this._updateState(r,!0);else if(e!=null)for(let r of Object.keys(e))this._updateState(r,!!e[r]);this._applyStateDiff()}_updateState(e,r){let i=this.stateMap.get(e);i!==void 0?(i.enabled!==r&&(i.changed=!0,i.enabled=r),i.touched=!0):this.stateMap.set(e,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let e of this.stateMap){let r=e[0],i=e[1];i.changed?(this._toggleClass(r,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),i.touched=!1}}_toggleClass(e,r){e=e.trim(),e.length>0&&e.split(Bo).forEach(i=>{r?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}static \u0275fac=function(r){return new(r||n)(ue(j),ue(Ve))};static \u0275dir=B({type:n,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return n})();var vm=(()=>{class n{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(e){this._viewContainerRef=e}ngOnChanges(e){if(this._shouldRecreateView(e)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let i=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,i,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(e){return!!e.ngTemplateOutlet||!!e.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(e,r,i)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,i):!1,get:(e,r,i)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,i)}})}static \u0275fac=function(r){return new(r||n)(ue(rt))};static \u0275dir=B({type:n,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[ke]})}return n})();function bm(n,t){return new O(2100,!1)}var ym=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,Em=(()=>{class n{transform(e){if(e==null)return null;if(typeof e!="string")throw bm(n,e);return e.replace(ym,r=>r[0].toUpperCase()+r.slice(1).toLowerCase())}static \u0275fac=function(r){return new(r||n)};static \u0275pipe=su({name:"titlecase",type:n,pure:!0})}return n})();var Tu=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({})}return n})();function jo(n,t){t=encodeURIComponent(t);for(let e of n.split(";")){let r=e.indexOf("="),[i,s]=r==-1?[e,""]:[e.slice(0,r),e.slice(r+1)];if(i.trim()===t)return decodeURIComponent(s)}return null}var mr=class{};var Vo="browser",Im="server";function Ru(n){return n===Vo}function Au(n){return n===Im}var Ri=new y(""),Wo=(()=>{class n{_zone;_plugins;_eventNameToPlugin=new Map;constructor(e,r){this._zone=r,e.forEach(i=>{i.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,r,i,s){return this._findPluginFor(r).addEventListener(e,r,i,s)}getZone(){return this._zone}_findPluginFor(e){let r=this._eventNameToPlugin.get(e);if(r)return r;if(r=this._plugins.find(s=>s.supports(e)),!r)throw new O(5101,!1);return this._eventNameToPlugin.set(e,r),r}static \u0275fac=function(r){return new(r||n)(T(Ri),T(D))};static \u0275prov=_({token:n,factory:n.\u0275fac})}return n})(),gr=class{_doc;constructor(t){this._doc=t}manager},Si="ng-app-id";function ku(n){for(let t of n)t.remove()}function Ou(n,t){let e=t.createElement("style");return e.textContent=n,e}function Cm(n,t,e,r){let i=n.head?.querySelectorAll(`style[${Si}="${t}"],link[${Si}="${t}"]`);if(i)for(let s of i)s.removeAttribute(Si),s instanceof HTMLLinkElement?r.set(s.href.slice(s.href.lastIndexOf("/")+1),{usage:0,elements:[s]}):s.textContent&&e.set(s.textContent,{usage:0,elements:[s]})}function zo(n,t){let e=t.createElement("link");return e.setAttribute("rel","stylesheet"),e.setAttribute("href",n),e}var Go=(()=>{class n{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(e,r,i,s={}){this.doc=e,this.appId=r,this.nonce=i,this.isServer=Au(s),Cm(e,r,this.inline,this.external),this.hosts.add(e.head)}addStyles(e,r){for(let i of e)this.addUsage(i,this.inline,Ou);r?.forEach(i=>this.addUsage(i,this.external,zo))}removeStyles(e,r){for(let i of e)this.removeUsage(i,this.inline);r?.forEach(i=>this.removeUsage(i,this.external))}addUsage(e,r,i){let s=r.get(e);s?s.usage++:r.set(e,{usage:1,elements:[...this.hosts].map(o=>this.addElement(o,i(e,this.doc)))})}removeUsage(e,r){let i=r.get(e);i&&(i.usage--,i.usage<=0&&(ku(i.elements),r.delete(e)))}ngOnDestroy(){for(let[,{elements:e}]of[...this.inline,...this.external])ku(e);this.hosts.clear()}addHost(e){this.hosts.add(e);for(let[r,{elements:i}]of this.inline)i.push(this.addElement(e,Ou(r,this.doc)));for(let[r,{elements:i}]of this.external)i.push(this.addElement(e,zo(r,this.doc)))}removeHost(e){this.hosts.delete(e)}addElement(e,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(Si,this.appId),e.appendChild(r)}static \u0275fac=function(r){return new(r||n)(T(w),T(Xt),T(hr,8),T(nt))};static \u0275prov=_({token:n,factory:n.\u0275fac})}return n})(),$o={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},qo=/%COMP%/g;var Mu="%COMP%",Sm=`_nghost-${Mu}`,Tm=`_ngcontent-${Mu}`,Rm=!0,Am=new y("",{providedIn:"root",factory:()=>Rm});function km(n){return Tm.replace(qo,n)}function Om(n){return Sm.replace(qo,n)}function Nu(n,t){return t.map(e=>e.replace(qo,n))}var Ko=(()=>{class n{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(e,r,i,s,o,a,c,l=null,u=null){this.eventManager=e,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=s,this.doc=o,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=u,this.platformIsServer=!1,this.defaultRenderer=new _r(e,o,c,this.platformIsServer,this.tracingService)}createRenderer(e,r){if(!e||!r)return this.defaultRenderer;let i=this.getOrCreateRenderer(e,r);return i instanceof Ti?i.applyToHost(e):i instanceof vr&&i.applyStyles(),i}getOrCreateRenderer(e,r){let i=this.rendererByCompId,s=i.get(r.id);if(!s){let o=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,p=this.platformIsServer,f=this.tracingService;switch(r.encapsulation){case To.Emulated:s=new Ti(c,l,r,this.appId,u,o,a,p,f);break;case To.ShadowDom:return new Ho(c,l,e,r,o,a,this.nonce,p,f);default:s=new vr(c,l,r,u,o,a,p,f);break}i.set(r.id,s)}return s}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(e){this.rendererByCompId.delete(e)}static \u0275fac=function(r){return new(r||n)(T(Wo),T(Go),T(Xt),T(Am),T(w),T(nt),T(D),T(hr),T(au,8))};static \u0275prov=_({token:n,factory:n.\u0275fac})}return n})(),_r=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,e,r,i,s){this.eventManager=t,this.doc=e,this.ngZone=r,this.platformIsServer=i,this.tracingService=s}destroy(){}destroyNode=null;createElement(t,e){return e?this.doc.createElementNS($o[e]||e,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,e){(Pu(t)?t.content:t).appendChild(e)}insertBefore(t,e,r){t&&(Pu(t)?t.content:t).insertBefore(e,r)}removeChild(t,e){e.remove()}selectRootElement(t,e){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new O(-5104,!1);return e||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,e,r,i){if(i){e=i+":"+e;let s=$o[i];s?t.setAttributeNS(s,e,r):t.setAttribute(e,r)}else t.setAttribute(e,r)}removeAttribute(t,e,r){if(r){let i=$o[r];i?t.removeAttributeNS(i,e):t.removeAttribute(`${r}:${e}`)}else t.removeAttribute(e)}addClass(t,e){t.classList.add(e)}removeClass(t,e){t.classList.remove(e)}setStyle(t,e,r,i){i&(Rn.DashCase|Rn.Important)?t.style.setProperty(e,r,i&Rn.Important?"important":""):t.style[e]=r}removeStyle(t,e,r){r&Rn.DashCase?t.style.removeProperty(e):t.style[e]=""}setProperty(t,e,r){t!=null&&(t[e]=r)}setValue(t,e){t.nodeValue=e}listen(t,e,r,i){if(typeof t=="string"&&(t=ct().getGlobalEventTarget(this.doc,t),!t))throw new O(5102,!1);let s=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(s=this.tracingService.wrapEventListener(t,e,s)),this.eventManager.addEventListener(t,e,s,i)}decoratePreventDefault(t){return e=>{if(e==="__ngUnwrap__")return t;t(e)===!1&&e.preventDefault()}}};function Pu(n){return n.tagName==="TEMPLATE"&&n.content!==void 0}var Ho=class extends _r{sharedStylesHost;hostEl;shadowRoot;constructor(t,e,r,i,s,o,a,c,l){super(t,s,o,c,l),this.sharedStylesHost=e,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=i.styles;u=Nu(i.id,u);for(let f of u){let m=document.createElement("style");a&&m.setAttribute("nonce",a),m.textContent=f,this.shadowRoot.appendChild(m)}let p=i.getExternalStyles?.();if(p)for(let f of p){let m=zo(f,s);a&&m.setAttribute("nonce",a),this.shadowRoot.appendChild(m)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,e){return super.appendChild(this.nodeOrShadowRoot(t),e)}insertBefore(t,e,r){return super.insertBefore(this.nodeOrShadowRoot(t),e,r)}removeChild(t,e){return super.removeChild(null,e)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},vr=class extends _r{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,e,r,i,s,o,a,c,l){super(t,s,o,a,c),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=i;let u=r.styles;this.styles=l?Nu(l,u):u,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Ti=class extends vr{contentAttr;hostAttr;constructor(t,e,r,i,s,o,a,c,l){let u=i+"-"+r.id;super(t,e,r,s,o,a,c,l,u),this.contentAttr=km(u),this.hostAttr=Om(u)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,e){let r=super.createElement(t,e);return super.setAttribute(r,this.contentAttr,""),r}};var Ai=class n extends pr{supportsDOMEvents=!0;static makeCurrent(){Lo(new n)}onAndCancel(t,e,r,i){return t.addEventListener(e,r,i),()=>{t.removeEventListener(e,r,i)}}dispatchEvent(t,e){t.dispatchEvent(e)}remove(t){t.remove()}createElement(t,e){return e=e||this.getDefaultDocument(),e.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,e){return e==="window"?window:e==="document"?t:e==="body"?t.body:null}getBaseHref(t){let e=Mm();return e==null?null:Nm(e)}resetBaseElement(){br=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return jo(document.cookie,t)}},br=null;function Mm(){return br=br||document.head.querySelector("base"),br?br.getAttribute("href"):null}function Nm(n){return new URL(n,document.baseURI).pathname}var xm=(()=>{class n{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac})}return n})(),Fu=(()=>{class n extends gr{constructor(e){super(e)}supports(e){return!0}addEventListener(e,r,i,s){return e.addEventListener(r,i,s),()=>this.removeEventListener(e,r,i,s)}removeEventListener(e,r,i,s){return e.removeEventListener(r,i,s)}static \u0275fac=function(r){return new(r||n)(T(w))};static \u0275prov=_({token:n,factory:n.\u0275fac})}return n})(),xu=["alt","control","meta","shift"],Fm={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Lm={alt:n=>n.altKey,control:n=>n.ctrlKey,meta:n=>n.metaKey,shift:n=>n.shiftKey},Lu=(()=>{class n extends gr{constructor(e){super(e)}supports(e){return n.parseEventName(e)!=null}addEventListener(e,r,i,s){let o=n.parseEventName(r),a=n.eventCallback(o.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>ct().onAndCancel(e,o.domEventName,a,s))}static parseEventName(e){let r=e.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let s=n._normalizeKey(r.pop()),o="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),o="code."),xu.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),o+=l+".")}),o+=s,r.length!=0||s.length===0)return null;let c={};return c.domEventName=i,c.fullKey=o,c}static matchEventFullKeyCode(e,r){let i=Fm[e.key]||e.key,s="";return r.indexOf("code.")>-1&&(i=e.code,s="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),xu.forEach(o=>{if(o!==i){let a=Lm[o];a(e)&&(s+=o+".")}}),s+=i,s===r)}static eventCallback(e,r,i){return s=>{n.matchEventFullKeyCode(s,e)&&i.runGuarded(()=>r(s))}}static _normalizeKey(e){return e==="esc"?"escape":e}static \u0275fac=function(r){return new(r||n)(T(w))};static \u0275prov=_({token:n,factory:n.\u0275fac})}return n})();function Um(n,t){return _u(g({rootComponent:n},Bm(t)))}function Bm(n){return{appProviders:[...Hm,...n?.providers??[]],platformProviders:zm}}function jm(){Ai.makeCurrent()}function Vm(){return new wt}function $m(){return Kl(document),document}var zm=[{provide:nt,useValue:Vo},{provide:Yl,useValue:jm,multi:!0},{provide:w,useFactory:$m}];var Hm=[{provide:Hl,useValue:"root"},{provide:wt,useFactory:Vm},{provide:Ri,useClass:Fu,multi:!0,deps:[w]},{provide:Ri,useClass:Lu,multi:!0,deps:[w]},Ko,Go,Wo,{provide:we,useExisting:Ko},{provide:mr,useClass:xm},[]];var Xo=class{};var tn=class n{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(e=>{let r=e.indexOf(":");if(r>0){let i=e.slice(0,r),s=e.slice(r+1).trim();this.addHeaderEntry(i,s)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((e,r)=>{this.addHeaderEntry(r,e)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([e,r])=>{this.setHeaderEntries(e,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let e=this.headers.get(t.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,e){return this.clone({name:t,value:e,op:"a"})}set(t,e){return this.clone({name:t,value:e,op:"s"})}delete(t,e){return this.clone({name:t,value:e,op:"d"})}maybeSetNormalizedName(t,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,t)}init(){this.lazyInit&&(this.lazyInit instanceof n?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(e=>{this.headers.set(e,t.headers.get(e)),this.normalizedNames.set(e,t.normalizedNames.get(e))})}clone(t){let e=new n;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof n?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([t]),e}applyUpdate(t){let e=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,e);let i=(t.op==="a"?this.headers.get(e):void 0)||[];i.push(...r),this.headers.set(e,i);break;case"d":let s=t.value;if(!s)this.headers.delete(e),this.normalizedNames.delete(e);else{let o=this.headers.get(e);if(!o)return;o=o.filter(a=>s.indexOf(a)===-1),o.length===0?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,o)}break}}addHeaderEntry(t,e){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(e):this.headers.set(r,[e])}setHeaderEntries(t,e){let r=(Array.isArray(e)?e:[e]).map(s=>s.toString()),i=t.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(t,i)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>t(this.normalizedNames.get(e),this.headers.get(e)))}};var Zo=class{encodeKey(t){return Uu(t)}encodeValue(t){return Uu(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function Wm(n,t){let e=new Map;return n.length>0&&n.replace(/^\?/,"").split("&").forEach(i=>{let s=i.indexOf("="),[o,a]=s==-1?[t.decodeKey(i),""]:[t.decodeKey(i.slice(0,s)),t.decodeValue(i.slice(s+1))],c=e.get(o)||[];c.push(a),e.set(o,c)}),e}var Gm=/%(\d[a-f0-9])/gi,qm={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Uu(n){return encodeURIComponent(n).replace(Gm,(t,e)=>qm[e]??t)}function ki(n){return`${n}`}var lt=class n{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Zo,t.fromString){if(t.fromObject)throw new O(2805,!1);this.map=Wm(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(e=>{let r=t.fromObject[e],i=Array.isArray(r)?r.map(ki):[ki(r)];this.map.set(e,i)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let e=this.map.get(t);return e?e[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,e){return this.clone({param:t,value:e,op:"a"})}appendAll(t){let e=[];return Object.keys(t).forEach(r=>{let i=t[r];Array.isArray(i)?i.forEach(s=>{e.push({param:r,value:s,op:"a"})}):e.push({param:r,value:i,op:"a"})}),this.clone(e)}set(t,e){return this.clone({param:t,value:e,op:"s"})}delete(t,e){return this.clone({param:t,value:e,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let e=this.encoder.encodeKey(t);return this.map.get(t).map(r=>e+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let e=new n({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(t),e}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let e=(t.op==="a"?this.map.get(t.param):void 0)||[];e.push(ki(t.value)),this.map.set(t.param,e);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],i=r.indexOf(ki(t.value));i!==-1&&r.splice(i,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Jo=class{map=new Map;set(t,e){return this.map.set(t,e),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function Km(n){switch(n){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Bu(n){return typeof ArrayBuffer<"u"&&n instanceof ArrayBuffer}function ju(n){return typeof Blob<"u"&&n instanceof Blob}function Vu(n){return typeof FormData<"u"&&n instanceof FormData}function Ym(n){return typeof URLSearchParams<"u"&&n instanceof URLSearchParams}var Xm="X-Request-URL",$u="text/plain",zu="application/json",eS=`${zu}, ${$u}, */*`,Pn=class n{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;keepalive=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,e,r,i){this.url=e,this.method=t.toUpperCase();let s;if(Km(this.method)||i?(this.body=r!==void 0?r:null,s=i):s=r,s&&(this.reportProgress=!!s.reportProgress,this.withCredentials=!!s.withCredentials,this.keepalive=!!s.keepalive,s.responseType&&(this.responseType=s.responseType),s.headers&&(this.headers=s.headers),s.context&&(this.context=s.context),s.params&&(this.params=s.params),this.transferCache=s.transferCache),this.headers??=new tn,this.context??=new Jo,!this.params)this.params=new lt,this.urlWithParams=e;else{let o=this.params.toString();if(o.length===0)this.urlWithParams=e;else{let a=e.indexOf("?"),c=a===-1?"?":a<e.length-1?"&":"";this.urlWithParams=e+c+o}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Bu(this.body)||ju(this.body)||Vu(this.body)||Ym(this.body)?this.body:this.body instanceof lt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Vu(this.body)?null:ju(this.body)?this.body.type||null:Bu(this.body)?null:typeof this.body=="string"?$u:this.body instanceof lt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?zu:null}clone(t={}){let e=t.method||this.method,r=t.url||this.url,i=t.responseType||this.responseType,s=t.keepalive??this.keepalive,o=t.transferCache??this.transferCache,a=t.body!==void 0?t.body:this.body,c=t.withCredentials??this.withCredentials,l=t.reportProgress??this.reportProgress,u=t.headers||this.headers,p=t.params||this.params,f=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((m,v)=>m.set(v,t.setHeaders[v]),u)),t.setParams&&(p=Object.keys(t.setParams).reduce((m,v)=>m.set(v,t.setParams[v]),p)),new n(e,r,a,{params:p,headers:u,context:f,reportProgress:l,responseType:i,withCredentials:c,transferCache:o,keepalive:s})}},ea=function(n){return n[n.Sent=0]="Sent",n[n.UploadProgress=1]="UploadProgress",n[n.ResponseHeader=2]="ResponseHeader",n[n.DownloadProgress=3]="DownloadProgress",n[n.Response=4]="Response",n[n.User=5]="User",n}(ea||{}),Qo=class{headers;status;statusText;url;ok;type;constructor(t,e=200,r="OK"){this.headers=t.headers||new tn,this.status=t.status!==void 0?t.status:e,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}};var Oi=class n extends Qo{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=ea.Response;clone(t={}){return new n({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}};function Yo(n,t){return{body:t,headers:n.headers,context:n.context,observe:n.observe,params:n.params,reportProgress:n.reportProgress,responseType:n.responseType,withCredentials:n.withCredentials,transferCache:n.transferCache,keepalive:n.keepalive}}var ta=(()=>{class n{handler;constructor(e){this.handler=e}request(e,r,i={}){let s;if(e instanceof Pn)s=e;else{let c;i.headers instanceof tn?c=i.headers:c=new tn(i.headers);let l;i.params&&(i.params instanceof lt?l=i.params:l=new lt({fromObject:i.params})),s=new Pn(e,r,i.body!==void 0?i.body:null,{headers:c,context:i.context,params:l,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache,keepalive:i.keepalive})}let o=b(s).pipe(yt(c=>this.handler.handle(c)));if(e instanceof Pn||i.observe==="events")return o;let a=o.pipe(ae(c=>c instanceof Oi));switch(i.observe||"body"){case"body":switch(s.responseType){case"arraybuffer":return a.pipe(E(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new O(2806,!1);return c.body}));case"blob":return a.pipe(E(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new O(2807,!1);return c.body}));case"text":return a.pipe(E(c=>{if(c.body!==null&&typeof c.body!="string")throw new O(2808,!1);return c.body}));case"json":default:return a.pipe(E(c=>c.body))}case"response":return a;default:throw new O(2809,!1)}}delete(e,r={}){return this.request("DELETE",e,r)}get(e,r={}){return this.request("GET",e,r)}head(e,r={}){return this.request("HEAD",e,r)}jsonp(e,r){return this.request("JSONP",e,{params:new lt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(e,r={}){return this.request("OPTIONS",e,r)}patch(e,r,i={}){return this.request("PATCH",e,Yo(i,r))}post(e,r,i={}){return this.request("POST",e,Yo(i,r))}put(e,r,i={}){return this.request("PUT",e,Yo(i,r))}static \u0275fac=function(r){return new(r||n)(T(Xo))};static \u0275prov=_({token:n,factory:n.\u0275fac})}return n})();var tS=RegExp(`^${Xm}:`,"m");var Hu=(()=>{class n{_doc;constructor(e){this._doc=e}getTitle(){return this._doc.title}setTitle(e){this._doc.title=e||""}static \u0275fac=function(r){return new(r||n)(T(w))};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var na=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:function(r){let i=null;return r?i=new(r||n):i=T(Qm),i},providedIn:"root"})}return n})(),Qm=(()=>{class n extends na{_doc;constructor(e){super(),this._doc=e}sanitize(e,r){if(r==null)return null;switch(e){case Ee.NONE:return r;case Ee.HTML:return Sn(r,"HTML")?Cn(r):nu(this._doc,String(r)).toString();case Ee.STYLE:return Sn(r,"Style")?Cn(r):r;case Ee.SCRIPT:if(Sn(r,"Script"))return Cn(r);throw new O(5200,!1);case Ee.URL:return Sn(r,"URL")?Cn(r):tu(String(r));case Ee.RESOURCE_URL:if(Sn(r,"ResourceURL"))return Cn(r);throw new O(5201,!1);default:throw new O(5202,!1)}}bypassSecurityTrustHtml(e){return Xl(e)}bypassSecurityTrustStyle(e){return Zl(e)}bypassSecurityTrustScript(e){return Jl(e)}bypassSecurityTrustUrl(e){return Ql(e)}bypassSecurityTrustResourceUrl(e){return eu(e)}static \u0275fac=function(r){return new(r||n)(T(w))};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var S="primary",Nr=Symbol("RouteTitle"),aa=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let e=this.params[t];return Array.isArray(e)?e[0]:e}return null}getAll(t){if(this.has(t)){let e=this.params[t];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function sn(n){return new aa(n)}function Qu(n,t,e){let r=e.path.split("/");if(r.length>n.length||e.pathMatch==="full"&&(t.hasChildren()||r.length<n.length))return null;let i={};for(let s=0;s<r.length;s++){let o=r[s],a=n[s];if(o[0]===":")i[o.substring(1)]=a;else if(o!==a.path)return null}return{consumed:n.slice(0,r.length),posParams:i}}function eg(n,t){if(n.length!==t.length)return!1;for(let e=0;e<n.length;++e)if(!$e(n[e],t[e]))return!1;return!0}function $e(n,t){let e=n?ca(n):void 0,r=t?ca(t):void 0;if(!e||!r||e.length!=r.length)return!1;let i;for(let s=0;s<e.length;s++)if(i=e[s],!ed(n[i],t[i]))return!1;return!0}function ca(n){return[...Object.keys(n),...Object.getOwnPropertySymbols(n)]}function ed(n,t){if(Array.isArray(n)&&Array.isArray(t)){if(n.length!==t.length)return!1;let e=[...n].sort(),r=[...t].sort();return e.every((i,s)=>r[s]===i)}else return n===t}function td(n){return n.length>0?n[n.length-1]:null}function ht(n){return vo(n)?n:ko(n)?ee(Promise.resolve(n)):b(n)}var tg={exact:rd,subset:id},nd={exact:ng,subset:rg,ignored:()=>!0};function Gu(n,t,e){return tg[e.paths](n.root,t.root,e.matrixParams)&&nd[e.queryParams](n.queryParams,t.queryParams)&&!(e.fragment==="exact"&&n.fragment!==t.fragment)}function ng(n,t){return $e(n,t)}function rd(n,t,e){if(!nn(n.segments,t.segments)||!Ni(n.segments,t.segments,e)||n.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!n.children[r]||!rd(n.children[r],t.children[r],e))return!1;return!0}function rg(n,t){return Object.keys(t).length<=Object.keys(n).length&&Object.keys(t).every(e=>ed(n[e],t[e]))}function id(n,t,e){return sd(n,t,t.segments,e)}function sd(n,t,e,r){if(n.segments.length>e.length){let i=n.segments.slice(0,e.length);return!(!nn(i,e)||t.hasChildren()||!Ni(i,e,r))}else if(n.segments.length===e.length){if(!nn(n.segments,e)||!Ni(n.segments,e,r))return!1;for(let i in t.children)if(!n.children[i]||!id(n.children[i],t.children[i],r))return!1;return!0}else{let i=e.slice(0,n.segments.length),s=e.slice(n.segments.length);return!nn(n.segments,i)||!Ni(n.segments,i,r)||!n.children[S]?!1:sd(n.children[S],t,s,r)}}function Ni(n,t,e){return t.every((r,i)=>nd[e](n[i].parameters,r.parameters))}var He=class{root;queryParams;fragment;_queryParamMap;constructor(t=new M([],{}),e={},r=null){this.root=t,this.queryParams=e,this.fragment=r}get queryParamMap(){return this._queryParamMap??=sn(this.queryParams),this._queryParamMap}toString(){return og.serialize(this)}},M=class{segments;children;parent=null;constructor(t,e){this.segments=t,this.children=e,Object.values(e).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return xi(this)}},Tt=class{path;parameters;_parameterMap;constructor(t,e){this.path=t,this.parameters=e}get parameterMap(){return this._parameterMap??=sn(this.parameters),this._parameterMap}toString(){return ad(this)}};function ig(n,t){return nn(n,t)&&n.every((e,r)=>$e(e.parameters,t[r].parameters))}function nn(n,t){return n.length!==t.length?!1:n.every((e,r)=>e.path===t[r].path)}function sg(n,t){let e=[];return Object.entries(n.children).forEach(([r,i])=>{r===S&&(e=e.concat(t(i,r)))}),Object.entries(n.children).forEach(([r,i])=>{r!==S&&(e=e.concat(t(i,r)))}),e}var xr=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:()=>new on,providedIn:"root"})}return n})(),on=class{parse(t){let e=new ua(t);return new He(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(t){let e=`/${yr(t.root,!0)}`,r=lg(t.queryParams),i=typeof t.fragment=="string"?`#${ag(t.fragment)}`:"";return`${e}${r}${i}`}},og=new on;function xi(n){return n.segments.map(t=>ad(t)).join("/")}function yr(n,t){if(!n.hasChildren())return xi(n);if(t){let e=n.children[S]?yr(n.children[S],!1):"",r=[];return Object.entries(n.children).forEach(([i,s])=>{i!==S&&r.push(`${i}:${yr(s,!1)}`)}),r.length>0?`${e}(${r.join("//")})`:e}else{let e=sg(n,(r,i)=>i===S?[yr(n.children[S],!1)]:[`${i}:${yr(r,!1)}`]);return Object.keys(n.children).length===1&&n.children[S]!=null?`${xi(n)}/${e[0]}`:`${xi(n)}/(${e.join("//")})`}}function od(n){return encodeURIComponent(n).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Pi(n){return od(n).replace(/%3B/gi,";")}function ag(n){return encodeURI(n)}function la(n){return od(n).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Fi(n){return decodeURIComponent(n)}function qu(n){return Fi(n.replace(/\+/g,"%20"))}function ad(n){return`${la(n.path)}${cg(n.parameters)}`}function cg(n){return Object.entries(n).map(([t,e])=>`;${la(t)}=${la(e)}`).join("")}function lg(n){let t=Object.entries(n).map(([e,r])=>Array.isArray(r)?r.map(i=>`${Pi(e)}=${Pi(i)}`).join("&"):`${Pi(e)}=${Pi(r)}`).filter(e=>e);return t.length?`?${t.join("&")}`:""}var ug=/^[^\/()?;#]+/;function ra(n){let t=n.match(ug);return t?t[0]:""}var dg=/^[^\/()?;=#]+/;function hg(n){let t=n.match(dg);return t?t[0]:""}var fg=/^[^=?&#]+/;function pg(n){let t=n.match(fg);return t?t[0]:""}var mg=/^[^&#]+/;function gg(n){let t=n.match(mg);return t?t[0]:""}var ua=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new M([],{}):new M([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(e).length>0)&&(r[S]=new M(t,e)),r}parseSegment(){let t=ra(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new O(4009,!1);return this.capture(t),new Tt(Fi(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let e=hg(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let i=ra(this.remaining);i&&(r=i,this.capture(r))}t[Fi(e)]=Fi(r)}parseQueryParam(t){let e=pg(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let o=gg(this.remaining);o&&(r=o,this.capture(r))}let i=qu(e),s=qu(r);if(t.hasOwnProperty(i)){let o=t[i];Array.isArray(o)||(o=[o],t[i]=o),o.push(s)}else t[i]=s}parseParens(t){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=ra(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new O(4010,!1);let s;r.indexOf(":")>-1?(s=r.slice(0,r.indexOf(":")),this.capture(s),this.capture(":")):t&&(s=S);let o=this.parseChildren();e[s]=Object.keys(o).length===1?o[S]:new M([],o),this.consumeOptional("//")}return e}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new O(4011,!1)}};function cd(n){return n.segments.length>0?new M([],{[S]:n}):n}function ld(n){let t={};for(let[r,i]of Object.entries(n.children)){let s=ld(i);if(r===S&&s.segments.length===0&&s.hasChildren())for(let[o,a]of Object.entries(s.children))t[o]=a;else(s.segments.length>0||s.hasChildren())&&(t[r]=s)}let e=new M(n.segments,t);return _g(e)}function _g(n){if(n.numberOfChildren===1&&n.children[S]){let t=n.children[S];return new M(n.segments.concat(t.segments),t.children)}return n}function Rt(n){return n instanceof He}function ud(n,t,e=null,r=null){let i=dd(n);return hd(i,t,e,r)}function dd(n){let t;function e(s){let o={};for(let c of s.children){let l=e(c);o[c.outlet]=l}let a=new M(s.url,o);return s===n&&(t=a),a}let r=e(n.root),i=cd(r);return t??i}function hd(n,t,e,r){let i=n;for(;i.parent;)i=i.parent;if(t.length===0)return ia(i,i,i,e,r);let s=vg(t);if(s.toRoot())return ia(i,i,new M([],{}),e,r);let o=bg(s,i,n),a=o.processChildren?wr(o.segmentGroup,o.index,s.commands):pd(o.segmentGroup,o.index,s.commands);return ia(i,o.segmentGroup,a,e,r)}function Li(n){return typeof n=="object"&&n!=null&&!n.outlets&&!n.segmentPath}function Cr(n){return typeof n=="object"&&n!=null&&n.outlets}function ia(n,t,e,r,i){let s={};r&&Object.entries(r).forEach(([c,l])=>{s[c]=Array.isArray(l)?l.map(u=>`${u}`):`${l}`});let o;n===t?o=e:o=fd(n,t,e);let a=cd(ld(o));return new He(a,s,i)}function fd(n,t,e){let r={};return Object.entries(n.children).forEach(([i,s])=>{s===t?r[i]=e:r[i]=fd(s,t,e)}),new M(n.segments,r)}var Ui=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,e,r){if(this.isAbsolute=t,this.numberOfDoubleDots=e,this.commands=r,t&&r.length>0&&Li(r[0]))throw new O(4003,!1);let i=r.find(Cr);if(i&&i!==td(r))throw new O(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function vg(n){if(typeof n[0]=="string"&&n.length===1&&n[0]==="/")return new Ui(!0,0,n);let t=0,e=!1,r=n.reduce((i,s,o)=>{if(typeof s=="object"&&s!=null){if(s.outlets){let a={};return Object.entries(s.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...i,{outlets:a}]}if(s.segmentPath)return[...i,s.segmentPath]}return typeof s!="string"?[...i,s]:o===0?(s.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?e=!0:a===".."?t++:a!=""&&i.push(a))}),i):[...i,s]},[]);return new Ui(e,t,r)}var xn=class{segmentGroup;processChildren;index;constructor(t,e,r){this.segmentGroup=t,this.processChildren=e,this.index=r}};function bg(n,t,e){if(n.isAbsolute)return new xn(t,!0,0);if(!e)return new xn(t,!1,NaN);if(e.parent===null)return new xn(e,!0,0);let r=Li(n.commands[0])?0:1,i=e.segments.length-1+r;return yg(e,i,n.numberOfDoubleDots)}function yg(n,t,e){let r=n,i=t,s=e;for(;s>i;){if(s-=i,r=r.parent,!r)throw new O(4005,!1);i=r.segments.length}return new xn(r,!1,i-s)}function Eg(n){return Cr(n[0])?n[0].outlets:{[S]:n}}function pd(n,t,e){if(n??=new M([],{}),n.segments.length===0&&n.hasChildren())return wr(n,t,e);let r=wg(n,t,e),i=e.slice(r.commandIndex);if(r.match&&r.pathIndex<n.segments.length){let s=new M(n.segments.slice(0,r.pathIndex),{});return s.children[S]=new M(n.segments.slice(r.pathIndex),n.children),wr(s,0,i)}else return r.match&&i.length===0?new M(n.segments,{}):r.match&&!n.hasChildren()?da(n,t,e):r.match?wr(n,0,i):da(n,t,e)}function wr(n,t,e){if(e.length===0)return new M(n.segments,{});{let r=Eg(e),i={};if(Object.keys(r).some(s=>s!==S)&&n.children[S]&&n.numberOfChildren===1&&n.children[S].segments.length===0){let s=wr(n.children[S],t,e);return new M(n.segments,s.children)}return Object.entries(r).forEach(([s,o])=>{typeof o=="string"&&(o=[o]),o!==null&&(i[s]=pd(n.children[s],t,o))}),Object.entries(n.children).forEach(([s,o])=>{r[s]===void 0&&(i[s]=o)}),new M(n.segments,i)}}function wg(n,t,e){let r=0,i=t,s={match:!1,pathIndex:0,commandIndex:0};for(;i<n.segments.length;){if(r>=e.length)return s;let o=n.segments[i],a=e[r];if(Cr(a))break;let c=`${a}`,l=r<e.length-1?e[r+1]:null;if(i>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!Yu(c,l,o))return s;r+=2}else{if(!Yu(c,{},o))return s;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function da(n,t,e){let r=n.segments.slice(0,t),i=0;for(;i<e.length;){let s=e[i];if(Cr(s)){let c=Ig(s.outlets);return new M(r,c)}if(i===0&&Li(e[0])){let c=n.segments[t];r.push(new Tt(c.path,Ku(e[0]))),i++;continue}let o=Cr(s)?s.outlets[S]:`${s}`,a=i<e.length-1?e[i+1]:null;o&&a&&Li(a)?(r.push(new Tt(o,Ku(a))),i+=2):(r.push(new Tt(o,{})),i++)}return new M(r,{})}function Ig(n){let t={};return Object.entries(n).forEach(([e,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[e]=da(new M([],{}),0,r))}),t}function Ku(n){let t={};return Object.entries(n).forEach(([e,r])=>t[e]=`${r}`),t}function Yu(n,t,e){return n==e.path&&$e(t,e.parameters)}var Ir="imperative",te=function(n){return n[n.NavigationStart=0]="NavigationStart",n[n.NavigationEnd=1]="NavigationEnd",n[n.NavigationCancel=2]="NavigationCancel",n[n.NavigationError=3]="NavigationError",n[n.RoutesRecognized=4]="RoutesRecognized",n[n.ResolveStart=5]="ResolveStart",n[n.ResolveEnd=6]="ResolveEnd",n[n.GuardsCheckStart=7]="GuardsCheckStart",n[n.GuardsCheckEnd=8]="GuardsCheckEnd",n[n.RouteConfigLoadStart=9]="RouteConfigLoadStart",n[n.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",n[n.ChildActivationStart=11]="ChildActivationStart",n[n.ChildActivationEnd=12]="ChildActivationEnd",n[n.ActivationStart=13]="ActivationStart",n[n.ActivationEnd=14]="ActivationEnd",n[n.Scroll=15]="Scroll",n[n.NavigationSkipped=16]="NavigationSkipped",n}(te||{}),De=class{id;url;constructor(t,e){this.id=t,this.url=e}},an=class extends De{type=te.NavigationStart;navigationTrigger;restoredState;constructor(t,e,r="imperative",i=null){super(t,e),this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Ne=class extends De{urlAfterRedirects;type=te.NavigationEnd;constructor(t,e,r){super(t,e),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},de=function(n){return n[n.Redirect=0]="Redirect",n[n.SupersededByNewNavigation=1]="SupersededByNewNavigation",n[n.NoDataFromResolver=2]="NoDataFromResolver",n[n.GuardRejected=3]="GuardRejected",n[n.Aborted=4]="Aborted",n}(de||{}),Sr=function(n){return n[n.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",n[n.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",n}(Sr||{}),ze=class extends De{reason;code;type=te.NavigationCancel;constructor(t,e,r,i){super(t,e),this.reason=r,this.code=i}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},ut=class extends De{reason;code;type=te.NavigationSkipped;constructor(t,e,r,i){super(t,e),this.reason=r,this.code=i}},Ln=class extends De{error;target;type=te.NavigationError;constructor(t,e,r,i){super(t,e),this.error=r,this.target=i}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Tr=class extends De{urlAfterRedirects;state;type=te.RoutesRecognized;constructor(t,e,r,i){super(t,e),this.urlAfterRedirects=r,this.state=i}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Bi=class extends De{urlAfterRedirects;state;type=te.GuardsCheckStart;constructor(t,e,r,i){super(t,e),this.urlAfterRedirects=r,this.state=i}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ji=class extends De{urlAfterRedirects;state;shouldActivate;type=te.GuardsCheckEnd;constructor(t,e,r,i,s){super(t,e),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=s}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Vi=class extends De{urlAfterRedirects;state;type=te.ResolveStart;constructor(t,e,r,i){super(t,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},$i=class extends De{urlAfterRedirects;state;type=te.ResolveEnd;constructor(t,e,r,i){super(t,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},zi=class{route;type=te.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Hi=class{route;type=te.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Wi=class{snapshot;type=te.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Gi=class{snapshot;type=te.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},qi=class{snapshot;type=te.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ki=class{snapshot;type=te.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Rr=class{},Un=class{url;navigationBehaviorOptions;constructor(t,e){this.url=t,this.navigationBehaviorOptions=e}};function Dg(n){return!(n instanceof Rr)&&!(n instanceof Un)}function Cg(n,t){return n.providers&&!n._injector&&(n._injector=Ro(n.providers,t,`Route: ${n.path}`)),n._injector??t}function Me(n){return n.outlet||S}function Sg(n,t){let e=n.filter(r=>Me(r)===t);return e.push(...n.filter(r=>Me(r)!==t)),e}function Fr(n){if(!n)return null;if(n.routeConfig?._injector)return n.routeConfig._injector;for(let t=n.parent;t;t=t.parent){let e=t.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var Yi=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Fr(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Vn(this.rootInjector)}},Vn=(()=>{class n{rootInjector;contexts=new Map;constructor(e){this.rootInjector=e}onChildOutletCreated(e,r){let i=this.getOrCreateContext(e);i.outlet=r,this.contexts.set(e,i)}onChildOutletDestroyed(e){let r=this.getContext(e);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let r=this.getContext(e);return r||(r=new Yi(this.rootInjector),this.contexts.set(e,r)),r}getContext(e){return this.contexts.get(e)||null}static \u0275fac=function(r){return new(r||n)(T(le))};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Xi=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let e=this.pathFromRoot(t);return e.length>1?e[e.length-2]:null}children(t){let e=ha(t,this._root);return e?e.children.map(r=>r.value):[]}firstChild(t){let e=ha(t,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(t){let e=fa(t,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==t)}pathFromRoot(t){return fa(t,this._root).map(e=>e.value)}};function ha(n,t){if(n===t.value)return t;for(let e of t.children){let r=ha(n,e);if(r)return r}return null}function fa(n,t){if(n===t.value)return[t];for(let e of t.children){let r=fa(n,e);if(r.length)return r.unshift(t),r}return[]}var Ie=class{value;children;constructor(t,e){this.value=t,this.children=e}toString(){return`TreeNode(${this.value})`}};function Nn(n){let t={};return n&&n.children.forEach(e=>t[e.value.outlet]=e),t}var Ar=class extends Xi{snapshot;constructor(t,e){super(t),this.snapshot=e,Ea(this,t)}toString(){return this.snapshot.toString()}};function md(n){let t=Tg(n),e=new oe([new Tt("",{})]),r=new oe({}),i=new oe({}),s=new oe({}),o=new oe(""),a=new dt(e,r,s,o,i,S,n,t.root);return a.snapshot=t.root,new Ar(new Ie(a,[]),t)}function Tg(n){let t={},e={},r={},i="",s=new rn([],t,r,i,e,S,n,null,{});return new kr("",new Ie(s,[]))}var dt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,e,r,i,s,o,a,c){this.urlSubject=t,this.paramsSubject=e,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=s,this.outlet=o,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(E(l=>l[Nr]))??b(void 0),this.url=t,this.params=e,this.queryParams=r,this.fragment=i,this.data=s}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(E(t=>sn(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(E(t=>sn(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Zi(n,t,e="emptyOnly"){let r,{routeConfig:i}=n;return t!==null&&(e==="always"||i?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:g(g({},t.params),n.params),data:g(g({},t.data),n.data),resolve:g(g(g(g({},n.data),t.data),i?.data),n._resolvedData)}:r={params:g({},n.params),data:g({},n.data),resolve:g(g({},n.data),n._resolvedData??{})},i&&_d(i)&&(r.resolve[Nr]=i.title),r}var rn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Nr]}constructor(t,e,r,i,s,o,a,c,l){this.url=t,this.params=e,this.queryParams=r,this.fragment=i,this.data=s,this.outlet=o,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=sn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=sn(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${e}')`}},kr=class extends Xi{url;constructor(t,e){super(e),this.url=t,Ea(this,e)}toString(){return gd(this._root)}};function Ea(n,t){t.value._routerState=n,t.children.forEach(e=>Ea(n,e))}function gd(n){let t=n.children.length>0?` { ${n.children.map(gd).join(", ")} } `:"";return`${n.value}${t}`}function sa(n){if(n.snapshot){let t=n.snapshot,e=n._futureSnapshot;n.snapshot=e,$e(t.queryParams,e.queryParams)||n.queryParamsSubject.next(e.queryParams),t.fragment!==e.fragment&&n.fragmentSubject.next(e.fragment),$e(t.params,e.params)||n.paramsSubject.next(e.params),eg(t.url,e.url)||n.urlSubject.next(e.url),$e(t.data,e.data)||n.dataSubject.next(e.data)}else n.snapshot=n._futureSnapshot,n.dataSubject.next(n._futureSnapshot.data)}function pa(n,t){let e=$e(n.params,t.params)&&ig(n.url,t.url),r=!n.parent!=!t.parent;return e&&!r&&(!n.parent||pa(n.parent,t.parent))}function _d(n){return typeof n.title=="string"||n.title===null}var vd=new y(""),wa=(()=>{class n{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=S;activateEvents=new Q;deactivateEvents=new Q;attachEvents=new Q;detachEvents=new Q;routerOutletData=mu(void 0);parentContexts=d(Vn);location=d(rt);changeDetector=d(Dt);inputBinder=d(ts,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(e){if(e.name){let{firstChange:r,previousValue:i}=e.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new O(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new O(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new O(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,r){this.activated=e,this._activatedRoute=r,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,r){if(this.isActivated)throw new O(4013,!1);this._activatedRoute=e;let i=this.location,o=e.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new ma(e,a,i.injector,this.routerOutletData);this.activated=i.createComponent(o,{index:i.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||n)};static \u0275dir=B({type:n,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[ke]})}return n})(),ma=class{route;childContexts;parent;outletData;constructor(t,e,r,i){this.route=t,this.childContexts=e,this.parent=r,this.outletData=i}get(t,e){return t===dt?this.route:t===Vn?this.childContexts:t===vd?this.outletData:this.parent.get(t,e)}},ts=new y("");var Ia=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,i){r&1&&Oe(0,"router-outlet")},dependencies:[wa],encapsulation:2})}return n})();function Da(n){let t=n.children&&n.children.map(Da),e=t?J(g({},n),{children:t}):g({},n);return!e.component&&!e.loadComponent&&(t||e.loadChildren)&&e.outlet&&e.outlet!==S&&(e.component=Ia),e}function Rg(n,t,e){let r=Or(n,t._root,e?e._root:void 0);return new Ar(r,t)}function Or(n,t,e){if(e&&n.shouldReuseRoute(t.value,e.value.snapshot)){let r=e.value;r._futureSnapshot=t.value;let i=Ag(n,t,e);return new Ie(r,i)}else{if(n.shouldAttach(t.value)){let s=n.retrieve(t.value);if(s!==null){let o=s.route;return o.value._futureSnapshot=t.value,o.children=t.children.map(a=>Or(n,a)),o}}let r=kg(t.value),i=t.children.map(s=>Or(n,s));return new Ie(r,i)}}function Ag(n,t,e){return t.children.map(r=>{for(let i of e.children)if(n.shouldReuseRoute(r.value,i.value.snapshot))return Or(n,r,i);return Or(n,r)})}function kg(n){return new dt(new oe(n.url),new oe(n.params),new oe(n.queryParams),new oe(n.fragment),new oe(n.data),n.outlet,n.component,n)}var Bn=class{redirectTo;navigationBehaviorOptions;constructor(t,e){this.redirectTo=t,this.navigationBehaviorOptions=e}},bd="ngNavigationCancelingError";function Ji(n,t){let{redirectTo:e,navigationBehaviorOptions:r}=Rt(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,i=yd(!1,de.Redirect);return i.url=e,i.navigationBehaviorOptions=r,i}function yd(n,t){let e=new Error(`NavigationCancelingError: ${n||""}`);return e[bd]=!0,e.cancellationCode=t,e}function Og(n){return Ed(n)&&Rt(n.url)}function Ed(n){return!!n&&n[bd]}var Pg=(n,t,e,r)=>E(i=>(new ga(t,i.targetRouterState,i.currentRouterState,e,r).activate(n),i)),ga=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,e,r,i,s){this.routeReuseStrategy=t,this.futureState=e,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=s}activate(t){let e=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,r,t),sa(this.futureState.root),this.activateChildRoutes(e,r,t)}deactivateChildRoutes(t,e,r){let i=Nn(e);t.children.forEach(s=>{let o=s.value.outlet;this.deactivateRoutes(s,i[o],r),delete i[o]}),Object.values(i).forEach(s=>{this.deactivateRouteAndItsChildren(s,r)})}deactivateRoutes(t,e,r){let i=t.value,s=e?e.value:null;if(i===s)if(i.component){let o=r.getContext(i.outlet);o&&this.deactivateChildRoutes(t,e,o.children)}else this.deactivateChildRoutes(t,e,r);else s&&this.deactivateRouteAndItsChildren(e,r)}deactivateRouteAndItsChildren(t,e){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,e):this.deactivateRouteAndOutlet(t,e)}detachAndStoreRouteSubtree(t,e){let r=e.getContext(t.value.outlet),i=r&&t.value.component?r.children:e,s=Nn(t);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);if(r&&r.outlet){let o=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:o,route:t,contexts:a})}}deactivateRouteAndOutlet(t,e){let r=e.getContext(t.value.outlet),i=r&&t.value.component?r.children:e,s=Nn(t);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,e,r){let i=Nn(e);t.children.forEach(s=>{this.activateRoutes(s,i[s.value.outlet],r),this.forwardEvent(new Ki(s.value.snapshot))}),t.children.length&&this.forwardEvent(new Gi(t.value.snapshot))}activateRoutes(t,e,r){let i=t.value,s=e?e.value:null;if(sa(i),i===s)if(i.component){let o=r.getOrCreateContext(i.outlet);this.activateChildRoutes(t,e,o.children)}else this.activateChildRoutes(t,e,r);else if(i.component){let o=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),o.children.onOutletReAttached(a.contexts),o.attachRef=a.componentRef,o.route=a.route.value,o.outlet&&o.outlet.attach(a.componentRef,a.route.value),sa(a.route.value),this.activateChildRoutes(t,null,o.children)}else o.attachRef=null,o.route=i,o.outlet&&o.outlet.activateWith(i,o.injector),this.activateChildRoutes(t,null,o.children)}else this.activateChildRoutes(t,null,r)}},Qi=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Fn=class{component;route;constructor(t,e){this.component=t,this.route=e}};function Mg(n,t,e){let r=n._root,i=t?t._root:null;return Er(r,i,e,[r.value])}function Ng(n){let t=n.routeConfig?n.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:n,guards:t}}function $n(n,t){let e=Symbol(),r=t.get(n,e);return r===e?typeof n=="function"&&!zl(n)?n:t.get(n):r}function Er(n,t,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=Nn(t);return n.children.forEach(o=>{xg(o,s[o.value.outlet],e,r.concat([o.value]),i),delete s[o.value.outlet]}),Object.entries(s).forEach(([o,a])=>Dr(a,e.getContext(o),i)),i}function xg(n,t,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=n.value,o=t?t.value:null,a=e?e.getContext(n.value.outlet):null;if(o&&s.routeConfig===o.routeConfig){let c=Fg(o,s,s.routeConfig.runGuardsAndResolvers);c?i.canActivateChecks.push(new Qi(r)):(s.data=o.data,s._resolvedData=o._resolvedData),s.component?Er(n,t,a?a.children:null,r,i):Er(n,t,e,r,i),c&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new Fn(a.outlet.component,o))}else o&&Dr(t,a,i),i.canActivateChecks.push(new Qi(r)),s.component?Er(n,null,a?a.children:null,r,i):Er(n,null,e,r,i);return i}function Fg(n,t,e){if(typeof e=="function")return e(n,t);switch(e){case"pathParamsChange":return!nn(n.url,t.url);case"pathParamsOrQueryParamsChange":return!nn(n.url,t.url)||!$e(n.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!pa(n,t)||!$e(n.queryParams,t.queryParams);case"paramsChange":default:return!pa(n,t)}}function Dr(n,t,e){let r=Nn(n),i=n.value;Object.entries(r).forEach(([s,o])=>{i.component?t?Dr(o,t.children.getContext(s),e):Dr(o,null,e):Dr(o,t,e)}),i.component?t&&t.outlet&&t.outlet.isActivated?e.canDeactivateChecks.push(new Fn(t.outlet.component,i)):e.canDeactivateChecks.push(new Fn(null,i)):e.canDeactivateChecks.push(new Fn(null,i))}function Lr(n){return typeof n=="function"}function Lg(n){return typeof n=="boolean"}function Ug(n){return n&&Lr(n.canLoad)}function Bg(n){return n&&Lr(n.canActivate)}function jg(n){return n&&Lr(n.canActivateChild)}function Vg(n){return n&&Lr(n.canDeactivate)}function $g(n){return n&&Lr(n.canMatch)}function wd(n){return n instanceof Ll||n?.name==="EmptyError"}var Mi=Symbol("INITIAL_VALUE");function jn(){return ye(n=>cr(n.map(t=>t.pipe(Be(1),lr(Mi)))).pipe(E(t=>{for(let e of t)if(e!==!0){if(e===Mi)return Mi;if(e===!1||zg(e))return e}return!0}),ae(t=>t!==Mi),Be(1)))}function zg(n){return Rt(n)||n instanceof Bn}function Hg(n,t){return be(e=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:s,canDeactivateChecks:o}}=e;return o.length===0&&s.length===0?b(J(g({},e),{guardsResult:!0})):Wg(o,r,i,n).pipe(be(a=>a&&Lg(a)?Gg(r,s,n,t):b(a)),E(a=>J(g({},e),{guardsResult:a})))})}function Wg(n,t,e,r){return ee(n).pipe(be(i=>Zg(i.component,i.route,e,t,r)),Et(i=>i!==!0,!0))}function Gg(n,t,e,r){return ee(t).pipe(yt(i=>mi(Kg(i.route.parent,r),qg(i.route,r),Xg(n,i.path,e),Yg(n,i.route,e))),Et(i=>i!==!0,!0))}function qg(n,t){return n!==null&&t&&t(new qi(n)),b(!0)}function Kg(n,t){return n!==null&&t&&t(new Wi(n)),b(!0)}function Yg(n,t,e){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return b(!0);let i=r.map(s=>gi(()=>{let o=Fr(t)??e,a=$n(s,o),c=Bg(a)?a.canActivate(t,n):ge(o,()=>a(t,n));return ht(c).pipe(Et())}));return b(i).pipe(jn())}function Xg(n,t,e){let r=t[t.length-1],s=t.slice(0,t.length-1).reverse().map(o=>Ng(o)).filter(o=>o!==null).map(o=>gi(()=>{let a=o.guards.map(c=>{let l=Fr(o.node)??e,u=$n(c,l),p=jg(u)?u.canActivateChild(r,n):ge(l,()=>u(r,n));return ht(p).pipe(Et())});return b(a).pipe(jn())}));return b(s).pipe(jn())}function Zg(n,t,e,r,i){let s=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!s||s.length===0)return b(!0);let o=s.map(a=>{let c=Fr(t)??i,l=$n(a,c),u=Vg(l)?l.canDeactivate(n,t,e,r):ge(c,()=>l(n,t,e,r));return ht(u).pipe(Et())});return b(o).pipe(jn())}function Jg(n,t,e,r){let i=t.canLoad;if(i===void 0||i.length===0)return b(!0);let s=i.map(o=>{let a=$n(o,n),c=Ug(a)?a.canLoad(t,e):ge(n,()=>a(t,e));return ht(c)});return b(s).pipe(jn(),Id(r))}function Id(n){return Nl(V(t=>{if(typeof t!="boolean")throw Ji(n,t)}),E(t=>t===!0))}function Qg(n,t,e,r){let i=t.canMatch;if(!i||i.length===0)return b(!0);let s=i.map(o=>{let a=$n(o,n),c=$g(a)?a.canMatch(t,e):ge(n,()=>a(t,e));return ht(c)});return b(s).pipe(jn(),Id(r))}var Pr=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},Mr=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function Mn(n){return qt(new Pr(n))}function e_(n){return qt(new O(4e3,!1))}function t_(n){return qt(yd(!1,de.GuardRejected))}var _a=class{urlSerializer;urlTree;constructor(t,e){this.urlSerializer=t,this.urlTree=e}lineralizeSegments(t,e){let r=[],i=e.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return b(r);if(i.numberOfChildren>1||!i.children[S])return e_(`${t.redirectTo}`);i=i.children[S]}}applyRedirectCommands(t,e,r,i,s){return n_(e,i,s).pipe(E(o=>{if(o instanceof He)throw new Mr(o);let a=this.applyRedirectCreateUrlTree(o,this.urlSerializer.parse(o),t,r);if(o[0]==="/")throw new Mr(a);return a}))}applyRedirectCreateUrlTree(t,e,r,i){let s=this.createSegmentGroup(t,e.root,r,i);return new He(s,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(t,e){let r={};return Object.entries(t).forEach(([i,s])=>{if(typeof s=="string"&&s[0]===":"){let a=s.substring(1);r[i]=e[a]}else r[i]=s}),r}createSegmentGroup(t,e,r,i){let s=this.createSegments(t,e.segments,r,i),o={};return Object.entries(e.children).forEach(([a,c])=>{o[a]=this.createSegmentGroup(t,c,r,i)}),new M(s,o)}createSegments(t,e,r,i){return e.map(s=>s.path[0]===":"?this.findPosParam(t,s,i):this.findOrReturn(s,r))}findPosParam(t,e,r){let i=r[e.path.substring(1)];if(!i)throw new O(4001,!1);return i}findOrReturn(t,e){let r=0;for(let i of e){if(i.path===t.path)return e.splice(r),i;r++}return t}};function n_(n,t,e){if(typeof n=="string")return b(n);let r=n,{queryParams:i,fragment:s,routeConfig:o,url:a,outlet:c,params:l,data:u,title:p}=t;return ht(ge(e,()=>r({params:l,data:u,queryParams:i,fragment:s,routeConfig:o,url:a,outlet:c,title:p})))}var va={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function r_(n,t,e,r,i){let s=Dd(n,t,e);return s.matched?(r=Cg(t,r),Qg(r,t,e,i).pipe(E(o=>o===!0?s:g({},va)))):b(s)}function Dd(n,t,e){if(t.path==="**")return i_(e);if(t.path==="")return t.pathMatch==="full"&&(n.hasChildren()||e.length>0)?g({},va):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(t.matcher||Qu)(e,n,t);if(!i)return g({},va);let s={};Object.entries(i.posParams??{}).forEach(([a,c])=>{s[a]=c.path});let o=i.consumed.length>0?g(g({},s),i.consumed[i.consumed.length-1].parameters):s;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:o,positionalParamSegments:i.posParams??{}}}function i_(n){return{matched:!0,parameters:n.length>0?td(n).parameters:{},consumedSegments:n,remainingSegments:[],positionalParamSegments:{}}}function Xu(n,t,e,r){return e.length>0&&a_(n,e,r)?{segmentGroup:new M(t,o_(r,new M(e,n.children))),slicedSegments:[]}:e.length===0&&c_(n,e,r)?{segmentGroup:new M(n.segments,s_(n,e,r,n.children)),slicedSegments:e}:{segmentGroup:new M(n.segments,n.children),slicedSegments:e}}function s_(n,t,e,r){let i={};for(let s of e)if(ns(n,t,s)&&!r[Me(s)]){let o=new M([],{});i[Me(s)]=o}return g(g({},r),i)}function o_(n,t){let e={};e[S]=t;for(let r of n)if(r.path===""&&Me(r)!==S){let i=new M([],{});e[Me(r)]=i}return e}function a_(n,t,e){return e.some(r=>ns(n,t,r)&&Me(r)!==S)}function c_(n,t,e){return e.some(r=>ns(n,t,r))}function ns(n,t,e){return(n.hasChildren()||t.length>0)&&e.pathMatch==="full"?!1:e.path===""}function l_(n,t,e){return t.length===0&&!n.children[e]}var ba=class{};function u_(n,t,e,r,i,s,o="emptyOnly"){return new ya(n,t,e,r,i,o,s).recognize()}var d_=31,ya=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,e,r,i,s,o,a){this.injector=t,this.configLoader=e,this.rootComponentType=r,this.config=i,this.urlTree=s,this.paramsInheritanceStrategy=o,this.urlSerializer=a,this.applyRedirects=new _a(this.urlSerializer,this.urlTree)}noMatchError(t){return new O(4002,`'${t.segmentGroup}'`)}recognize(){let t=Xu(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(E(({children:e,rootSnapshot:r})=>{let i=new Ie(r,e),s=new kr("",i),o=ud(r,[],this.urlTree.queryParams,this.urlTree.fragment);return o.queryParams=this.urlTree.queryParams,s.url=this.urlSerializer.serialize(o),{state:s,tree:o}}))}match(t){let e=new rn([],Object.freeze({}),Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),S,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,S,e).pipe(E(r=>({children:r,rootSnapshot:e})),et(r=>{if(r instanceof Mr)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Pr?this.noMatchError(r):r}))}processSegmentGroup(t,e,r,i,s){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,e,r,s):this.processSegment(t,e,r,r.segments,i,!0,s).pipe(E(o=>o instanceof Ie?[o]:[]))}processChildren(t,e,r,i){let s=[];for(let o of Object.keys(r.children))o==="primary"?s.unshift(o):s.push(o);return ee(s).pipe(yt(o=>{let a=r.children[o],c=Sg(e,o);return this.processSegmentGroup(t,c,a,o,i)}),jl((o,a)=>(o.push(...a),o)),Eo(null),Bl(),be(o=>{if(o===null)return Mn(r);let a=Cd(o);return h_(a),b(a)}))}processSegment(t,e,r,i,s,o,a){return ee(e).pipe(yt(c=>this.processSegmentAgainstRoute(c._injector??t,e,c,r,i,s,o,a).pipe(et(l=>{if(l instanceof Pr)return b(null);throw l}))),Et(c=>!!c),et(c=>{if(wd(c))return l_(r,i,s)?b(new ba):Mn(r);throw c}))}processSegmentAgainstRoute(t,e,r,i,s,o,a,c){return Me(r)!==o&&(o===S||!ns(i,s,r))?Mn(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,i,r,s,o,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,i,e,r,s,o,c):Mn(i)}expandSegmentAgainstRouteUsingRedirect(t,e,r,i,s,o,a){let{matched:c,parameters:l,consumedSegments:u,positionalParamSegments:p,remainingSegments:f}=Dd(e,i,s);if(!c)return Mn(e);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>d_&&(this.allowRedirects=!1));let m=new rn(s,l,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Zu(i),Me(i),i.component??i._loadedComponent??null,i,Ju(i)),v=Zi(m,a,this.paramsInheritanceStrategy);return m.params=Object.freeze(v.params),m.data=Object.freeze(v.data),this.applyRedirects.applyRedirectCommands(u,i.redirectTo,p,m,t).pipe(ye(R=>this.applyRedirects.lineralizeSegments(i,R)),be(R=>this.processSegment(t,r,e,R.concat(f),o,!1,a)))}matchSegmentAgainstRoute(t,e,r,i,s,o){let a=r_(e,r,i,t,this.urlSerializer);return r.path==="**"&&(e.children={}),a.pipe(ye(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,i).pipe(ye(({routes:l})=>{let u=r._loadedInjector??t,{parameters:p,consumedSegments:f,remainingSegments:m}=c,v=new rn(f,p,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Zu(r),Me(r),r.component??r._loadedComponent??null,r,Ju(r)),C=Zi(v,o,this.paramsInheritanceStrategy);v.params=Object.freeze(C.params),v.data=Object.freeze(C.data);let{segmentGroup:R,slicedSegments:X}=Xu(e,f,m,l);if(X.length===0&&R.hasChildren())return this.processChildren(u,l,R,v).pipe(E(U=>new Ie(v,U)));if(l.length===0&&X.length===0)return b(new Ie(v,[]));let H=Me(r)===s;return this.processSegment(u,l,R,X,H?S:s,!0,v).pipe(E(U=>new Ie(v,U instanceof Ie?[U]:[])))}))):Mn(e)))}getChildConfig(t,e,r){return e.children?b({routes:e.children,injector:t}):e.loadChildren?e._loadedRoutes!==void 0?b({routes:e._loadedRoutes,injector:e._loadedInjector}):Jg(t,e,r,this.urlSerializer).pipe(be(i=>i?this.configLoader.loadChildren(t,e).pipe(V(s=>{e._loadedRoutes=s.routes,e._loadedInjector=s.injector})):t_(e))):b({routes:[],injector:t})}};function h_(n){n.sort((t,e)=>t.value.outlet===S?-1:e.value.outlet===S?1:t.value.outlet.localeCompare(e.value.outlet))}function f_(n){let t=n.value.routeConfig;return t&&t.path===""}function Cd(n){let t=[],e=new Set;for(let r of n){if(!f_(r)){t.push(r);continue}let i=t.find(s=>r.value.routeConfig===s.value.routeConfig);i!==void 0?(i.children.push(...r.children),e.add(i)):t.push(r)}for(let r of e){let i=Cd(r.children);t.push(new Ie(r.value,i))}return t.filter(r=>!e.has(r))}function Zu(n){return n.data||{}}function Ju(n){return n.resolve||{}}function p_(n,t,e,r,i,s){return be(o=>u_(n,t,e,r,o.extractedUrl,i,s).pipe(E(({state:a,tree:c})=>J(g({},o),{targetSnapshot:a,urlAfterRedirects:c}))))}function m_(n,t){return be(e=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=e;if(!i.length)return b(e);let s=new Set(i.map(c=>c.route)),o=new Set;for(let c of s)if(!o.has(c))for(let l of Sd(c))o.add(l);let a=0;return ee(o).pipe(yt(c=>s.has(c)?g_(c,r,n,t):(c.data=Zi(c,c.parent,n).resolve,b(void 0))),V(()=>a++),Io(1),be(c=>a===o.size?b(e):Qe))})}function Sd(n){let t=n.children.map(e=>Sd(e)).flat();return[n,...t]}function g_(n,t,e,r){let i=n.routeConfig,s=n._resolve;return i?.title!==void 0&&!_d(i)&&(s[Nr]=i.title),gi(()=>(n.data=Zi(n,n.parent,e).resolve,__(s,n,t,r).pipe(E(o=>(n._resolvedData=o,n.data=g(g({},n.data),o),null)))))}function __(n,t,e,r){let i=ca(n);if(i.length===0)return b({});let s={};return ee(i).pipe(be(o=>v_(n[o],t,e,r).pipe(Et(),V(a=>{if(a instanceof Bn)throw Ji(new on,a);s[o]=a}))),Io(1),E(()=>s),et(o=>wd(o)?Qe:qt(o)))}function v_(n,t,e,r){let i=Fr(t)??r,s=$n(n,i),o=s.resolve?s.resolve(t,e):ge(i,()=>s(t,e));return ht(o)}function oa(n){return ye(t=>{let e=n(t);return e?ee(e).pipe(E(()=>t)):b(t)})}var Ca=(()=>{class n{buildTitle(e){let r,i=e.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(s=>s.outlet===S);return r}getResolvedTitleForRoute(e){return e.data[Nr]}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:()=>d(Td),providedIn:"root"})}return n})(),Td=(()=>{class n extends Ca{title;constructor(e){super(),this.title=e}updateTitle(e){let r=this.buildTitle(e);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||n)(T(Hu))};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),zn=new y("",{providedIn:"root",factory:()=>({})}),Ur=new y(""),Rd=(()=>{class n{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=d(fu);loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return b(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let r=ht(e.loadComponent()).pipe(E(kd),V(s=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=s}),Kt(()=>{this.componentLoaders.delete(e)})),i=new po(r,()=>new A).pipe(fo());return this.componentLoaders.set(e,i),i}loadChildren(e,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return b({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let s=Ad(r,this.compiler,e,this.onLoadEndListener).pipe(Kt(()=>{this.childrenLoaders.delete(r)})),o=new po(s,()=>new A).pipe(fo());return this.childrenLoaders.set(r,o),o}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Ad(n,t,e,r){return ht(n.loadChildren()).pipe(E(kd),be(i=>i instanceof iu||Array.isArray(i)?b(i):ee(t.compileModuleAsync(i))),E(i=>{r&&r(n);let s,o,a=!1;return Array.isArray(i)?(o=i,a=!0):(s=i.create(e).injector,o=s.get(Ur,[],{optional:!0,self:!0}).flat()),{routes:o.map(Da),injector:s}}))}function b_(n){return n&&typeof n=="object"&&"default"in n}function kd(n){return b_(n)?n.default:n}var rs=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:()=>d(y_),providedIn:"root"})}return n})(),y_=(()=>{class n{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,r){return e}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Od=new y("");var Pd=new y(""),Md=(()=>{class n{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new A;transitionAbortWithErrorSubject=new A;configLoader=d(Rd);environmentInjector=d(le);destroyRef=d(bi);urlSerializer=d(xr);rootContexts=d(Vn);location=d(St);inputBindingEnabled=d(ts,{optional:!0})!==null;titleStrategy=d(Ca);options=d(zn,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=d(rs);createViewTransition=d(Od,{optional:!0});navigationErrorHandler=d(Pd,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>b(void 0);rootComponentType=null;destroyed=!1;constructor(){let e=i=>this.events.next(new zi(i)),r=i=>this.events.next(new Hi(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=e,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(e){let r=++this.navigationId;this.transitions?.next(J(g({},e),{extractedUrl:this.urlHandlingStrategy.extract(e.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(e){return this.transitions=new oe(null),this.transitions.pipe(ae(r=>r!==null),ye(r=>{let i=!1;return b(r).pipe(ye(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",de.SupersededByNewNavigation),Qe;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?J(g({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>s.abortController.abort()};let o=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=s.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!o&&a!=="reload"){let c="";return this.events.next(new ut(s.id,this.urlSerializer.serialize(s.rawUrl),c,Sr.IgnoredSameUrlNavigation)),s.resolve(!1),Qe}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return b(s).pipe(ye(c=>(this.events.next(new an(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?Qe:Promise.resolve(c))),p_(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),V(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=J(g({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let l=new Tr(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}));if(o&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:c,extractedUrl:l,source:u,restoredState:p,extras:f}=s,m=new an(c,this.urlSerializer.serialize(l),u,p);this.events.next(m);let v=md(this.rootComponentType).snapshot;return this.currentTransition=r=J(g({},s),{targetSnapshot:v,urlAfterRedirects:l,extras:J(g({},f),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,b(r)}else{let c="";return this.events.next(new ut(s.id,this.urlSerializer.serialize(s.extractedUrl),c,Sr.IgnoredByUrlHandlingStrategy)),s.resolve(!1),Qe}}),V(s=>{let o=new Bi(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(o)}),E(s=>(this.currentTransition=r=J(g({},s),{guards:Mg(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),Hg(this.environmentInjector,s=>this.events.next(s)),V(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw Ji(this.urlSerializer,s.guardsResult);let o=new ji(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(o)}),ae(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",de.GuardRejected),!1)),oa(s=>{if(s.guards.canActivateChecks.length!==0)return b(s).pipe(V(o=>{let a=new Vi(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot);this.events.next(a)}),ye(o=>{let a=!1;return b(o).pipe(m_(this.paramsInheritanceStrategy,this.environmentInjector),V({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(o,"",de.NoDataFromResolver)}}))}),V(o=>{let a=new $i(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot);this.events.next(a)}))}),oa(s=>{let o=a=>{let c=[];a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent&&c.push(this.configLoader.loadComponent(a.routeConfig).pipe(V(l=>{a.component=l}),E(()=>{})));for(let l of a.children)c.push(...o(l));return c};return cr(o(s.targetSnapshot.root)).pipe(Eo(null),Be(1))}),oa(()=>this.afterPreactivation()),ye(()=>{let{currentSnapshot:s,targetSnapshot:o}=r,a=this.createViewTransition?.(this.environmentInjector,s.root,o.root);return a?ee(a).pipe(E(()=>r)):b(r)}),E(s=>{let o=Rg(e.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=J(g({},s),{targetRouterState:o}),this.currentNavigation.targetRouterState=o,r}),V(()=>{this.events.next(new Rr)}),Pg(this.rootContexts,e.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),Be(1),je(new Se(s=>{let o=r.abortController.signal,a=()=>s.next();return o.addEventListener("abort",a),()=>o.removeEventListener("abort",a)}).pipe(ae(()=>!i&&!r.targetRouterState),V(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",de.Aborted)}))),V({next:s=>{i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Ne(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{i=!0}}),je(this.transitionAbortWithErrorSubject.pipe(V(s=>{throw s}))),Kt(()=>{i||this.cancelNavigationTransition(r,"",de.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),et(s=>{if(this.destroyed)return r.resolve(!1),Qe;if(i=!0,Ed(s))this.events.next(new ze(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),Og(s)?this.events.next(new Un(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let o=new Ln(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let a=ge(this.environmentInjector,()=>this.navigationErrorHandler?.(o));if(a instanceof Bn){let{message:c,cancellationCode:l}=Ji(this.urlSerializer,a);this.events.next(new ze(r.id,this.urlSerializer.serialize(r.extractedUrl),c,l)),this.events.next(new Un(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(o),s}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return Qe}))}))}cancelNavigationTransition(e,r,i){let s=new ze(e.id,this.urlSerializer.serialize(e.extractedUrl),r,i);this.events.next(s),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function E_(n){return n!==Ir}var Nd=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:()=>d(w_),providedIn:"root"})}return n})(),es=class{shouldDetach(t){return!1}store(t,e){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,e){return t.routeConfig===e.routeConfig}},w_=(()=>{class n extends es{static \u0275fac=(()=>{let e;return function(i){return(e||(e=Yt(n)))(i||n)}})();static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),xd=(()=>{class n{urlSerializer=d(xr);options=d(zn,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=d(St);urlHandlingStrategy=d(rs);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new He;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:e,initialUrl:r,targetBrowserUrl:i}){let s=e!==void 0?this.urlHandlingStrategy.merge(e,r):r,o=i??s;return o instanceof He?this.urlSerializer.serialize(o):o}commitTransition({targetRouterState:e,finalUrl:r,initialUrl:i}){r&&e?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,i),this.routerState=e):this.rawUrlTree=i}routerState=md(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:e}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e??this.rawUrlTree)}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:()=>d(I_),providedIn:"root"})}return n})(),I_=(()=>{class n extends xd{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{e(r.url,r.state,"popstate")})})}handleRouterEvent(e,r){e instanceof an?this.updateStateMemento():e instanceof ut?this.commitTransition(r):e instanceof Tr?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof Rr?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof ze&&e.code!==de.SupersededByNewNavigation&&e.code!==de.Redirect?this.restoreHistory(r):e instanceof Ln?this.restoreHistory(r,!0):e instanceof Ne&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,{extras:r,id:i}){let{replaceUrl:s,state:o}=r;if(this.location.isCurrentPathEqualTo(e)||s){let a=this.browserPageId,c=g(g({},o),this.generateNgRouterState(i,a));this.location.replaceState(e,"",c)}else{let a=g(g({},o),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(e,"",a)}}restoreHistory(e,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,s=this.currentPageId-i;s!==0?this.location.historyGo(s):this.getCurrentUrlTree()===e.finalUrl&&s===0&&(this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,r){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:r}:{navigationId:e}}static \u0275fac=(()=>{let e;return function(i){return(e||(e=Yt(n)))(i||n)}})();static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Sa(n,t){n.events.pipe(ae(e=>e instanceof Ne||e instanceof ze||e instanceof Ln||e instanceof ut),E(e=>e instanceof Ne||e instanceof ut?0:(e instanceof ze?e.code===de.Redirect||e.code===de.SupersededByNewNavigation:!1)?2:1),ae(e=>e!==2),Be(1)).subscribe(()=>{t()})}var D_={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},C_={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Hn=(()=>{class n{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=d(Ao);stateManager=d(xd);options=d(zn,{optional:!0})||{};pendingTasks=d(ql);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=d(Md);urlSerializer=d(xr);location=d(St);urlHandlingStrategy=d(rs);injector=d(le);_events=new A;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=d(Nd);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=d(Ur,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!d(ts,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}eventsSubscription=new me;subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,s=this.navigationTransitions.currentNavigation;if(i!==null&&s!==null){if(this.stateManager.handleRouterEvent(r,s),r instanceof ze&&r.code!==de.Redirect&&r.code!==de.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Ne)this.navigated=!0;else if(r instanceof Un){let o=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),c=g({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||E_(i.source)},o);this.scheduleNavigation(a,Ir,null,c,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}Dg(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortWithErrorSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Ir,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,r,i)=>{this.navigateToSyncWithBrowser(e,i,r)})}navigateToSyncWithBrowser(e,r,i){let s={replaceUrl:!0},o=i?.navigationId?i:null;if(i){let c=g({},i);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(s.state=c)}let a=this.parseUrl(e);this.scheduleNavigation(a,r,o,s).catch(c=>{this.injector.get(ur)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(Da),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,r={}){let{relativeTo:i,queryParams:s,fragment:o,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:o,u=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":u=g(g({},this.currentUrlTree.queryParams),s);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=s||null}u!==null&&(u=this.removeEmptyProps(u));let p;try{let f=i?i.snapshot:this.routerState.snapshot.root;p=dd(f)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),p=this.currentUrlTree.root}return hd(p,e,u,l??null)}navigateByUrl(e,r={skipLocationChange:!1}){let i=Rt(e)?e:this.parseUrl(e),s=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(s,Ir,null,r)}navigate(e,r={skipLocationChange:!1}){return S_(e),this.navigateByUrl(this.createUrlTree(e,r),r)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,r){let i;if(r===!0?i=g({},D_):r===!1?i=g({},C_):i=r,Rt(e))return Gu(this.currentUrlTree,e,i);let s=this.parseUrl(e);return Gu(this.currentUrlTree,s,i)}removeEmptyProps(e){return Object.entries(e).reduce((r,[i,s])=>(s!=null&&(r[i]=s),r),{})}scheduleNavigation(e,r,i,s,o){if(this.disposed)return Promise.resolve(!1);let a,c,l;o?(a=o.resolve,c=o.reject,l=o.promise):l=new Promise((p,f)=>{a=p,c=f});let u=this.pendingTasks.add();return Sa(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:s,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(p=>Promise.reject(p))}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function S_(n){for(let t=0;t<n.length;t++)if(n[t]==null)throw new O(4008,!1)}var is=(()=>{class n{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=tt(null);get href(){return No(this.reactiveHref)}set href(e){this.reactiveHref.set(e)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new A;applicationErrorHandler=d(ur);options=d(zn,{optional:!0});constructor(e,r,i,s,o,a){this.router=e,this.route=r,this.tabIndexAttribute=i,this.renderer=s,this.el=o,this.locationStrategy=a,this.reactiveHref.set(d(new Di("href"),{optional:!0}));let c=o.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area"||!!(typeof customElements=="object"&&customElements.get(c)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let e=this.preserveFragment,r=i=>i==="merge"||i==="preserve";e||=r(this.queryParamsHandling),e||=!this.queryParamsHandling&&!r(this.options?.defaultQueryParamsHandling),e&&(this.subscription=this.router.events.subscribe(i=>{i instanceof Ne&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(e){e==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(Rt(e)?this.routerLinkInput=e:this.routerLinkInput=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0"))}onClick(e,r,i,s,o){let a=this.urlTree;if(a===null||this.isAnchorElement&&(e!==0||r||i||s||o||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c)?.catch(l=>{this.applicationErrorHandler(l)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.reactiveHref.set(e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e))??"":null)}applyAttributeValue(e,r){let i=this.renderer,s=this.el.nativeElement;r!==null?i.setAttribute(s,e,r):i.removeAttribute(s,e)}get urlTree(){return this.routerLinkInput===null?null:Rt(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||n)(ue(Hn),ue(dt),Co("tabindex"),ue(Ve),ue(j),ue(On))};static \u0275dir=B({type:n,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(r,i){r&1&&fr("click",function(o){return i.onClick(o.button,o.ctrlKey,o.shiftKey,o.altKey,o.metaKey)}),r&2&&ot("href",i.reactiveHref(),ru)("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Y],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Y],replaceUrl:[2,"replaceUrl","replaceUrl",Y],routerLink:"routerLink"},features:[ke]})}return n})(),R_=(()=>{class n{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new Q;constructor(e,r,i,s,o){this.router=e,this.element=r,this.renderer=i,this.cdr=s,this.link=o,this.routerEventsSubscription=e.events.subscribe(a=>{a instanceof Ne&&this.update()})}ngAfterContentInit(){b(this.links.changes,b(null)).pipe(bo()).subscribe(e=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let e=[...this.links.toArray(),this.link].filter(r=>!!r).map(r=>r.onChanges);this.linkInputChangesSubscription=ee(e).pipe(bo()).subscribe(r=>{this._isActive!==this.isLinkActive(this.router)(r)&&this.update()})}set routerLinkActive(e){let r=Array.isArray(e)?e:e.split(" ");this.classes=r.filter(i=>!!i)}ngOnChanges(e){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let e=this.hasActiveLinks();this.classes.forEach(r=>{e?this.renderer.addClass(this.element.nativeElement,r):this.renderer.removeClass(this.element.nativeElement,r)}),e&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==e&&(this._isActive=e,this.cdr.markForCheck(),this.isActiveChange.emit(e))})}isLinkActive(e){let r=A_(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return i=>{let s=i.urlTree;return s?e.isActive(s,r):!1}}hasActiveLinks(){let e=this.isLinkActive(this.router);return this.link&&e(this.link)||this.links.some(e)}static \u0275fac=function(r){return new(r||n)(ue(Hn),ue(j),ue(Ve),ue(Dt),ue(is,8))};static \u0275dir=B({type:n,selectors:[["","routerLinkActive",""]],contentQueries:function(r,i,s){if(r&1&&hu(s,is,5),r&2){let o;An(o=kn())&&(i.links=o)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[ke]})}return n})();function A_(n){return!!n.paths}var k_=new y("");function O_(n,...t){return Te([{provide:Ur,multi:!0,useValue:n},[],{provide:dt,useFactory:P_,deps:[Hn]},{provide:cu,multi:!0,useFactory:M_},t.map(e=>e.\u0275providers)])}function P_(n){return n.routerState.root}function M_(){let n=d(k);return t=>{let e=n.get(Jt);if(t!==e.components[0])return;let r=n.get(Hn),i=n.get(N_);n.get(x_)===1&&r.initialNavigation(),n.get(F_,null,{optional:!0})?.setUpPreloading(),n.get(k_,null,{optional:!0})?.init(),r.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var N_=new y("",{factory:()=>new A}),x_=new y("",{providedIn:"root",factory:()=>1});var F_=new y("");function Br(n){return n.buttons===0||n.detail===0}function jr(n){let t=n.touches&&n.touches[0]||n.changedTouches&&n.changedTouches[0];return!!t&&t.identifier===-1&&(t.radiusX==null||t.radiusX===1)&&(t.radiusY==null||t.radiusY===1)}var Ta;function Fd(){if(Ta==null){let n=typeof document<"u"?document.head:null;Ta=!!(n&&(n.createShadowRoot||n.attachShadow))}return Ta}function Ra(n){if(Fd()){let t=n.getRootNode?n.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&t instanceof ShadowRoot)return t}return null}function he(n){return n.composedPath?n.composedPath()[0]:n.target}var Aa;try{Aa=typeof Intl<"u"&&Intl.v8BreakIterator}catch{Aa=!1}var $=(()=>{class n{_platformId=d(nt);isBrowser=this._platformId?Ru(this._platformId):typeof document=="object"&&!!document;EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent);TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent);BLINK=this.isBrowser&&!!(window.chrome||Aa)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT;WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT;IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window);FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent);ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT;SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT;constructor(){}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Vr;function Ld(){if(Vr==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>Vr=!0}))}finally{Vr=Vr||!1}return Vr}function Wn(n){return Ld()?n:!!n.capture}function Ud(n,t=0){return Bd(n)?Number(n):arguments.length===2?t:0}function Bd(n){return!isNaN(parseFloat(n))&&!isNaN(Number(n))}function We(n){return n instanceof j?n.nativeElement:n}var jd=new y("cdk-input-modality-detector-options"),Vd={ignoreKeys:[18,17,224,91,16]},$d=650,ka={passive:!0,capture:!0},zd=(()=>{class n{_platform=d($);_listenerCleanups;modalityDetected;modalityChanged;get mostRecentModality(){return this._modality.value}_mostRecentTarget=null;_modality=new oe(null);_options;_lastTouchMs=0;_onKeydown=e=>{this._options?.ignoreKeys?.some(r=>r===e.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=he(e))};_onMousedown=e=>{Date.now()-this._lastTouchMs<$d||(this._modality.next(Br(e)?"keyboard":"mouse"),this._mostRecentTarget=he(e))};_onTouchstart=e=>{if(jr(e)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=he(e)};constructor(){let e=d(D),r=d(w),i=d(jd,{optional:!0});if(this._options=g(g({},Vd),i),this.modalityDetected=this._modality.pipe(vi(1)),this.modalityChanged=this.modalityDetected.pipe(wo()),this._platform.isBrowser){let s=d(we).createRenderer(null,null);this._listenerCleanups=e.runOutsideAngular(()=>[s.listen(r,"keydown",this._onKeydown,ka),s.listen(r,"mousedown",this._onMousedown,ka),s.listen(r,"touchstart",this._onTouchstart,ka)])}}ngOnDestroy(){this._modality.complete(),this._listenerCleanups?.forEach(e=>e())}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),$r=function(n){return n[n.IMMEDIATE=0]="IMMEDIATE",n[n.EVENTUAL=1]="EVENTUAL",n}($r||{}),Hd=new y("cdk-focus-monitor-default-options"),ss=Wn({passive:!0,capture:!0}),Oa=(()=>{class n{_ngZone=d(D);_platform=d($);_inputModalityDetector=d(zd);_origin=null;_lastFocusOrigin;_windowFocused=!1;_windowFocusTimeoutId;_originTimeoutId;_originFromTouchInteraction=!1;_elementInfo=new Map;_monitoredElementCount=0;_rootNodeFocusListenerCount=new Map;_detectionMode;_windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=setTimeout(()=>this._windowFocused=!1)};_document=d(w,{optional:!0});_stopInputModalityDetector=new A;constructor(){let e=d(Hd,{optional:!0});this._detectionMode=e?.detectionMode||$r.IMMEDIATE}_rootNodeFocusAndBlurListener=e=>{let r=he(e);for(let i=r;i;i=i.parentElement)e.type==="focus"?this._onFocus(e,i):this._onBlur(e,i)};monitor(e,r=!1){let i=We(e);if(!this._platform.isBrowser||i.nodeType!==1)return b();let s=Ra(i)||this._getDocument(),o=this._elementInfo.get(i);if(o)return r&&(o.checkChildren=!0),o.subject;let a={checkChildren:r,subject:new A,rootNode:s};return this._elementInfo.set(i,a),this._registerGlobalListeners(a),a.subject}stopMonitoring(e){let r=We(e),i=this._elementInfo.get(r);i&&(i.subject.complete(),this._setClasses(r),this._elementInfo.delete(r),this._removeGlobalListeners(i))}focusVia(e,r,i){let s=We(e),o=this._getDocument().activeElement;s===o?this._getClosestElementsInfo(s).forEach(([a,c])=>this._originChanged(a,r,c)):(this._setOrigin(r),typeof s.focus=="function"&&s.focus(i))}ngOnDestroy(){this._elementInfo.forEach((e,r)=>this.stopMonitoring(r))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(e){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(e)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:e&&this._isLastInteractionFromInputLabel(e)?"mouse":"program"}_shouldBeAttributedToTouch(e){return this._detectionMode===$r.EVENTUAL||!!e?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(e,r){e.classList.toggle("cdk-focused",!!r),e.classList.toggle("cdk-touch-focused",r==="touch"),e.classList.toggle("cdk-keyboard-focused",r==="keyboard"),e.classList.toggle("cdk-mouse-focused",r==="mouse"),e.classList.toggle("cdk-program-focused",r==="program")}_setOrigin(e,r=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=e,this._originFromTouchInteraction=e==="touch"&&r,this._detectionMode===$r.IMMEDIATE){clearTimeout(this._originTimeoutId);let i=this._originFromTouchInteraction?$d:1;this._originTimeoutId=setTimeout(()=>this._origin=null,i)}})}_onFocus(e,r){let i=this._elementInfo.get(r),s=he(e);!i||!i.checkChildren&&r!==s||this._originChanged(r,this._getFocusOrigin(s),i)}_onBlur(e,r){let i=this._elementInfo.get(r);!i||i.checkChildren&&e.relatedTarget instanceof Node&&r.contains(e.relatedTarget)||(this._setClasses(r),this._emitOrigin(i,null))}_emitOrigin(e,r){e.subject.observers.length&&this._ngZone.run(()=>e.subject.next(r))}_registerGlobalListeners(e){if(!this._platform.isBrowser)return;let r=e.rootNode,i=this._rootNodeFocusListenerCount.get(r)||0;i||this._ngZone.runOutsideAngular(()=>{r.addEventListener("focus",this._rootNodeFocusAndBlurListener,ss),r.addEventListener("blur",this._rootNodeFocusAndBlurListener,ss)}),this._rootNodeFocusListenerCount.set(r,i+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(je(this._stopInputModalityDetector)).subscribe(s=>{this._setOrigin(s,!0)}))}_removeGlobalListeners(e){let r=e.rootNode;if(this._rootNodeFocusListenerCount.has(r)){let i=this._rootNodeFocusListenerCount.get(r);i>1?this._rootNodeFocusListenerCount.set(r,i-1):(r.removeEventListener("focus",this._rootNodeFocusAndBlurListener,ss),r.removeEventListener("blur",this._rootNodeFocusAndBlurListener,ss),this._rootNodeFocusListenerCount.delete(r))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(e,r,i){this._setClasses(e,r),this._emitOrigin(i,r),this._lastFocusOrigin=r}_getClosestElementsInfo(e){let r=[];return this._elementInfo.forEach((i,s)=>{(s===e||i.checkChildren&&s.contains(e))&&r.push([s,i])}),r}_isLastInteractionFromInputLabel(e){let{_mostRecentTarget:r,mostRecentModality:i}=this._inputModalityDetector;if(i!=="mouse"||!r||r===e||e.nodeName!=="INPUT"&&e.nodeName!=="TEXTAREA"||e.disabled)return!1;let s=e.labels;if(s){for(let o=0;o<s.length;o++)if(s[o].contains(r))return!0}return!1}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var os=new WeakMap,Ge=(()=>{class n{_appRef;_injector=d(k);_environmentInjector=d(le);load(e){let r=this._appRef=this._appRef||this._injector.get(Jt),i=os.get(r);i||(i={loaders:new Set,refs:[]},os.set(r,i),r.onDestroy(()=>{os.get(r)?.refs.forEach(s=>s.destroy()),os.delete(r)})),i.loaders.has(e)||(i.loaders.add(e),i.refs.push(Ci(e,{environmentInjector:this._environmentInjector})))}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Wd=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["ng-component"]],exportAs:["cdkVisuallyHidden"],decls:0,vars:0,template:function(r,i){},styles:[`.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}
`],encapsulation:2,changeDetection:0})}return n})();function Gn(n){return Array.isArray(n)?n:[n]}var Gd=new Set,cn,as=(()=>{class n{_platform=d($);_nonce=d(hr,{optional:!0});_matchMedia;constructor(){this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):U_}matchMedia(e){return(this._platform.WEBKIT||this._platform.BLINK)&&L_(e,this._nonce),this._matchMedia(e)}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function L_(n,t){if(!Gd.has(n))try{cn||(cn=document.createElement("style"),t&&cn.setAttribute("nonce",t),cn.setAttribute("type","text/css"),document.head.appendChild(cn)),cn.sheet&&(cn.sheet.insertRule(`@media ${n} {body{ }}`,0),Gd.add(n))}catch(e){console.error(e)}}function U_(n){return{matches:n==="all"||n==="",media:n,addListener:()=>{},removeListener:()=>{}}}var zr=(()=>{class n{_mediaMatcher=d(as);_zone=d(D);_queries=new Map;_destroySubject=new A;constructor(){}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(e){return qd(Gn(e)).some(i=>this._registerQuery(i).mql.matches)}observe(e){let i=qd(Gn(e)).map(o=>this._registerQuery(o).observable),s=cr(i);return s=mi(s.pipe(Be(1)),s.pipe(vi(1),_i(0))),s.pipe(E(o=>{let a={matches:!1,breakpoints:{}};return o.forEach(({matches:c,query:l})=>{a.matches=a.matches||c,a.breakpoints[l]=c}),a}))}_registerQuery(e){if(this._queries.has(e))return this._queries.get(e);let r=this._mediaMatcher.matchMedia(e),s={observable:new Se(o=>{let a=c=>this._zone.run(()=>o.next(c));return r.addListener(a),()=>{r.removeListener(a)}}).pipe(lr(r),E(({matches:o})=>({query:e,matches:o})),je(this._destroySubject)),mql:r};return this._queries.set(e,s),s}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function qd(n){return n.map(t=>t.split(",")).reduce((t,e)=>t.concat(e)).map(t=>t.trim())}var B_=(()=>{class n{create(e){return typeof MutationObserver>"u"?null:new MutationObserver(e)}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Kd=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({providers:[B_]})}return n})();var Zd=new y("liveAnnouncerElement",{providedIn:"root",factory:Jd});function Jd(){return null}var Qd=new y("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),j_=0,Ma=(()=>{class n{_ngZone=d(D);_defaultOptions=d(Qd,{optional:!0});_liveElement;_document=d(w);_previousTimeout;_currentPromise;_currentResolve;constructor(){let e=d(Zd,{optional:!0});this._liveElement=e||this._createLiveElement()}announce(e,...r){let i=this._defaultOptions,s,o;return r.length===1&&typeof r[0]=="number"?o=r[0]:[s,o]=r,this.clear(),clearTimeout(this._previousTimeout),s||(s=i&&i.politeness?i.politeness:"polite"),o==null&&i&&(o=i.duration),this._liveElement.setAttribute("aria-live",s),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(a=>this._currentResolve=a)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=e,typeof o=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),o)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let e="cdk-live-announcer-element",r=this._document.getElementsByClassName(e),i=this._document.createElement("div");for(let s=0;s<r.length;s++)r[s].remove();return i.classList.add(e),i.classList.add("cdk-visually-hidden"),i.setAttribute("aria-atomic","true"),i.setAttribute("aria-live","polite"),i.id=`cdk-live-announcer-${j_++}`,this._document.body.appendChild(i),i}_exposeAnnouncerToModals(e){let r=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let i=0;i<r.length;i++){let s=r[i],o=s.getAttribute("aria-owns");o?o.indexOf(e)===-1&&s.setAttribute("aria-owns",o+" "+e):s.setAttribute("aria-owns",e)}}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var At=function(n){return n[n.NONE=0]="NONE",n[n.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",n[n.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",n}(At||{}),Yd="cdk-high-contrast-black-on-white",Xd="cdk-high-contrast-white-on-black",Pa="cdk-high-contrast-active",cs=(()=>{class n{_platform=d($);_hasCheckedHighContrastMode;_document=d(w);_breakpointSubscription;constructor(){this._breakpointSubscription=d(zr).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return At.NONE;let e=this._document.createElement("div");e.style.backgroundColor="rgb(1,2,3)",e.style.position="absolute",this._document.body.appendChild(e);let r=this._document.defaultView||window,i=r&&r.getComputedStyle?r.getComputedStyle(e):null,s=(i&&i.backgroundColor||"").replace(/ /g,"");switch(e.remove(),s){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return At.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return At.BLACK_ON_WHITE}return At.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let e=this._document.body.classList;e.remove(Pa,Yd,Xd),this._hasCheckedHighContrastMode=!0;let r=this.getHighContrastMode();r===At.BLACK_ON_WHITE?e.add(Pa,Yd):r===At.WHITE_ON_BLACK&&e.add(Pa,Xd)}}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),V_=(()=>{class n{constructor(){d(cs)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({imports:[Kd]})}return n})();var Na={},Hr=(()=>{class n{_appId=d(Xt);getId(e){return this._appId!=="ng"&&(e+=this._appId),Na.hasOwnProperty(e)||(Na[e]=0),`${e}${Na[e]++}`}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var $_=200,ls=class{_letterKeyStream=new A;_items=[];_selectedItemIndex=-1;_pressedLetters=[];_skipPredicateFn;_selectedItem=new A;selectedItem=this._selectedItem;constructor(t,e){let r=typeof e?.debounceInterval=="number"?e.debounceInterval:$_;e?.skipPredicate&&(this._skipPredicateFn=e.skipPredicate),this.setItems(t),this._setupKeyHandler(r)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(t){this._selectedItemIndex=t}setItems(t){this._items=t}handleKey(t){let e=t.keyCode;t.key&&t.key.length===1?this._letterKeyStream.next(t.key.toLocaleUpperCase()):(e>=65&&e<=90||e>=48&&e<=57)&&this._letterKeyStream.next(String.fromCharCode(e))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(t){this._letterKeyStream.pipe(V(e=>this._pressedLetters.push(e)),_i(t),ae(()=>this._pressedLetters.length>0),E(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(e=>{for(let r=1;r<this._items.length+1;r++){let i=(this._selectedItemIndex+r)%this._items.length,s=this._items[i];if(!this._skipPredicateFn?.(s)&&s.getLabel?.().toLocaleUpperCase().trim().indexOf(e)===0){this._selectedItem.next(s);break}}this._pressedLetters=[]})}};function us(n,...t){return t.length?t.some(e=>n[e]):n.altKey||n.shiftKey||n.ctrlKey||n.metaKey}var qn=class{_items;_activeItemIndex=tt(-1);_activeItem=tt(null);_wrap=!1;_typeaheadSubscription=me.EMPTY;_itemChangesSubscription;_vertical=!0;_horizontal;_allowedModifierKeys=[];_homeAndEnd=!1;_pageUpAndDown={enabled:!1,delta:10};_effectRef;_typeahead;_skipPredicateFn=t=>t.disabled;constructor(t,e){this._items=t,t instanceof So?this._itemChangesSubscription=t.changes.subscribe(r=>this._itemsChanged(r.toArray())):Do(t)&&(this._effectRef=pu(()=>this._itemsChanged(t()),{injector:e}))}tabOut=new A;change=new A;skipPredicate(t){return this._skipPredicateFn=t,this}withWrap(t=!0){return this._wrap=t,this}withVerticalOrientation(t=!0){return this._vertical=t,this}withHorizontalOrientation(t){return this._horizontal=t,this}withAllowedModifierKeys(t){return this._allowedModifierKeys=t,this}withTypeAhead(t=200){this._typeaheadSubscription.unsubscribe();let e=this._getItemsArray();return this._typeahead=new ls(e,{debounceInterval:typeof t=="number"?t:void 0,skipPredicate:r=>this._skipPredicateFn(r)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(r=>{this.setActiveItem(r)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(t=!0){return this._homeAndEnd=t,this}withPageUpDown(t=!0,e=10){return this._pageUpAndDown={enabled:t,delta:e},this}setActiveItem(t){let e=this._activeItem();this.updateActiveItem(t),this._activeItem()!==e&&this.change.next(this._activeItemIndex())}onKeydown(t){let e=t.keyCode,i=["altKey","ctrlKey","metaKey","shiftKey"].every(s=>!t[s]||this._allowedModifierKeys.indexOf(s)>-1);switch(e){case 9:this.tabOut.next();return;case 40:if(this._vertical&&i){this.setNextItemActive();break}else return;case 38:if(this._vertical&&i){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&i){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&i){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&i){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&i){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&i){let s=this._activeItemIndex()-this._pageUpAndDown.delta;this._setActiveItemByIndex(s>0?s:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&i){let s=this._activeItemIndex()+this._pageUpAndDown.delta,o=this._getItemsArray().length;this._setActiveItemByIndex(s<o?s:o-1,-1);break}else return;default:(i||us(t,"shiftKey"))&&this._typeahead?.handleKey(t);return}this._typeahead?.reset(),t.preventDefault()}get activeItemIndex(){return this._activeItemIndex()}get activeItem(){return this._activeItem()}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex()<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex()<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(t){let e=this._getItemsArray(),r=typeof t=="number"?t:e.indexOf(t),i=e[r];this._activeItem.set(i??null),this._activeItemIndex.set(r),this._typeahead?.setCurrentSelectedItemIndex(r)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(t){this._wrap?this._setActiveInWrapMode(t):this._setActiveInDefaultMode(t)}_setActiveInWrapMode(t){let e=this._getItemsArray();for(let r=1;r<=e.length;r++){let i=(this._activeItemIndex()+t*r+e.length)%e.length,s=e[i];if(!this._skipPredicateFn(s)){this.setActiveItem(i);return}}}_setActiveInDefaultMode(t){this._setActiveItemByIndex(this._activeItemIndex()+t,t)}_setActiveItemByIndex(t,e){let r=this._getItemsArray();if(r[t]){for(;this._skipPredicateFn(r[t]);)if(t+=e,!r[t])return;this.setActiveItem(t)}}_getItemsArray(){return Do(this._items)?this._items():this._items instanceof So?this._items.toArray():this._items}_itemsChanged(t){this._typeahead?.setItems(t);let e=this._activeItem();if(e){let r=t.indexOf(e);r>-1&&r!==this._activeItemIndex()&&(this._activeItemIndex.set(r),this._typeahead?.setCurrentSelectedItemIndex(r))}}};var xa=class extends qn{setActiveItem(t){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(t),this.activeItem&&this.activeItem.setActiveStyles()}};var Fa=class extends qn{_origin="program";setFocusOrigin(t){return this._origin=t,this}setActiveItem(t){super.setActiveItem(t),this.activeItem&&this.activeItem.focus(this._origin)}};var th=" ";function z_(n,t,e){let r=hs(n,t);e=e.trim(),!r.some(i=>i.trim()===e)&&(r.push(e),n.setAttribute(t,r.join(th)))}function H_(n,t,e){let r=hs(n,t);e=e.trim();let i=r.filter(s=>s!==e);i.length?n.setAttribute(t,i.join(th)):n.removeAttribute(t)}function hs(n,t){return n.getAttribute(t)?.match(/\S+/g)??[]}var nh="cdk-describedby-message",ds="cdk-describedby-host",Ua=0,pA=(()=>{class n{_platform=d($);_document=d(w);_messageRegistry=new Map;_messagesContainer=null;_id=`${Ua++}`;constructor(){d(Ge).load(Wd),this._id=d(Xt)+"-"+Ua++}describe(e,r,i){if(!this._canBeDescribed(e,r))return;let s=La(r,i);typeof r!="string"?(eh(r,this._id),this._messageRegistry.set(s,{messageElement:r,referenceCount:0})):this._messageRegistry.has(s)||this._createMessageElement(r,i),this._isElementDescribedByMessage(e,s)||this._addMessageReference(e,s)}removeDescription(e,r,i){if(!r||!this._isElementNode(e))return;let s=La(r,i);if(this._isElementDescribedByMessage(e,s)&&this._removeMessageReference(e,s),typeof r=="string"){let o=this._messageRegistry.get(s);o&&o.referenceCount===0&&this._deleteMessageElement(s)}this._messagesContainer?.childNodes.length===0&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){let e=this._document.querySelectorAll(`[${ds}="${this._id}"]`);for(let r=0;r<e.length;r++)this._removeCdkDescribedByReferenceIds(e[r]),e[r].removeAttribute(ds);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(e,r){let i=this._document.createElement("div");eh(i,this._id),i.textContent=e,r&&i.setAttribute("role",r),this._createMessagesContainer(),this._messagesContainer.appendChild(i),this._messageRegistry.set(La(e,r),{messageElement:i,referenceCount:0})}_deleteMessageElement(e){this._messageRegistry.get(e)?.messageElement?.remove(),this._messageRegistry.delete(e)}_createMessagesContainer(){if(this._messagesContainer)return;let e="cdk-describedby-message-container",r=this._document.querySelectorAll(`.${e}[platform="server"]`);for(let s=0;s<r.length;s++)r[s].remove();let i=this._document.createElement("div");i.style.visibility="hidden",i.classList.add(e),i.classList.add("cdk-visually-hidden"),this._platform.isBrowser||i.setAttribute("platform","server"),this._document.body.appendChild(i),this._messagesContainer=i}_removeCdkDescribedByReferenceIds(e){let r=hs(e,"aria-describedby").filter(i=>i.indexOf(nh)!=0);e.setAttribute("aria-describedby",r.join(" "))}_addMessageReference(e,r){let i=this._messageRegistry.get(r);z_(e,"aria-describedby",i.messageElement.id),e.setAttribute(ds,this._id),i.referenceCount++}_removeMessageReference(e,r){let i=this._messageRegistry.get(r);i.referenceCount--,H_(e,"aria-describedby",i.messageElement.id),e.removeAttribute(ds)}_isElementDescribedByMessage(e,r){let i=hs(e,"aria-describedby"),s=this._messageRegistry.get(r),o=s&&s.messageElement.id;return!!o&&i.indexOf(o)!=-1}_canBeDescribed(e,r){if(!this._isElementNode(e))return!1;if(r&&typeof r=="object")return!0;let i=r==null?"":`${r}`.trim(),s=e.getAttribute("aria-label");return i?!s||s.trim()!==i:!1}_isElementNode(e){return e.nodeType===this._document.ELEMENT_NODE}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function La(n,t){return typeof n=="string"?`${t||""}/${n}`:n}function eh(n,t){n.id||(n.id=`${nh}-${t}-${Ua++}`)}var ln;function rh(){if(ln==null){if(typeof document!="object"||!document||typeof Element!="function"||!Element)return ln=!1,ln;if("scrollBehavior"in document.documentElement.style)ln=!0;else{let n=Element.prototype.scrollTo;n?ln=!/\{\s*\[native code\]\s*\}/.test(n.toString()):ln=!1}}return ln}function Ba(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}var Kn,ih=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"];function CA(){if(Kn)return Kn;if(typeof document!="object"||!document)return Kn=new Set(ih),Kn;let n=document.createElement("input");return Kn=new Set(ih.filter(t=>(n.setAttribute("type",t),n.type===t))),Kn}var sh={XSmall:"(max-width: 599.98px)",Small:"(min-width: 600px) and (max-width: 959.98px)",Medium:"(min-width: 960px) and (max-width: 1279.98px)",Large:"(min-width: 1280px) and (max-width: 1919.98px)",XLarge:"(min-width: 1920px)",Handset:"(max-width: 599.98px) and (orientation: portrait), (max-width: 959.98px) and (orientation: landscape)",Tablet:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), (min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",Web:"(min-width: 840px) and (orientation: portrait), (min-width: 1280px) and (orientation: landscape)",HandsetPortrait:"(max-width: 599.98px) and (orientation: portrait)",TabletPortrait:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)",WebPortrait:"(min-width: 840px) and (orientation: portrait)",HandsetLandscape:"(max-width: 959.98px) and (orientation: landscape)",TabletLandscape:"(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",WebLandscape:"(min-width: 1280px) and (orientation: landscape)"};var W_=new y("MATERIAL_ANIMATIONS");function ft(){return d(W_,{optional:!0})?.animationsDisabled||d(yi,{optional:!0})==="NoopAnimations"?!0:d(as).matchMedia("(prefers-reduced-motion)").matches}function W(n){return n==null?"":typeof n=="string"?n:`${n}px`}function FA(n){return n!=null&&`${n}`!="false"}var Re=function(n){return n[n.FADING_IN=0]="FADING_IN",n[n.VISIBLE=1]="VISIBLE",n[n.FADING_OUT=2]="FADING_OUT",n[n.HIDDEN=3]="HIDDEN",n}(Re||{}),ja=class{_renderer;element;config;_animationForciblyDisabledThroughCss;state=Re.HIDDEN;constructor(t,e,r,i=!1){this._renderer=t,this.element=e,this.config=r,this._animationForciblyDisabledThroughCss=i}fadeOut(){this._renderer.fadeOutRipple(this)}},oh=Wn({passive:!0,capture:!0}),Va=class{_events=new Map;addHandler(t,e,r,i){let s=this._events.get(e);if(s){let o=s.get(r);o?o.add(i):s.set(r,new Set([i]))}else this._events.set(e,new Map([[r,new Set([i])]])),t.runOutsideAngular(()=>{document.addEventListener(e,this._delegateEventHandler,oh)})}removeHandler(t,e,r){let i=this._events.get(t);if(!i)return;let s=i.get(e);s&&(s.delete(r),s.size===0&&i.delete(e),i.size===0&&(this._events.delete(t),document.removeEventListener(t,this._delegateEventHandler,oh)))}_delegateEventHandler=t=>{let e=he(t);e&&this._events.get(t.type)?.forEach((r,i)=>{(i===e||i.contains(e))&&r.forEach(s=>s.handleEvent(t))})}},Wr={enterDuration:225,exitDuration:150},G_=800,ah=Wn({passive:!0,capture:!0}),ch=["mousedown","touchstart"],lh=["mouseup","mouseleave","touchend","touchcancel"],q_=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["ng-component"]],hostAttrs:["mat-ripple-style-loader",""],decls:0,vars:0,template:function(r,i){},styles:[`.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}
`],encapsulation:2,changeDetection:0})}return n})(),Gr=class n{_target;_ngZone;_platform;_containerElement;_triggerElement;_isPointerDown=!1;_activeRipples=new Map;_mostRecentTransientRipple;_lastTouchStartEvent;_pointerUpEventsRegistered=!1;_containerRect;static _eventManager=new Va;constructor(t,e,r,i,s){this._target=t,this._ngZone=e,this._platform=i,i.isBrowser&&(this._containerElement=We(r)),s&&s.get(Ge).load(q_)}fadeInRipple(t,e,r={}){let i=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),s=g(g({},Wr),r.animation);r.centered&&(t=i.left+i.width/2,e=i.top+i.height/2);let o=r.radius||K_(t,e,i),a=t-i.left,c=e-i.top,l=s.enterDuration,u=document.createElement("div");u.classList.add("mat-ripple-element"),u.style.left=`${a-o}px`,u.style.top=`${c-o}px`,u.style.height=`${o*2}px`,u.style.width=`${o*2}px`,r.color!=null&&(u.style.backgroundColor=r.color),u.style.transitionDuration=`${l}ms`,this._containerElement.appendChild(u);let p=window.getComputedStyle(u),f=p.transitionProperty,m=p.transitionDuration,v=f==="none"||m==="0s"||m==="0s, 0s"||i.width===0&&i.height===0,C=new ja(this,u,r,v);u.style.transform="scale3d(1, 1, 1)",C.state=Re.FADING_IN,r.persistent||(this._mostRecentTransientRipple=C);let R=null;return!v&&(l||s.exitDuration)&&this._ngZone.runOutsideAngular(()=>{let X=()=>{R&&(R.fallbackTimer=null),clearTimeout(U),this._finishRippleTransition(C)},H=()=>this._destroyRipple(C),U=setTimeout(H,l+100);u.addEventListener("transitionend",X),u.addEventListener("transitioncancel",H),R={onTransitionEnd:X,onTransitionCancel:H,fallbackTimer:U}}),this._activeRipples.set(C,R),(v||!l)&&this._finishRippleTransition(C),C}fadeOutRipple(t){if(t.state===Re.FADING_OUT||t.state===Re.HIDDEN)return;let e=t.element,r=g(g({},Wr),t.config.animation);e.style.transitionDuration=`${r.exitDuration}ms`,e.style.opacity="0",t.state=Re.FADING_OUT,(t._animationForciblyDisabledThroughCss||!r.exitDuration)&&this._finishRippleTransition(t)}fadeOutAll(){this._getActiveRipples().forEach(t=>t.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(t=>{t.config.persistent||t.fadeOut()})}setupTriggerEvents(t){let e=We(t);!this._platform.isBrowser||!e||e===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=e,ch.forEach(r=>{n._eventManager.addHandler(this._ngZone,r,e,this)}))}handleEvent(t){t.type==="mousedown"?this._onMousedown(t):t.type==="touchstart"?this._onTouchStart(t):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{lh.forEach(e=>{this._triggerElement.addEventListener(e,this,ah)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(t){t.state===Re.FADING_IN?this._startFadeOutTransition(t):t.state===Re.FADING_OUT&&this._destroyRipple(t)}_startFadeOutTransition(t){let e=t===this._mostRecentTransientRipple,{persistent:r}=t.config;t.state=Re.VISIBLE,!r&&(!e||!this._isPointerDown)&&t.fadeOut()}_destroyRipple(t){let e=this._activeRipples.get(t)??null;this._activeRipples.delete(t),this._activeRipples.size||(this._containerRect=null),t===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),t.state=Re.HIDDEN,e!==null&&(t.element.removeEventListener("transitionend",e.onTransitionEnd),t.element.removeEventListener("transitioncancel",e.onTransitionCancel),e.fallbackTimer!==null&&clearTimeout(e.fallbackTimer)),t.element.remove()}_onMousedown(t){let e=Br(t),r=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+G_;!this._target.rippleDisabled&&!e&&!r&&(this._isPointerDown=!0,this.fadeInRipple(t.clientX,t.clientY,this._target.rippleConfig))}_onTouchStart(t){if(!this._target.rippleDisabled&&!jr(t)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;let e=t.changedTouches;if(e)for(let r=0;r<e.length;r++)this.fadeInRipple(e[r].clientX,e[r].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(t=>{let e=t.state===Re.VISIBLE||t.config.terminateOnPointerUp&&t.state===Re.FADING_IN;!t.config.persistent&&e&&t.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){let t=this._triggerElement;t&&(ch.forEach(e=>n._eventManager.removeHandler(e,t,this)),this._pointerUpEventsRegistered&&(lh.forEach(e=>t.removeEventListener(e,this,ah)),this._pointerUpEventsRegistered=!1))}};function K_(n,t,e){let r=Math.max(Math.abs(n-e.left),Math.abs(n-e.right)),i=Math.max(Math.abs(t-e.top),Math.abs(t-e.bottom));return Math.sqrt(r*r+i*i)}var $a=new y("mat-ripple-global-options"),XA=(()=>{class n{_elementRef=d(j);_animationsDisabled=ft();color;unbounded;centered;radius=0;animation;get disabled(){return this._disabled}set disabled(e){e&&this.fadeOutAllNonPersistent(),this._disabled=e,this._setupTriggerEventsIfEnabled()}_disabled=!1;get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(e){this._trigger=e,this._setupTriggerEventsIfEnabled()}_trigger;_rippleRenderer;_globalOptions;_isInitialized=!1;constructor(){let e=d(D),r=d($),i=d($a,{optional:!0}),s=d(k);this._globalOptions=i||{},this._rippleRenderer=new Gr(this,e,this._elementRef,r,s)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:g(g(g({},this._globalOptions.animation),this._animationsDisabled?{enterDuration:0,exitDuration:0}:{}),this.animation),terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(e,r=0,i){return typeof e=="number"?this._rippleRenderer.fadeInRipple(e,r,g(g({},this.rippleConfig),i)):this._rippleRenderer.fadeInRipple(0,0,g(g({},this.rippleConfig),e))}static \u0275fac=function(r){return new(r||n)};static \u0275dir=B({type:n,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(r,i){r&2&&Pe("mat-ripple-unbounded",i.unbounded)},inputs:{color:[0,"matRippleColor","color"],unbounded:[0,"matRippleUnbounded","unbounded"],centered:[0,"matRippleCentered","centered"],radius:[0,"matRippleRadius","radius"],animation:[0,"matRippleAnimation","animation"],disabled:[0,"matRippleDisabled","disabled"],trigger:[0,"matRippleTrigger","trigger"]},exportAs:["matRipple"]})}return n})();var Y_={capture:!0},X_=["focus","mousedown","mouseenter","touchstart"],za="mat-ripple-loader-uninitialized",Ha="mat-ripple-loader-class-name",uh="mat-ripple-loader-centered",fs="mat-ripple-loader-disabled",dh=(()=>{class n{_document=d(w);_animationsDisabled=ft();_globalRippleOptions=d($a,{optional:!0});_platform=d($);_ngZone=d(D);_injector=d(k);_eventCleanups;_hosts=new Map;constructor(){let e=d(we).createRenderer(null,null);this._eventCleanups=this._ngZone.runOutsideAngular(()=>X_.map(r=>e.listen(this._document,r,this._onInteraction,Y_)))}ngOnDestroy(){let e=this._hosts.keys();for(let r of e)this.destroyRipple(r);this._eventCleanups.forEach(r=>r())}configureRipple(e,r){e.setAttribute(za,this._globalRippleOptions?.namespace??""),(r.className||!e.hasAttribute(Ha))&&e.setAttribute(Ha,r.className||""),r.centered&&e.setAttribute(uh,""),r.disabled&&e.setAttribute(fs,"")}setDisabled(e,r){let i=this._hosts.get(e);i?(i.target.rippleDisabled=r,!r&&!i.hasSetUpEvents&&(i.hasSetUpEvents=!0,i.renderer.setupTriggerEvents(e))):r?e.setAttribute(fs,""):e.removeAttribute(fs)}_onInteraction=e=>{let r=he(e);if(r instanceof HTMLElement){let i=r.closest(`[${za}="${this._globalRippleOptions?.namespace??""}"]`);i&&this._createRipple(i)}};_createRipple(e){if(!this._document||this._hosts.has(e))return;e.querySelector(".mat-ripple")?.remove();let r=this._document.createElement("span");r.classList.add("mat-ripple",e.getAttribute(Ha)),e.append(r);let i=this._globalRippleOptions,s=this._animationsDisabled?0:i?.animation?.enterDuration??Wr.enterDuration,o=this._animationsDisabled?0:i?.animation?.exitDuration??Wr.exitDuration,a={rippleDisabled:this._animationsDisabled||i?.disabled||e.hasAttribute(fs),rippleConfig:{centered:e.hasAttribute(uh),terminateOnPointerUp:i?.terminateOnPointerUp,animation:{enterDuration:s,exitDuration:o}}},c=new Gr(a,this._ngZone,r,this._platform,this._injector),l=!a.rippleDisabled;l&&c.setupTriggerEvents(e),this._hosts.set(e,{target:a,renderer:c,hasSetUpEvents:l}),e.removeAttribute(za)}destroyRipple(e){let r=this._hosts.get(e);r&&(r.renderer._removeTriggerEvents(),this._hosts.delete(e))}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var hh=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["structural-styles"]],decls:0,vars:0,template:function(r,i){},styles:[`.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:""}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}
`],encapsulation:2,changeDetection:0})}return n})();var Z_=["mat-icon-button",""],J_=["*"],Q_=new y("MAT_BUTTON_CONFIG");function fh(n){return n==null?void 0:vu(n)}var Wa=(()=>{class n{_elementRef=d(j);_ngZone=d(D);_animationsDisabled=ft();_config=d(Q_,{optional:!0});_focusMonitor=d(Oa);_cleanupClick;_renderer=d(Ve);_rippleLoader=d(dh);_isAnchor;_isFab=!1;color;get disableRipple(){return this._disableRipple}set disableRipple(e){this._disableRipple=e,this._updateRippleDisabled()}_disableRipple=!1;get disabled(){return this._disabled}set disabled(e){this._disabled=e,this._updateRippleDisabled()}_disabled=!1;ariaDisabled;disabledInteractive;tabIndex;set _tabindex(e){this.tabIndex=e}constructor(){d(Ge).load(hh);let e=this._elementRef.nativeElement;this._isAnchor=e.tagName==="A",this.disabledInteractive=this._config?.disabledInteractive??!1,this.color=this._config?.color??null,this._rippleLoader?.configureRipple(e,{className:"mat-mdc-button-ripple"})}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0),this._isAnchor&&this._setupAsAnchor()}ngOnDestroy(){this._cleanupClick?.(),this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(e="program",r){e?this._focusMonitor.focusVia(this._elementRef.nativeElement,e,r):this._elementRef.nativeElement.focus(r)}_getAriaDisabled(){return this.ariaDisabled!=null?this.ariaDisabled:this._isAnchor?this.disabled||null:this.disabled&&this.disabledInteractive?!0:null}_getDisabledAttribute(){return this.disabledInteractive||!this.disabled?null:!0}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}_getTabIndex(){return this._isAnchor?this.disabled&&!this.disabledInteractive?-1:this.tabIndex:this.tabIndex}_setupAsAnchor(){this._cleanupClick=this._ngZone.runOutsideAngular(()=>this._renderer.listen(this._elementRef.nativeElement,"click",e=>{this.disabled&&(e.preventDefault(),e.stopImmediatePropagation())}))}static \u0275fac=function(r){return new(r||n)};static \u0275dir=B({type:n,hostAttrs:[1,"mat-mdc-button-base"],hostVars:13,hostBindings:function(r,i){r&2&&(ot("disabled",i._getDisabledAttribute())("aria-disabled",i._getAriaDisabled())("tabindex",i._getTabIndex()),Ii(i.color?"mat-"+i.color:""),Pe("mat-mdc-button-disabled",i.disabled)("mat-mdc-button-disabled-interactive",i.disabledInteractive)("mat-unthemed",!i.color)("_mat-animation-noopable",i._animationsDisabled))},inputs:{color:"color",disableRipple:[2,"disableRipple","disableRipple",Y],disabled:[2,"disabled","disabled",Y],ariaDisabled:[2,"aria-disabled","ariaDisabled",Y],disabledInteractive:[2,"disabledInteractive","disabledInteractive",Y],tabIndex:[2,"tabIndex","tabIndex",fh],_tabindex:[2,"tabindex","_tabindex",fh]}})}return n})(),ev=(()=>{class n extends Wa{constructor(){super(),this._rippleLoader.configureRipple(this._elementRef.nativeElement,{centered:!0})}static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["button","mat-icon-button",""],["a","mat-icon-button",""],["button","matIconButton",""],["a","matIconButton",""]],hostAttrs:[1,"mdc-icon-button","mat-mdc-icon-button"],exportAs:["matButton","matAnchor"],features:[it],attrs:Z_,ngContentSelectors:J_,decls:4,vars:0,consts:[[1,"mat-mdc-button-persistent-ripple","mdc-icon-button__ripple"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(r,i){r&1&&(en(),Oe(0,"span",0),at(1),Oe(2,"span",1)(3,"span",2))},styles:[`.mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%));flex-shrink:0;text-align:center;width:var(--mat-icon-button-state-layer-size, 40px);height:var(--mat-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mat-icon-button-state-layer-size, 40px) - var(--mat-icon-button-icon-size, 24px)) / 2);font-size:var(--mat-icon-button-icon-size, 24px);color:var(--mat-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:"";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-icon-button-touch-target-display, block);left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mat-icon-button-icon-size, 24px);height:var(--mat-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%))}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}
`,`@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-button-base.mat-tonal-button,.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}
`],encapsulation:2,changeDetection:0})}return n})();var tv=new y("cdk-dir-doc",{providedIn:"root",factory:nv});function nv(){return d(w)}var rv=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function ph(n){let t=n?.toLowerCase()||"";return t==="auto"&&typeof navigator<"u"&&navigator?.language?rv.test(navigator.language)?"rtl":"ltr":t==="rtl"?"rtl":"ltr"}var ps=(()=>{class n{get value(){return this.valueSignal()}valueSignal=tt("ltr");change=new Q;constructor(){let e=d(tv,{optional:!0});if(e){let r=e.body?e.body.dir:null,i=e.documentElement?e.documentElement.dir:null;this.valueSignal.set(ph(r||i||"ltr"))}}ngOnDestroy(){this.change.complete()}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var kt=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({})}return n})();var Ae=(()=>{class n{constructor(){d(cs)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({imports:[kt,kt]})}return n})();var gh=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({imports:[Ae,Ae]})}return n})();var iv=["matButton",""],sv=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],ov=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"];var _h=new Map([["text",["mat-mdc-button"]],["filled",["mdc-button--unelevated","mat-mdc-unelevated-button"]],["elevated",["mdc-button--raised","mat-mdc-raised-button"]],["outlined",["mdc-button--outlined","mat-mdc-outlined-button"]],["tonal",["mat-tonal-button"]]]),vh=(()=>{class n extends Wa{get appearance(){return this._appearance}set appearance(e){this.setAppearance(e||this._config?.defaultAppearance||"text")}_appearance=null;constructor(){super();let e=av(this._elementRef.nativeElement);e&&this.setAppearance(e)}setAppearance(e){if(e===this._appearance)return;let r=this._elementRef.nativeElement.classList,i=this._appearance?_h.get(this._appearance):null,s=_h.get(e);i&&r.remove(...i),r.add(...s),this._appearance=e}static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["button","matButton",""],["a","matButton",""],["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""],["a","mat-button",""],["a","mat-raised-button",""],["a","mat-flat-button",""],["a","mat-stroked-button",""]],hostAttrs:[1,"mdc-button"],inputs:{appearance:[0,"matButton","appearance"]},exportAs:["matButton","matAnchor"],features:[it],attrs:iv,ngContentSelectors:ov,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(r,i){r&1&&(en(sv),Oe(0,"span",0),at(1),Qt(2,"span",1),at(3,1),It(),at(4,2),Oe(5,"span",2)(6,"span",3)),r&2&&Pe("mdc-button__ripple",!i._isFab)("mdc-fab__ripple",i._isFab)},styles:[`.mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-button-text-horizontal-padding, 12px);height:var(--mat-button-text-container-height, 40px);font-family:var(--mat-button-text-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mat-button-text-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-button-text-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mat-button-text-label-text-transform);font-weight:var(--mat-button-text-label-text-weight, var(--mat-sys-label-large-weight))}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mat-button-text-container-shape, var(--mat-sys-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mat-button-text-label-text-color, var(--mat-sys-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-button-text-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-button-text-with-icon-horizontal-padding, 16px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-button-text-icon-spacing, 8px);margin-left:var(--mat-button-text-icon-offset, -4px)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-button-text-icon-offset, -4px);margin-left:var(--mat-button-text-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-text-icon-offset, -4px);margin-left:var(--mat-button-text-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-text-icon-spacing, 8px);margin-left:var(--mat-button-text-icon-offset, -4px)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-button-text-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-text-state-layer-color, var(--mat-sys-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-text-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-text-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-text-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-text-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-button-text-touch-target-display, block);left:0;right:0;transform:translateY(-50%)}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-button-filled-container-height, 40px);font-family:var(--mat-button-filled-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mat-button-filled-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-button-filled-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mat-button-filled-label-text-transform);font-weight:var(--mat-button-filled-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-button-filled-horizontal-padding, 24px)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-button-filled-icon-spacing, 8px);margin-left:var(--mat-button-filled-icon-offset, -8px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-button-filled-icon-offset, -8px);margin-left:var(--mat-button-filled-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-filled-icon-offset, -8px);margin-left:var(--mat-button-filled-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-filled-icon-spacing, 8px);margin-left:var(--mat-button-filled-icon-offset, -8px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-button-filled-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-filled-state-layer-color, var(--mat-sys-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-filled-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-unelevated-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-filled-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-filled-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-filled-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-button-filled-touch-target-display, block);left:0;right:0;transform:translateY(-50%)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mat-button-filled-label-text-color, var(--mat-sys-on-primary));background-color:var(--mat-button-filled-container-color, var(--mat-sys-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mat-button-filled-container-shape, var(--mat-sys-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-button-filled-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-button-filled-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);box-shadow:var(--mat-button-protected-container-elevation-shadow, var(--mat-sys-level1));height:var(--mat-button-protected-container-height, 40px);font-family:var(--mat-button-protected-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mat-button-protected-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-button-protected-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mat-button-protected-label-text-transform);font-weight:var(--mat-button-protected-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-button-protected-horizontal-padding, 24px)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-button-protected-icon-spacing, 8px);margin-left:var(--mat-button-protected-icon-offset, -8px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-button-protected-icon-offset, -8px);margin-left:var(--mat-button-protected-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-protected-icon-offset, -8px);margin-left:var(--mat-button-protected-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-protected-icon-spacing, 8px);margin-left:var(--mat-button-protected-icon-offset, -8px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-button-protected-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-protected-state-layer-color, var(--mat-sys-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-protected-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-raised-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-protected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-protected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-raised-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-protected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-button-protected-touch-target-display, block);left:0;right:0;transform:translateY(-50%)}.mat-mdc-raised-button:not(:disabled){color:var(--mat-button-protected-label-text-color, var(--mat-sys-primary));background-color:var(--mat-button-protected-container-color, var(--mat-sys-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mat-button-protected-container-shape, var(--mat-sys-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mat-button-protected-hover-container-elevation-shadow, var(--mat-sys-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mat-button-protected-focus-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mat-button-protected-pressed-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-button-protected-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-button-protected-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mat-button-protected-disabled-container-elevation-shadow, var(--mat-sys-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-button-outlined-container-height, 40px);font-family:var(--mat-button-outlined-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mat-button-outlined-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-button-outlined-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mat-button-outlined-label-text-transform);font-weight:var(--mat-button-outlined-label-text-weight, var(--mat-sys-label-large-weight));border-radius:var(--mat-button-outlined-container-shape, var(--mat-sys-corner-full));border-width:var(--mat-button-outlined-outline-width, 1px);padding:0 var(--mat-button-outlined-horizontal-padding, 24px)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-button-outlined-icon-spacing, 8px);margin-left:var(--mat-button-outlined-icon-offset, -8px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-button-outlined-icon-offset, -8px);margin-left:var(--mat-button-outlined-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-outlined-icon-offset, -8px);margin-left:var(--mat-button-outlined-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-outlined-icon-spacing, 8px);margin-left:var(--mat-button-outlined-icon-offset, -8px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-button-outlined-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-outlined-state-layer-color, var(--mat-sys-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-outlined-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-outlined-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-outlined-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-outlined-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-outlined-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-outlined-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-button-outlined-touch-target-display, block);left:0;right:0;transform:translateY(-50%)}.mat-mdc-outlined-button:not(:disabled){color:var(--mat-button-outlined-label-text-color, var(--mat-sys-primary));border-color:var(--mat-button-outlined-outline-color, var(--mat-sys-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-button-outlined-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:var(--mat-button-outlined-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-tonal-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-button-tonal-container-height, 40px);font-family:var(--mat-button-tonal-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mat-button-tonal-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mat-button-tonal-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mat-button-tonal-label-text-transform);font-weight:var(--mat-button-tonal-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-button-tonal-horizontal-padding, 24px)}.mat-tonal-button:not(:disabled){color:var(--mat-button-tonal-label-text-color, var(--mat-sys-on-secondary-container));background-color:var(--mat-button-tonal-container-color, var(--mat-sys-secondary-container))}.mat-tonal-button,.mat-tonal-button .mdc-button__ripple{border-radius:var(--mat-button-tonal-container-shape, var(--mat-sys-corner-full))}.mat-tonal-button[disabled],.mat-tonal-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-button-tonal-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-button-tonal-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-tonal-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-tonal-button>.mat-icon{margin-right:var(--mat-button-tonal-icon-spacing, 8px);margin-left:var(--mat-button-tonal-icon-offset, -8px)}[dir=rtl] .mat-tonal-button>.mat-icon{margin-right:var(--mat-button-tonal-icon-offset, -8px);margin-left:var(--mat-button-tonal-icon-spacing, 8px)}.mat-tonal-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-tonal-icon-offset, -8px);margin-left:var(--mat-button-tonal-icon-spacing, 8px)}[dir=rtl] .mat-tonal-button .mdc-button__label+.mat-icon{margin-right:var(--mat-button-tonal-icon-spacing, 8px);margin-left:var(--mat-button-tonal-icon-offset, -8px)}.mat-tonal-button .mat-ripple-element{background-color:var(--mat-button-tonal-ripple-color, color-mix(in srgb, var(--mat-sys-on-secondary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-tonal-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-tonal-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-tonal-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-button-tonal-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-tonal-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-tonal-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-tonal-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-tonal-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-tonal-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-tonal-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-tonal-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-button-tonal-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-tonal-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-button-tonal-touch-target-display, block);left:0;right:0;transform:translateY(-50%)}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button,.mat-tonal-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before,.mat-tonal-button .mat-mdc-button-ripple,.mat-tonal-button .mat-mdc-button-persistent-ripple,.mat-tonal-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-tonal-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before,.mat-tonal-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon,.mat-tonal-button .mdc-button__label,.mat-tonal-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-focus-indicator,.mat-mdc-unelevated-button .mat-focus-indicator,.mat-mdc-raised-button .mat-focus-indicator,.mat-mdc-outlined-button .mat-focus-indicator,.mat-tonal-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-button:focus>.mat-focus-indicator::before,.mat-mdc-unelevated-button:focus>.mat-focus-indicator::before,.mat-mdc-raised-button:focus>.mat-focus-indicator::before,.mat-mdc-outlined-button:focus>.mat-focus-indicator::before,.mat-tonal-button:focus>.mat-focus-indicator::before{content:"";border-radius:inherit}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable,.mat-tonal-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon,.mat-tonal-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-focus-indicator::before,.mat-tonal-button .mat-focus-indicator::before,.mat-mdc-raised-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}
`,`@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-button-base.mat-tonal-button,.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}
`],encapsulation:2,changeDetection:0})}return n})();function av(n){return n.hasAttribute("mat-raised-button")?"elevated":n.hasAttribute("mat-stroked-button")?"outlined":n.hasAttribute("mat-flat-button")?"filled":n.hasAttribute("mat-button")?"text":null}var bh=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({imports:[Ae,gh,Ae]})}return n})();var ms;function cv(){if(ms===void 0&&(ms=null,typeof window<"u")){let n=window;n.trustedTypes!==void 0&&(ms=n.trustedTypes.createPolicy("angular#components",{createHTML:t=>t}))}return ms}function qr(n){return cv()?.createHTML(n)||n}function yh(n){return Error(`Unable to find icon with the name "${n}"`)}function lv(){return Error("Could not find HttpClient for use with Angular Material icons. Please add provideHttpClient() to your providers.")}function Eh(n){return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL via Angular's DomSanitizer. Attempted URL was "${n}".`)}function wh(n){return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by Angular's DomSanitizer. Attempted literal was "${n}".`)}var pt=class{url;svgText;options;svgElement;constructor(t,e,r){this.url=t,this.svgText=e,this.options=r}},Dh=(()=>{class n{_httpClient;_sanitizer;_errorHandler;_document;_svgIconConfigs=new Map;_iconSetConfigs=new Map;_cachedIconsByUrl=new Map;_inProgressUrlFetches=new Map;_fontCssClassesByAlias=new Map;_resolvers=[];_defaultFontSetClass=["material-icons","mat-ligature-font"];constructor(e,r,i,s){this._httpClient=e,this._sanitizer=r,this._errorHandler=s,this._document=i}addSvgIcon(e,r,i){return this.addSvgIconInNamespace("",e,r,i)}addSvgIconLiteral(e,r,i){return this.addSvgIconLiteralInNamespace("",e,r,i)}addSvgIconInNamespace(e,r,i,s){return this._addSvgIconConfig(e,r,new pt(i,null,s))}addSvgIconResolver(e){return this._resolvers.push(e),this}addSvgIconLiteralInNamespace(e,r,i,s){let o=this._sanitizer.sanitize(Ee.HTML,i);if(!o)throw wh(i);let a=qr(o);return this._addSvgIconConfig(e,r,new pt("",a,s))}addSvgIconSet(e,r){return this.addSvgIconSetInNamespace("",e,r)}addSvgIconSetLiteral(e,r){return this.addSvgIconSetLiteralInNamespace("",e,r)}addSvgIconSetInNamespace(e,r,i){return this._addSvgIconSetConfig(e,new pt(r,null,i))}addSvgIconSetLiteralInNamespace(e,r,i){let s=this._sanitizer.sanitize(Ee.HTML,r);if(!s)throw wh(r);let o=qr(s);return this._addSvgIconSetConfig(e,new pt("",o,i))}registerFontClassAlias(e,r=e){return this._fontCssClassesByAlias.set(e,r),this}classNameForFontAlias(e){return this._fontCssClassesByAlias.get(e)||e}setDefaultFontSetClass(...e){return this._defaultFontSetClass=e,this}getDefaultFontSetClass(){return this._defaultFontSetClass}getSvgIconFromUrl(e){let r=this._sanitizer.sanitize(Ee.RESOURCE_URL,e);if(!r)throw Eh(e);let i=this._cachedIconsByUrl.get(r);return i?b(gs(i)):this._loadSvgIconFromConfig(new pt(e,null)).pipe(V(s=>this._cachedIconsByUrl.set(r,s)),E(s=>gs(s)))}getNamedSvgIcon(e,r=""){let i=Ih(r,e),s=this._svgIconConfigs.get(i);if(s)return this._getSvgFromConfig(s);if(s=this._getIconConfigFromResolvers(r,e),s)return this._svgIconConfigs.set(i,s),this._getSvgFromConfig(s);let o=this._iconSetConfigs.get(r);return o?this._getSvgFromIconSetConfigs(e,o):qt(yh(i))}ngOnDestroy(){this._resolvers=[],this._svgIconConfigs.clear(),this._iconSetConfigs.clear(),this._cachedIconsByUrl.clear()}_getSvgFromConfig(e){return e.svgText?b(gs(this._svgElementFromConfig(e))):this._loadSvgIconFromConfig(e).pipe(E(r=>gs(r)))}_getSvgFromIconSetConfigs(e,r){let i=this._extractIconWithNameFromAnySet(e,r);if(i)return b(i);let s=r.filter(o=>!o.svgText).map(o=>this._loadSvgIconSetFromConfig(o).pipe(et(a=>{let l=`Loading icon set URL: ${this._sanitizer.sanitize(Ee.RESOURCE_URL,o.url)} failed: ${a.message}`;return this._errorHandler.handleError(new Error(l)),b(null)})));return Ul(s).pipe(E(()=>{let o=this._extractIconWithNameFromAnySet(e,r);if(!o)throw yh(e);return o}))}_extractIconWithNameFromAnySet(e,r){for(let i=r.length-1;i>=0;i--){let s=r[i];if(s.svgText&&s.svgText.toString().indexOf(e)>-1){let o=this._svgElementFromConfig(s),a=this._extractSvgIconFromSet(o,e,s.options);if(a)return a}}return null}_loadSvgIconFromConfig(e){return this._fetchIcon(e).pipe(V(r=>e.svgText=r),E(()=>this._svgElementFromConfig(e)))}_loadSvgIconSetFromConfig(e){return e.svgText?b(null):this._fetchIcon(e).pipe(V(r=>e.svgText=r))}_extractSvgIconFromSet(e,r,i){let s=e.querySelector(`[id="${r}"]`);if(!s)return null;let o=s.cloneNode(!0);if(o.removeAttribute("id"),o.nodeName.toLowerCase()==="svg")return this._setSvgAttributes(o,i);if(o.nodeName.toLowerCase()==="symbol")return this._setSvgAttributes(this._toSvgElement(o),i);let a=this._svgElementFromString(qr("<svg></svg>"));return a.appendChild(o),this._setSvgAttributes(a,i)}_svgElementFromString(e){let r=this._document.createElement("DIV");r.innerHTML=e;let i=r.querySelector("svg");if(!i)throw Error("<svg> tag not found");return i}_toSvgElement(e){let r=this._svgElementFromString(qr("<svg></svg>")),i=e.attributes;for(let s=0;s<i.length;s++){let{name:o,value:a}=i[s];o!=="id"&&r.setAttribute(o,a)}for(let s=0;s<e.childNodes.length;s++)e.childNodes[s].nodeType===this._document.ELEMENT_NODE&&r.appendChild(e.childNodes[s].cloneNode(!0));return r}_setSvgAttributes(e,r){return e.setAttribute("fit",""),e.setAttribute("height","100%"),e.setAttribute("width","100%"),e.setAttribute("preserveAspectRatio","xMidYMid meet"),e.setAttribute("focusable","false"),r&&r.viewBox&&e.setAttribute("viewBox",r.viewBox),e}_fetchIcon(e){let{url:r,options:i}=e,s=i?.withCredentials??!1;if(!this._httpClient)throw lv();if(r==null)throw Error(`Cannot fetch icon from URL "${r}".`);let o=this._sanitizer.sanitize(Ee.RESOURCE_URL,r);if(!o)throw Eh(r);let a=this._inProgressUrlFetches.get(o);if(a)return a;let c=this._httpClient.get(o,{responseType:"text",withCredentials:s}).pipe(E(l=>qr(l)),Kt(()=>this._inProgressUrlFetches.delete(o)),Vl());return this._inProgressUrlFetches.set(o,c),c}_addSvgIconConfig(e,r,i){return this._svgIconConfigs.set(Ih(e,r),i),this}_addSvgIconSetConfig(e,r){let i=this._iconSetConfigs.get(e);return i?i.push(r):this._iconSetConfigs.set(e,[r]),this}_svgElementFromConfig(e){if(!e.svgElement){let r=this._svgElementFromString(e.svgText);this._setSvgAttributes(r,e.options),e.svgElement=r}return e.svgElement}_getIconConfigFromResolvers(e,r){for(let i=0;i<this._resolvers.length;i++){let s=this._resolvers[i](r,e);if(s)return uv(s)?new pt(s.url,null,s.options):new pt(s,null)}}static \u0275fac=function(r){return new(r||n)(T(ta,8),T(na),T(w,8),T(wt))};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function gs(n){return n.cloneNode(!0)}function Ih(n,t){return n+":"+t}function uv(n){return!!(n.url&&n.options)}var dv=["*"],hv=new y("MAT_ICON_DEFAULT_OPTIONS"),fv=new y("mat-icon-location",{providedIn:"root",factory:pv});function pv(){let n=d(w),t=n?n.location:null;return{getPathname:()=>t?t.pathname+t.search:""}}var Ch=["clip-path","color-profile","src","cursor","fill","filter","marker","marker-start","marker-mid","marker-end","mask","stroke"],mv=Ch.map(n=>`[${n}]`).join(", "),gv=/^url\(['"]?#(.*?)['"]?\)$/,Zk=(()=>{class n{_elementRef=d(j);_iconRegistry=d(Dh);_location=d(fv);_errorHandler=d(wt);_defaultColor;get color(){return this._color||this._defaultColor}set color(e){this._color=e}_color;inline=!1;get svgIcon(){return this._svgIcon}set svgIcon(e){e!==this._svgIcon&&(e?this._updateSvgIcon(e):this._svgIcon&&this._clearSvgElement(),this._svgIcon=e)}_svgIcon;get fontSet(){return this._fontSet}set fontSet(e){let r=this._cleanupFontValue(e);r!==this._fontSet&&(this._fontSet=r,this._updateFontIconClasses())}_fontSet;get fontIcon(){return this._fontIcon}set fontIcon(e){let r=this._cleanupFontValue(e);r!==this._fontIcon&&(this._fontIcon=r,this._updateFontIconClasses())}_fontIcon;_previousFontSetClass=[];_previousFontIconClass;_svgName;_svgNamespace;_previousPath;_elementsWithExternalReferences;_currentIconFetch=me.EMPTY;constructor(){let e=d(new Di("aria-hidden"),{optional:!0}),r=d(hv,{optional:!0});r&&(r.color&&(this.color=this._defaultColor=r.color),r.fontSet&&(this.fontSet=r.fontSet)),e||this._elementRef.nativeElement.setAttribute("aria-hidden","true")}_splitIconName(e){if(!e)return["",""];let r=e.split(":");switch(r.length){case 1:return["",r[0]];case 2:return r;default:throw Error(`Invalid icon name: "${e}"`)}}ngOnInit(){this._updateFontIconClasses()}ngAfterViewChecked(){let e=this._elementsWithExternalReferences;if(e&&e.size){let r=this._location.getPathname();r!==this._previousPath&&(this._previousPath=r,this._prependPathToReferences(r))}}ngOnDestroy(){this._currentIconFetch.unsubscribe(),this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear()}_usingFontIcon(){return!this.svgIcon}_setSvgElement(e){this._clearSvgElement();let r=this._location.getPathname();this._previousPath=r,this._cacheChildrenWithExternalReferences(e),this._prependPathToReferences(r),this._elementRef.nativeElement.appendChild(e)}_clearSvgElement(){let e=this._elementRef.nativeElement,r=e.childNodes.length;for(this._elementsWithExternalReferences&&this._elementsWithExternalReferences.clear();r--;){let i=e.childNodes[r];(i.nodeType!==1||i.nodeName.toLowerCase()==="svg")&&i.remove()}}_updateFontIconClasses(){if(!this._usingFontIcon())return;let e=this._elementRef.nativeElement,r=(this.fontSet?this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/):this._iconRegistry.getDefaultFontSetClass()).filter(i=>i.length>0);this._previousFontSetClass.forEach(i=>e.classList.remove(i)),r.forEach(i=>e.classList.add(i)),this._previousFontSetClass=r,this.fontIcon!==this._previousFontIconClass&&!r.includes("mat-ligature-font")&&(this._previousFontIconClass&&e.classList.remove(this._previousFontIconClass),this.fontIcon&&e.classList.add(this.fontIcon),this._previousFontIconClass=this.fontIcon)}_cleanupFontValue(e){return typeof e=="string"?e.trim().split(" ")[0]:e}_prependPathToReferences(e){let r=this._elementsWithExternalReferences;r&&r.forEach((i,s)=>{i.forEach(o=>{s.setAttribute(o.name,`url('${e}#${o.value}')`)})})}_cacheChildrenWithExternalReferences(e){let r=e.querySelectorAll(mv),i=this._elementsWithExternalReferences=this._elementsWithExternalReferences||new Map;for(let s=0;s<r.length;s++)Ch.forEach(o=>{let a=r[s],c=a.getAttribute(o),l=c?c.match(gv):null;if(l){let u=i.get(a);u||(u=[],i.set(a,u)),u.push({name:o,value:l[1]})}})}_updateSvgIcon(e){if(this._svgNamespace=null,this._svgName=null,this._currentIconFetch.unsubscribe(),e){let[r,i]=this._splitIconName(e);r&&(this._svgNamespace=r),i&&(this._svgName=i),this._currentIconFetch=this._iconRegistry.getNamedSvgIcon(i,r).pipe(Be(1)).subscribe(s=>this._setSvgElement(s),s=>{let o=`Error retrieving icon ${r}:${i}! ${s.message}`;this._errorHandler.handleError(new Error(o))})}}static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["mat-icon"]],hostAttrs:["role","img",1,"mat-icon","notranslate"],hostVars:10,hostBindings:function(r,i){r&2&&(ot("data-mat-icon-type",i._usingFontIcon()?"font":"svg")("data-mat-icon-name",i._svgName||i.fontIcon)("data-mat-icon-namespace",i._svgNamespace||i.fontSet)("fontIcon",i._usingFontIcon()?i.fontIcon:null),Ii(i.color?"mat-"+i.color:""),Pe("mat-icon-inline",i.inline)("mat-icon-no-color",i.color!=="primary"&&i.color!=="accent"&&i.color!=="warn"))},inputs:{color:"color",inline:[2,"inline","inline",Y],svgIcon:"svgIcon",fontSet:"fontSet",fontIcon:"fontIcon"},exportAs:["matIcon"],ngContentSelectors:dv,decls:1,vars:0,template:function(r,i){r&1&&(en(),at(0))},styles:[`mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color, inherit)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}
`],encapsulation:2,changeDetection:0})}return n})(),Jk=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({imports:[Ae,Ae]})}return n})();var Kr=class{_attachedHost;attach(t){return this._attachedHost=t,t.attach(this)}detach(){let t=this._attachedHost;t!=null&&(this._attachedHost=null,t.detach())}get isAttached(){return this._attachedHost!=null}setAttachedHost(t){this._attachedHost=t}},Yn=class extends Kr{component;viewContainerRef;injector;projectableNodes;constructor(t,e,r,i){super(),this.component=t,this.viewContainerRef=e,this.injector=r,this.projectableNodes=i}},un=class extends Kr{templateRef;viewContainerRef;context;injector;constructor(t,e,r,i){super(),this.templateRef=t,this.viewContainerRef=e,this.context=r,this.injector=i}get origin(){return this.templateRef.elementRef}attach(t,e=this.context){return this.context=e,super.attach(t)}detach(){return this.context=void 0,super.detach()}},Ga=class extends Kr{element;constructor(t){super(),this.element=t instanceof j?t.nativeElement:t}},Xn=class{_attachedPortal;_disposeFn;_isDisposed=!1;hasAttached(){return!!this._attachedPortal}attach(t){if(t instanceof Yn)return this._attachedPortal=t,this.attachComponentPortal(t);if(t instanceof un)return this._attachedPortal=t,this.attachTemplatePortal(t);if(this.attachDomPortal&&t instanceof Ga)return this._attachedPortal=t,this.attachDomPortal(t)}attachDomPortal=null;detach(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()}dispose(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0}setDisposeFn(t){this._disposeFn=t}_invokeDisposeFn(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)}},_s=class extends Xn{outletElement;_appRef;_defaultInjector;constructor(t,e,r){super(),this.outletElement=t,this._appRef=e,this._defaultInjector=r}attachComponentPortal(t){let e;if(t.viewContainerRef){let r=t.injector||t.viewContainerRef.injector,i=r.get(Ei,null,{optional:!0})||void 0;e=t.viewContainerRef.createComponent(t.component,{index:t.viewContainerRef.length,injector:r,ngModuleRef:i,projectableNodes:t.projectableNodes||void 0}),this.setDisposeFn(()=>e.destroy())}else{let r=this._appRef,i=t.injector||this._defaultInjector||k.NULL,s=i.get(le,r.injector);e=Ci(t.component,{elementInjector:i,environmentInjector:s,projectableNodes:t.projectableNodes||void 0}),r.attachView(e.hostView),this.setDisposeFn(()=>{r.viewCount>0&&r.detachView(e.hostView),e.destroy()})}return this.outletElement.appendChild(this._getComponentRootNode(e)),this._attachedPortal=t,e}attachTemplatePortal(t){let e=t.viewContainerRef,r=e.createEmbeddedView(t.templateRef,t.context,{injector:t.injector});return r.rootNodes.forEach(i=>this.outletElement.appendChild(i)),r.detectChanges(),this.setDisposeFn(()=>{let i=e.indexOf(r);i!==-1&&e.remove(i)}),this._attachedPortal=t,r}attachDomPortal=t=>{let e=t.element;e.parentNode;let r=this.outletElement.ownerDocument.createComment("dom-portal");e.parentNode.insertBefore(r,e),this.outletElement.appendChild(e),this._attachedPortal=t,super.setDisposeFn(()=>{r.parentNode&&r.parentNode.replaceChild(e,r)})};dispose(){super.dispose(),this.outletElement.remove()}_getComponentRootNode(t){return t.hostView.rootNodes[0]}};var qa=(()=>{class n extends Xn{_moduleRef=d(Ei,{optional:!0});_document=d(w);_viewContainerRef=d(rt);_isInitialized=!1;_attachedRef;constructor(){super()}get portal(){return this._attachedPortal}set portal(e){this.hasAttached()&&!e&&!this._isInitialized||(this.hasAttached()&&super.detach(),e&&super.attach(e),this._attachedPortal=e||null)}attached=new Q;get attachedRef(){return this._attachedRef}ngOnInit(){this._isInitialized=!0}ngOnDestroy(){super.dispose(),this._attachedRef=this._attachedPortal=null}attachComponentPortal(e){e.setAttachedHost(this);let r=e.viewContainerRef!=null?e.viewContainerRef:this._viewContainerRef,i=r.createComponent(e.component,{index:r.length,injector:e.injector||r.injector,projectableNodes:e.projectableNodes||void 0,ngModuleRef:this._moduleRef||void 0});return r!==this._viewContainerRef&&this._getRootNode().appendChild(i.hostView.rootNodes[0]),super.setDisposeFn(()=>i.destroy()),this._attachedPortal=e,this._attachedRef=i,this.attached.emit(i),i}attachTemplatePortal(e){e.setAttachedHost(this);let r=this._viewContainerRef.createEmbeddedView(e.templateRef,e.context,{injector:e.injector});return super.setDisposeFn(()=>this._viewContainerRef.clear()),this._attachedPortal=e,this._attachedRef=r,this.attached.emit(r),r}attachDomPortal=e=>{let r=e.element;r.parentNode;let i=this._document.createComment("dom-portal");e.setAttachedHost(this),r.parentNode.insertBefore(i,r),this._getRootNode().appendChild(r),this._attachedPortal=e,super.setDisposeFn(()=>{i.parentNode&&i.parentNode.replaceChild(r,i)})};_getRootNode(){let e=this._viewContainerRef.element.nativeElement;return e.nodeType===e.ELEMENT_NODE?e:e.parentNode}static \u0275fac=function(r){return new(r||n)};static \u0275dir=B({type:n,selectors:[["","cdkPortalOutlet",""]],inputs:{portal:[0,"cdkPortalOutlet","portal"]},outputs:{attached:"attached"},exportAs:["cdkPortalOutlet"],features:[it]})}return n})();var vs=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({})}return n})();var _v=20,bs=(()=>{class n{_ngZone=d(D);_platform=d($);_renderer=d(we).createRenderer(null,null);_cleanupGlobalListener;constructor(){}_scrolled=new A;_scrolledCount=0;scrollContainers=new Map;register(e){this.scrollContainers.has(e)||this.scrollContainers.set(e,e.elementScrolled().subscribe(()=>this._scrolled.next(e)))}deregister(e){let r=this.scrollContainers.get(e);r&&(r.unsubscribe(),this.scrollContainers.delete(e))}scrolled(e=_v){return this._platform.isBrowser?new Se(r=>{this._cleanupGlobalListener||(this._cleanupGlobalListener=this._ngZone.runOutsideAngular(()=>this._renderer.listen("document","scroll",()=>this._scrolled.next())));let i=e>0?this._scrolled.pipe(yo(e)).subscribe(r):this._scrolled.subscribe(r);return this._scrolledCount++,()=>{i.unsubscribe(),this._scrolledCount--,this._scrolledCount||(this._cleanupGlobalListener?.(),this._cleanupGlobalListener=void 0)}}):b()}ngOnDestroy(){this._cleanupGlobalListener?.(),this._cleanupGlobalListener=void 0,this.scrollContainers.forEach((e,r)=>this.deregister(r)),this._scrolled.complete()}ancestorScrolled(e,r){let i=this.getAncestorScrollContainers(e);return this.scrolled(r).pipe(ae(s=>!s||i.indexOf(s)>-1))}getAncestorScrollContainers(e){let r=[];return this.scrollContainers.forEach((i,s)=>{this._scrollableContainsElement(s,e)&&r.push(s)}),r}_scrollableContainsElement(e,r){let i=We(r),s=e.getElementRef().nativeElement;do if(i==s)return!0;while(i=i.parentElement);return!1}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var vv=20,Zn=(()=>{class n{_platform=d($);_listeners;_viewportSize;_change=new A;_document=d(w,{optional:!0});constructor(){let e=d(D),r=d(we).createRenderer(null,null);e.runOutsideAngular(()=>{if(this._platform.isBrowser){let i=s=>this._change.next(s);this._listeners=[r.listen("window","resize",i),r.listen("window","orientationchange",i)]}this.change().subscribe(()=>this._viewportSize=null)})}ngOnDestroy(){this._listeners?.forEach(e=>e()),this._change.complete()}getViewportSize(){this._viewportSize||this._updateViewportSize();let e={width:this._viewportSize.width,height:this._viewportSize.height};return this._platform.isBrowser||(this._viewportSize=null),e}getViewportRect(){let e=this.getViewportScrollPosition(),{width:r,height:i}=this.getViewportSize();return{top:e.top,left:e.left,bottom:e.top+i,right:e.left+r,height:i,width:r}}getViewportScrollPosition(){if(!this._platform.isBrowser)return{top:0,left:0};let e=this._document,r=this._getWindow(),i=e.documentElement,s=i.getBoundingClientRect(),o=-s.top||e.body.scrollTop||r.scrollY||i.scrollTop||0,a=-s.left||e.body.scrollLeft||r.scrollX||i.scrollLeft||0;return{top:o,left:a}}change(e=vv){return e>0?this._change.pipe(yo(e)):this._change}_getWindow(){return this._document.defaultView||window}_updateViewportSize(){let e=this._getWindow();this._viewportSize=this._platform.isBrowser?{width:e.innerWidth,height:e.innerHeight}:{width:0,height:0}}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Ka=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({})}return n})(),Ya=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({imports:[kt,Ka,kt,Ka]})}return n})();var Sh=rh();function Mh(n){return new ys(n.get(Zn),n.get(w))}var ys=class{_viewportRuler;_previousHTMLStyles={top:"",left:""};_previousScrollPosition;_isEnabled=!1;_document;constructor(t,e){this._viewportRuler=t,this._document=e}attach(){}enable(){if(this._canBeEnabled()){let t=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=t.style.left||"",this._previousHTMLStyles.top=t.style.top||"",t.style.left=W(-this._previousScrollPosition.left),t.style.top=W(-this._previousScrollPosition.top),t.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){let t=this._document.documentElement,e=this._document.body,r=t.style,i=e.style,s=r.scrollBehavior||"",o=i.scrollBehavior||"";this._isEnabled=!1,r.left=this._previousHTMLStyles.left,r.top=this._previousHTMLStyles.top,t.classList.remove("cdk-global-scrollblock"),Sh&&(r.scrollBehavior=i.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),Sh&&(r.scrollBehavior=s,i.scrollBehavior=o)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;let e=this._document.documentElement,r=this._viewportRuler.getViewportSize();return e.scrollHeight>r.height||e.scrollWidth>r.width}};function Nh(n,t){return new Es(n.get(bs),n.get(D),n.get(Zn),t)}var Es=class{_scrollDispatcher;_ngZone;_viewportRuler;_config;_scrollSubscription=null;_overlayRef;_initialScrollPosition;constructor(t,e,r,i){this._scrollDispatcher=t,this._ngZone=e,this._viewportRuler=r,this._config=i}attach(t){this._overlayRef,this._overlayRef=t}enable(){if(this._scrollSubscription)return;let t=this._scrollDispatcher.scrolled(0).pipe(ae(e=>!e||!this._overlayRef.overlayElement.contains(e.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=t.subscribe(()=>{let e=this._viewportRuler.getViewportScrollPosition().top;Math.abs(e-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=t.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}_detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}};var Yr=class{enable(){}disable(){}attach(){}};function Xa(n,t){return t.some(e=>{let r=n.bottom<e.top,i=n.top>e.bottom,s=n.right<e.left,o=n.left>e.right;return r||i||s||o})}function Th(n,t){return t.some(e=>{let r=n.top<e.top,i=n.bottom>e.bottom,s=n.left<e.left,o=n.right>e.right;return r||i||s||o})}function Ts(n,t){return new ws(n.get(bs),n.get(Zn),n.get(D),t)}var ws=class{_scrollDispatcher;_viewportRuler;_ngZone;_config;_scrollSubscription=null;_overlayRef;constructor(t,e,r,i){this._scrollDispatcher=t,this._viewportRuler=e,this._ngZone=r,this._config=i}attach(t){this._overlayRef,this._overlayRef=t}enable(){if(!this._scrollSubscription){let t=this._config?this._config.scrollThrottle:0;this._scrollSubscription=this._scrollDispatcher.scrolled(t).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){let e=this._overlayRef.overlayElement.getBoundingClientRect(),{width:r,height:i}=this._viewportRuler.getViewportSize();Xa(e,[{width:r,height:i,bottom:i,right:r,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}})}}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}},xh=(()=>{class n{_injector=d(k);constructor(){}noop=()=>new Yr;close=e=>Nh(this._injector,e);block=()=>Mh(this._injector);reposition=e=>Ts(this._injector,e);static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),hn=class{positionStrategy;scrollStrategy=new Yr;panelClass="";hasBackdrop=!1;backdropClass="cdk-overlay-dark-backdrop";disableAnimations;width;height;minWidth;minHeight;maxWidth;maxHeight;direction;disposeOnNavigation=!1;constructor(t){if(t){let e=Object.keys(t);for(let r of e)t[r]!==void 0&&(this[r]=t[r])}}};var Is=class{connectionPair;scrollableViewProperties;constructor(t,e){this.connectionPair=t,this.scrollableViewProperties=e}};var Fh=(()=>{class n{_attachedOverlays=[];_document=d(w);_isAttached;constructor(){}ngOnDestroy(){this.detach()}add(e){this.remove(e),this._attachedOverlays.push(e)}remove(e){let r=this._attachedOverlays.indexOf(e);r>-1&&this._attachedOverlays.splice(r,1),this._attachedOverlays.length===0&&this.detach()}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Lh=(()=>{class n extends Fh{_ngZone=d(D);_renderer=d(we).createRenderer(null,null);_cleanupKeydown;add(e){super.add(e),this._isAttached||(this._ngZone.runOutsideAngular(()=>{this._cleanupKeydown=this._renderer.listen("body","keydown",this._keydownListener)}),this._isAttached=!0)}detach(){this._isAttached&&(this._cleanupKeydown?.(),this._isAttached=!1)}_keydownListener=e=>{let r=this._attachedOverlays;for(let i=r.length-1;i>-1;i--)if(r[i]._keydownEvents.observers.length>0){this._ngZone.run(()=>r[i]._keydownEvents.next(e));break}};static \u0275fac=(()=>{let e;return function(i){return(e||(e=Yt(n)))(i||n)}})();static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Uh=(()=>{class n extends Fh{_platform=d($);_ngZone=d(D);_renderer=d(we).createRenderer(null,null);_cursorOriginalValue;_cursorStyleIsSet=!1;_pointerDownEventTarget;_cleanups;add(e){if(super.add(e),!this._isAttached){let r=this._document.body,i={capture:!0},s=this._renderer;this._cleanups=this._ngZone.runOutsideAngular(()=>[s.listen(r,"pointerdown",this._pointerDownListener,i),s.listen(r,"click",this._clickListener,i),s.listen(r,"auxclick",this._clickListener,i),s.listen(r,"contextmenu",this._clickListener,i)]),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=r.style.cursor,r.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){this._isAttached&&(this._cleanups?.forEach(e=>e()),this._cleanups=void 0,this._platform.IOS&&this._cursorStyleIsSet&&(this._document.body.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1)}_pointerDownListener=e=>{this._pointerDownEventTarget=he(e)};_clickListener=e=>{let r=he(e),i=e.type==="click"&&this._pointerDownEventTarget?this._pointerDownEventTarget:r;this._pointerDownEventTarget=null;let s=this._attachedOverlays.slice();for(let o=s.length-1;o>-1;o--){let a=s[o];if(a._outsidePointerEvents.observers.length<1||!a.hasAttached())continue;if(Rh(a.overlayElement,r)||Rh(a.overlayElement,i))break;let c=a._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>c.next(e)):c.next(e)}};static \u0275fac=(()=>{let e;return function(i){return(e||(e=Yt(n)))(i||n)}})();static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Rh(n,t){let e=typeof ShadowRoot<"u"&&ShadowRoot,r=t;for(;r;){if(r===n)return!0;r=e&&r instanceof ShadowRoot?r.host:r.parentNode}return!1}var Bh=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["ng-component"]],hostAttrs:["cdk-overlay-style-loader",""],decls:0,vars:0,template:function(r,i){},styles:[`.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}
`],encapsulation:2,changeDetection:0})}return n})(),jh=(()=>{class n{_platform=d($);_containerElement;_document=d(w);_styleLoader=d(Ge);constructor(){}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._loadStyles(),this._containerElement||this._createContainer(),this._containerElement}_createContainer(){let e="cdk-overlay-container";if(this._platform.isBrowser||Ba()){let i=this._document.querySelectorAll(`.${e}[platform="server"], .${e}[platform="test"]`);for(let s=0;s<i.length;s++)i[s].remove()}let r=this._document.createElement("div");r.classList.add(e),Ba()?r.setAttribute("platform","test"):this._platform.isBrowser||r.setAttribute("platform","server"),this._document.body.appendChild(r),this._containerElement=r}_loadStyles(){this._styleLoader.load(Bh)}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Za=class{_renderer;_ngZone;element;_cleanupClick;_cleanupTransitionEnd;_fallbackTimeout;constructor(t,e,r,i){this._renderer=e,this._ngZone=r,this.element=t.createElement("div"),this.element.classList.add("cdk-overlay-backdrop"),this._cleanupClick=e.listen(this.element,"click",i)}detach(){this._ngZone.runOutsideAngular(()=>{let t=this.element;clearTimeout(this._fallbackTimeout),this._cleanupTransitionEnd?.(),this._cleanupTransitionEnd=this._renderer.listen(t,"transitionend",this.dispose),this._fallbackTimeout=setTimeout(this.dispose,500),t.style.pointerEvents="none",t.classList.remove("cdk-overlay-backdrop-showing")})}dispose=()=>{clearTimeout(this._fallbackTimeout),this._cleanupClick?.(),this._cleanupTransitionEnd?.(),this._cleanupClick=this._cleanupTransitionEnd=this._fallbackTimeout=void 0,this.element.remove()}},Ds=class{_portalOutlet;_host;_pane;_config;_ngZone;_keyboardDispatcher;_document;_location;_outsideClickDispatcher;_animationsDisabled;_injector;_renderer;_backdropClick=new A;_attachments=new A;_detachments=new A;_positionStrategy;_scrollStrategy;_locationChanges=me.EMPTY;_backdropRef=null;_detachContentMutationObserver;_detachContentAfterRenderRef;_previousHostParent;_keydownEvents=new A;_outsidePointerEvents=new A;_afterNextRenderRef;constructor(t,e,r,i,s,o,a,c,l,u=!1,p,f){this._portalOutlet=t,this._host=e,this._pane=r,this._config=i,this._ngZone=s,this._keyboardDispatcher=o,this._document=a,this._location=c,this._outsideClickDispatcher=l,this._animationsDisabled=u,this._injector=p,this._renderer=f,i.scrollStrategy&&(this._scrollStrategy=i.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=i.positionStrategy}get overlayElement(){return this._pane}get backdropElement(){return this._backdropRef?.element||null}get hostElement(){return this._host}attach(t){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);let e=this._portalOutlet.attach(t);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._afterNextRenderRef?.destroy(),this._afterNextRenderRef=st(()=>{this.hasAttached()&&this.updatePosition()},{injector:this._injector}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._completeDetachContent(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),typeof e?.onDestroy=="function"&&e.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),e}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();let t=this._portalOutlet.detach();return this._detachments.next(),this._completeDetachContent(),this._keyboardDispatcher.remove(this),this._detachContentWhenEmpty(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),t}dispose(){let t=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._backdropRef?.dispose(),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._afterNextRenderRef?.destroy(),this._previousHostParent=this._pane=this._host=this._backdropRef=null,t&&this._detachments.next(),this._detachments.complete(),this._completeDetachContent()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(t){t!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=t,this.hasAttached()&&(t.attach(this),this.updatePosition()))}updateSize(t){this._config=g(g({},this._config),t),this._updateElementSize()}setDirection(t){this._config=J(g({},this._config),{direction:t}),this._updateElementDirection()}addPanelClass(t){this._pane&&this._toggleClasses(this._pane,t,!0)}removePanelClass(t){this._pane&&this._toggleClasses(this._pane,t,!1)}getDirection(){let t=this._config.direction;return t?typeof t=="string"?t:t.value:"ltr"}updateScrollStrategy(t){t!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=t,this.hasAttached()&&(t.attach(this),t.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;let t=this._pane.style;t.width=W(this._config.width),t.height=W(this._config.height),t.minWidth=W(this._config.minWidth),t.minHeight=W(this._config.minHeight),t.maxWidth=W(this._config.maxWidth),t.maxHeight=W(this._config.maxHeight)}_togglePointerEvents(t){this._pane.style.pointerEvents=t?"":"none"}_attachBackdrop(){let t="cdk-overlay-backdrop-showing";this._backdropRef?.dispose(),this._backdropRef=new Za(this._document,this._renderer,this._ngZone,e=>{this._backdropClick.next(e)}),this._animationsDisabled&&this._backdropRef.element.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropRef.element,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropRef.element,this._host),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>this._backdropRef?.element.classList.add(t))}):this._backdropRef.element.classList.add(t)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){this._animationsDisabled?(this._backdropRef?.dispose(),this._backdropRef=null):this._backdropRef?.detach()}_toggleClasses(t,e,r){let i=Gn(e||[]).filter(s=>!!s);i.length&&(r?t.classList.add(...i):t.classList.remove(...i))}_detachContentWhenEmpty(){let t=!1;try{this._detachContentAfterRenderRef=st(()=>{t=!0,this._detachContent()},{injector:this._injector})}catch(e){if(t)throw e;this._detachContent()}globalThis.MutationObserver&&this._pane&&(this._detachContentMutationObserver||=new globalThis.MutationObserver(()=>{this._detachContent()}),this._detachContentMutationObserver.observe(this._pane,{childList:!0}))}_detachContent(){(!this._pane||!this._host||this._pane.children.length===0)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),this._completeDetachContent())}_completeDetachContent(){this._detachContentAfterRenderRef?.destroy(),this._detachContentAfterRenderRef=void 0,this._detachContentMutationObserver?.disconnect()}_disposeScrollStrategy(){let t=this._scrollStrategy;t?.disable(),t?.detach?.()}},Ah="cdk-overlay-connected-position-bounding-box",bv=/([A-Za-z%]+)$/;function Qa(n,t){return new Cs(t,n.get(Zn),n.get(w),n.get($),n.get(jh))}var Cs=class{_viewportRuler;_document;_platform;_overlayContainer;_overlayRef;_isInitialRender;_lastBoundingBoxSize={width:0,height:0};_isPushed=!1;_canPush=!0;_growAfterOpen=!1;_hasFlexibleDimensions=!0;_positionLocked=!1;_originRect;_overlayRect;_viewportRect;_containerRect;_viewportMargin=0;_scrollables=[];_preferredPositions=[];_origin;_pane;_isDisposed;_boundingBox;_lastPosition;_lastScrollVisibility;_positionChanges=new A;_resizeSubscription=me.EMPTY;_offsetX=0;_offsetY=0;_transformOriginSelector;_appliedPanelClasses=[];_previousPushAmount;positionChanges=this._positionChanges;get positions(){return this._preferredPositions}constructor(t,e,r,i,s){this._viewportRuler=e,this._document=r,this._platform=i,this._overlayContainer=s,this.setOrigin(t)}attach(t){this._overlayRef&&this._overlayRef,this._validatePositions(),t.hostElement.classList.add(Ah),this._overlayRef=t,this._boundingBox=t.hostElement,this._pane=t.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition){this.reapplyLastPosition();return}this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let t=this._originRect,e=this._overlayRect,r=this._viewportRect,i=this._containerRect,s=[],o;for(let a of this._preferredPositions){let c=this._getOriginPoint(t,i,a),l=this._getOverlayPoint(c,e,a),u=this._getOverlayFit(l,e,r,a);if(u.isCompletelyWithinViewport){this._isPushed=!1,this._applyPosition(a,c);return}if(this._canFitWithFlexibleDimensions(u,l,r)){s.push({position:a,origin:c,overlayRect:e,boundingBoxRect:this._calculateBoundingBoxRect(c,a)});continue}(!o||o.overlayFit.visibleArea<u.visibleArea)&&(o={overlayFit:u,overlayPoint:l,originPoint:c,position:a,overlayRect:e})}if(s.length){let a=null,c=-1;for(let l of s){let u=l.boundingBoxRect.width*l.boundingBoxRect.height*(l.position.weight||1);u>c&&(c=u,a=l)}this._isPushed=!1,this._applyPosition(a.position,a.origin);return}if(this._canPush){this._isPushed=!0,this._applyPosition(o.position,o.originPoint);return}this._applyPosition(o.position,o.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&dn(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(Ah),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;let t=this._lastPosition;if(t){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let e=this._getOriginPoint(this._originRect,this._containerRect,t);this._applyPosition(t,e)}else this.apply()}withScrollableContainers(t){return this._scrollables=t,this}withPositions(t){return this._preferredPositions=t,t.indexOf(this._lastPosition)===-1&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(t){return this._viewportMargin=t,this}withFlexibleDimensions(t=!0){return this._hasFlexibleDimensions=t,this}withGrowAfterOpen(t=!0){return this._growAfterOpen=t,this}withPush(t=!0){return this._canPush=t,this}withLockedPosition(t=!0){return this._positionLocked=t,this}setOrigin(t){return this._origin=t,this}withDefaultOffsetX(t){return this._offsetX=t,this}withDefaultOffsetY(t){return this._offsetY=t,this}withTransformOriginOn(t){return this._transformOriginSelector=t,this}_getOriginPoint(t,e,r){let i;if(r.originX=="center")i=t.left+t.width/2;else{let o=this._isRtl()?t.right:t.left,a=this._isRtl()?t.left:t.right;i=r.originX=="start"?o:a}e.left<0&&(i-=e.left);let s;return r.originY=="center"?s=t.top+t.height/2:s=r.originY=="top"?t.top:t.bottom,e.top<0&&(s-=e.top),{x:i,y:s}}_getOverlayPoint(t,e,r){let i;r.overlayX=="center"?i=-e.width/2:r.overlayX==="start"?i=this._isRtl()?-e.width:0:i=this._isRtl()?0:-e.width;let s;return r.overlayY=="center"?s=-e.height/2:s=r.overlayY=="top"?0:-e.height,{x:t.x+i,y:t.y+s}}_getOverlayFit(t,e,r,i){let s=Oh(e),{x:o,y:a}=t,c=this._getOffset(i,"x"),l=this._getOffset(i,"y");c&&(o+=c),l&&(a+=l);let u=0-o,p=o+s.width-r.width,f=0-a,m=a+s.height-r.height,v=this._subtractOverflows(s.width,u,p),C=this._subtractOverflows(s.height,f,m),R=v*C;return{visibleArea:R,isCompletelyWithinViewport:s.width*s.height===R,fitsInViewportVertically:C===s.height,fitsInViewportHorizontally:v==s.width}}_canFitWithFlexibleDimensions(t,e,r){if(this._hasFlexibleDimensions){let i=r.bottom-e.y,s=r.right-e.x,o=kh(this._overlayRef.getConfig().minHeight),a=kh(this._overlayRef.getConfig().minWidth),c=t.fitsInViewportVertically||o!=null&&o<=i,l=t.fitsInViewportHorizontally||a!=null&&a<=s;return c&&l}return!1}_pushOverlayOnScreen(t,e,r){if(this._previousPushAmount&&this._positionLocked)return{x:t.x+this._previousPushAmount.x,y:t.y+this._previousPushAmount.y};let i=Oh(e),s=this._viewportRect,o=Math.max(t.x+i.width-s.width,0),a=Math.max(t.y+i.height-s.height,0),c=Math.max(s.top-r.top-t.y,0),l=Math.max(s.left-r.left-t.x,0),u=0,p=0;return i.width<=s.width?u=l||-o:u=t.x<this._viewportMargin?s.left-r.left-t.x:0,i.height<=s.height?p=c||-a:p=t.y<this._viewportMargin?s.top-r.top-t.y:0,this._previousPushAmount={x:u,y:p},{x:t.x+u,y:t.y+p}}_applyPosition(t,e){if(this._setTransformOrigin(t),this._setOverlayElementStyles(e,t),this._setBoundingBoxStyles(e,t),t.panelClass&&this._addPanelClasses(t.panelClass),this._positionChanges.observers.length){let r=this._getScrollVisibility();if(t!==this._lastPosition||!this._lastScrollVisibility||!yv(this._lastScrollVisibility,r)){let i=new Is(t,r);this._positionChanges.next(i)}this._lastScrollVisibility=r}this._lastPosition=t,this._isInitialRender=!1}_setTransformOrigin(t){if(!this._transformOriginSelector)return;let e=this._boundingBox.querySelectorAll(this._transformOriginSelector),r,i=t.overlayY;t.overlayX==="center"?r="center":this._isRtl()?r=t.overlayX==="start"?"right":"left":r=t.overlayX==="start"?"left":"right";for(let s=0;s<e.length;s++)e[s].style.transformOrigin=`${r} ${i}`}_calculateBoundingBoxRect(t,e){let r=this._viewportRect,i=this._isRtl(),s,o,a;if(e.overlayY==="top")o=t.y,s=r.height-o+this._viewportMargin;else if(e.overlayY==="bottom")a=r.height-t.y+this._viewportMargin*2,s=r.height-a+this._viewportMargin;else{let m=Math.min(r.bottom-t.y+r.top,t.y),v=this._lastBoundingBoxSize.height;s=m*2,o=t.y-m,s>v&&!this._isInitialRender&&!this._growAfterOpen&&(o=t.y-v/2)}let c=e.overlayX==="start"&&!i||e.overlayX==="end"&&i,l=e.overlayX==="end"&&!i||e.overlayX==="start"&&i,u,p,f;if(l)f=r.width-t.x+this._viewportMargin*2,u=t.x-this._viewportMargin;else if(c)p=t.x,u=r.right-t.x;else{let m=Math.min(r.right-t.x+r.left,t.x),v=this._lastBoundingBoxSize.width;u=m*2,p=t.x-m,u>v&&!this._isInitialRender&&!this._growAfterOpen&&(p=t.x-v/2)}return{top:o,left:p,bottom:a,right:f,width:u,height:s}}_setBoundingBoxStyles(t,e){let r=this._calculateBoundingBoxRect(t,e);!this._isInitialRender&&!this._growAfterOpen&&(r.height=Math.min(r.height,this._lastBoundingBoxSize.height),r.width=Math.min(r.width,this._lastBoundingBoxSize.width));let i={};if(this._hasExactPosition())i.top=i.left="0",i.bottom=i.right=i.maxHeight=i.maxWidth="",i.width=i.height="100%";else{let s=this._overlayRef.getConfig().maxHeight,o=this._overlayRef.getConfig().maxWidth;i.height=W(r.height),i.top=W(r.top),i.bottom=W(r.bottom),i.width=W(r.width),i.left=W(r.left),i.right=W(r.right),e.overlayX==="center"?i.alignItems="center":i.alignItems=e.overlayX==="end"?"flex-end":"flex-start",e.overlayY==="center"?i.justifyContent="center":i.justifyContent=e.overlayY==="bottom"?"flex-end":"flex-start",s&&(i.maxHeight=W(s)),o&&(i.maxWidth=W(o))}this._lastBoundingBoxSize=r,dn(this._boundingBox.style,i)}_resetBoundingBoxStyles(){dn(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){dn(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(t,e){let r={},i=this._hasExactPosition(),s=this._hasFlexibleDimensions,o=this._overlayRef.getConfig();if(i){let u=this._viewportRuler.getViewportScrollPosition();dn(r,this._getExactOverlayY(e,t,u)),dn(r,this._getExactOverlayX(e,t,u))}else r.position="static";let a="",c=this._getOffset(e,"x"),l=this._getOffset(e,"y");c&&(a+=`translateX(${c}px) `),l&&(a+=`translateY(${l}px)`),r.transform=a.trim(),o.maxHeight&&(i?r.maxHeight=W(o.maxHeight):s&&(r.maxHeight="")),o.maxWidth&&(i?r.maxWidth=W(o.maxWidth):s&&(r.maxWidth="")),dn(this._pane.style,r)}_getExactOverlayY(t,e,r){let i={top:"",bottom:""},s=this._getOverlayPoint(e,this._overlayRect,t);if(this._isPushed&&(s=this._pushOverlayOnScreen(s,this._overlayRect,r)),t.overlayY==="bottom"){let o=this._document.documentElement.clientHeight;i.bottom=`${o-(s.y+this._overlayRect.height)}px`}else i.top=W(s.y);return i}_getExactOverlayX(t,e,r){let i={left:"",right:""},s=this._getOverlayPoint(e,this._overlayRect,t);this._isPushed&&(s=this._pushOverlayOnScreen(s,this._overlayRect,r));let o;if(this._isRtl()?o=t.overlayX==="end"?"left":"right":o=t.overlayX==="end"?"right":"left",o==="right"){let a=this._document.documentElement.clientWidth;i.right=`${a-(s.x+this._overlayRect.width)}px`}else i.left=W(s.x);return i}_getScrollVisibility(){let t=this._getOriginRect(),e=this._pane.getBoundingClientRect(),r=this._scrollables.map(i=>i.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:Th(t,r),isOriginOutsideView:Xa(t,r),isOverlayClipped:Th(e,r),isOverlayOutsideView:Xa(e,r)}}_subtractOverflows(t,...e){return e.reduce((r,i)=>r-Math.max(i,0),t)}_getNarrowedViewportRect(){let t=this._document.documentElement.clientWidth,e=this._document.documentElement.clientHeight,r=this._viewportRuler.getViewportScrollPosition();return{top:r.top+this._viewportMargin,left:r.left+this._viewportMargin,right:r.left+t-this._viewportMargin,bottom:r.top+e-this._viewportMargin,width:t-2*this._viewportMargin,height:e-2*this._viewportMargin}}_isRtl(){return this._overlayRef.getDirection()==="rtl"}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(t,e){return e==="x"?t.offsetX==null?this._offsetX:t.offsetX:t.offsetY==null?this._offsetY:t.offsetY}_validatePositions(){}_addPanelClasses(t){this._pane&&Gn(t).forEach(e=>{e!==""&&this._appliedPanelClasses.indexOf(e)===-1&&(this._appliedPanelClasses.push(e),this._pane.classList.add(e))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(t=>{this._pane.classList.remove(t)}),this._appliedPanelClasses=[])}_getOriginRect(){let t=this._origin;if(t instanceof j)return t.nativeElement.getBoundingClientRect();if(t instanceof Element)return t.getBoundingClientRect();let e=t.width||0,r=t.height||0;return{top:t.y,bottom:t.y+r,left:t.x,right:t.x+e,height:r,width:e}}};function dn(n,t){for(let e in t)t.hasOwnProperty(e)&&(n[e]=t[e]);return n}function kh(n){if(typeof n!="number"&&n!=null){let[t,e]=n.split(bv);return!e||e==="px"?parseFloat(t):null}return n||null}function Oh(n){return{top:Math.floor(n.top),right:Math.floor(n.right),bottom:Math.floor(n.bottom),left:Math.floor(n.left),width:Math.floor(n.width),height:Math.floor(n.height)}}function yv(n,t){return n===t?!0:n.isOriginClipped===t.isOriginClipped&&n.isOriginOutsideView===t.isOriginOutsideView&&n.isOverlayClipped===t.isOverlayClipped&&n.isOverlayOutsideView===t.isOverlayOutsideView}var Ph="cdk-global-overlay-wrapper";function Rs(n){return new Ss}var Ss=class{_overlayRef;_cssPosition="static";_topOffset="";_bottomOffset="";_alignItems="";_xPosition="";_xOffset="";_width="";_height="";_isDisposed=!1;attach(t){let e=t.getConfig();this._overlayRef=t,this._width&&!e.width&&t.updateSize({width:this._width}),this._height&&!e.height&&t.updateSize({height:this._height}),t.hostElement.classList.add(Ph),this._isDisposed=!1}top(t=""){return this._bottomOffset="",this._topOffset=t,this._alignItems="flex-start",this}left(t=""){return this._xOffset=t,this._xPosition="left",this}bottom(t=""){return this._topOffset="",this._bottomOffset=t,this._alignItems="flex-end",this}right(t=""){return this._xOffset=t,this._xPosition="right",this}start(t=""){return this._xOffset=t,this._xPosition="start",this}end(t=""){return this._xOffset=t,this._xPosition="end",this}width(t=""){return this._overlayRef?this._overlayRef.updateSize({width:t}):this._width=t,this}height(t=""){return this._overlayRef?this._overlayRef.updateSize({height:t}):this._height=t,this}centerHorizontally(t=""){return this.left(t),this._xPosition="center",this}centerVertically(t=""){return this.top(t),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;let t=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement.style,r=this._overlayRef.getConfig(),{width:i,height:s,maxWidth:o,maxHeight:a}=r,c=(i==="100%"||i==="100vw")&&(!o||o==="100%"||o==="100vw"),l=(s==="100%"||s==="100vh")&&(!a||a==="100%"||a==="100vh"),u=this._xPosition,p=this._xOffset,f=this._overlayRef.getConfig().direction==="rtl",m="",v="",C="";c?C="flex-start":u==="center"?(C="center",f?v=p:m=p):f?u==="left"||u==="end"?(C="flex-end",m=p):(u==="right"||u==="start")&&(C="flex-start",v=p):u==="left"||u==="start"?(C="flex-start",m=p):(u==="right"||u==="end")&&(C="flex-end",v=p),t.position=this._cssPosition,t.marginLeft=c?"0":m,t.marginTop=l?"0":this._topOffset,t.marginBottom=this._bottomOffset,t.marginRight=c?"0":v,e.justifyContent=C,e.alignItems=l?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;let t=this._overlayRef.overlayElement.style,e=this._overlayRef.hostElement,r=e.style;e.classList.remove(Ph),r.justifyContent=r.alignItems=t.marginTop=t.marginBottom=t.marginLeft=t.marginRight=t.position="",this._overlayRef=null,this._isDisposed=!0}},Vh=(()=>{class n{_injector=d(k);constructor(){}global(){return Rs()}flexibleConnectedTo(e){return Qa(this._injector,e)}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();function Xr(n,t){n.get(Ge).load(Bh);let e=n.get(jh),r=n.get(w),i=n.get(Hr),s=n.get(Jt),o=n.get(ps),a=r.createElement("div"),c=r.createElement("div");c.id=i.getId("cdk-overlay-"),c.classList.add("cdk-overlay-pane"),a.appendChild(c),e.getContainerElement().appendChild(a);let l=new _s(c,s,n),u=new hn(t),p=n.get(Ve,null,{optional:!0})||n.get(we).createRenderer(null,null);return u.direction=u.direction||o.value,new Ds(l,a,c,u,n.get(D),n.get(Lh),r,n.get(St),n.get(Uh),t?.disableAnimations??n.get(yi,null,{optional:!0})==="NoopAnimations",n.get(le),p)}var $h=(()=>{class n{scrollStrategies=d(xh);_positionBuilder=d(Vh);_injector=d(k);constructor(){}create(e){return Xr(this._injector,e)}position(){return this._positionBuilder}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),Ev=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}],zh=new y("cdk-connected-overlay-scroll-strategy",{providedIn:"root",factory:()=>{let n=d(k);return()=>Ts(n)}}),Ja=(()=>{class n{elementRef=d(j);constructor(){}static \u0275fac=function(r){return new(r||n)};static \u0275dir=B({type:n,selectors:[["","cdk-overlay-origin",""],["","overlay-origin",""],["","cdkOverlayOrigin",""]],exportAs:["cdkOverlayOrigin"]})}return n})(),wv=(()=>{class n{_dir=d(ps,{optional:!0});_injector=d(k);_overlayRef;_templatePortal;_backdropSubscription=me.EMPTY;_attachSubscription=me.EMPTY;_detachSubscription=me.EMPTY;_positionSubscription=me.EMPTY;_offsetX;_offsetY;_position;_scrollStrategyFactory=d(zh);_disposeOnNavigation=!1;_ngZone=d(D);origin;positions;positionStrategy;get offsetX(){return this._offsetX}set offsetX(e){this._offsetX=e,this._position&&this._updatePositionStrategy(this._position)}get offsetY(){return this._offsetY}set offsetY(e){this._offsetY=e,this._position&&this._updatePositionStrategy(this._position)}width;height;minWidth;minHeight;backdropClass;panelClass;viewportMargin=0;scrollStrategy;open=!1;disableClose=!1;transformOriginSelector;hasBackdrop=!1;lockPosition=!1;flexibleDimensions=!1;growAfterOpen=!1;push=!1;get disposeOnNavigation(){return this._disposeOnNavigation}set disposeOnNavigation(e){this._disposeOnNavigation=e}backdropClick=new Q;positionChange=new Q;attach=new Q;detach=new Q;overlayKeydown=new Q;overlayOutsideClick=new Q;constructor(){let e=d(Zt),r=d(rt);this._templatePortal=new un(e,r),this.scrollStrategy=this._scrollStrategyFactory()}get overlayRef(){return this._overlayRef}get dir(){return this._dir?this._dir.value:"ltr"}ngOnDestroy(){this._attachSubscription.unsubscribe(),this._detachSubscription.unsubscribe(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this._overlayRef?.dispose()}ngOnChanges(e){this._position&&(this._updatePositionStrategy(this._position),this._overlayRef?.updateSize({width:this.width,minWidth:this.minWidth,height:this.height,minHeight:this.minHeight}),e.origin&&this.open&&this._position.apply()),e.open&&(this.open?this.attachOverlay():this.detachOverlay())}_createOverlay(){(!this.positions||!this.positions.length)&&(this.positions=Ev);let e=this._overlayRef=Xr(this._injector,this._buildConfig());this._attachSubscription=e.attachments().subscribe(()=>this.attach.emit()),this._detachSubscription=e.detachments().subscribe(()=>this.detach.emit()),e.keydownEvents().subscribe(r=>{this.overlayKeydown.next(r),r.keyCode===27&&!this.disableClose&&!us(r)&&(r.preventDefault(),this.detachOverlay())}),this._overlayRef.outsidePointerEvents().subscribe(r=>{let i=this._getOriginElement(),s=he(r);(!i||i!==s&&!i.contains(s))&&this.overlayOutsideClick.next(r)})}_buildConfig(){let e=this._position=this.positionStrategy||this._createPositionStrategy(),r=new hn({direction:this._dir||"ltr",positionStrategy:e,scrollStrategy:this.scrollStrategy,hasBackdrop:this.hasBackdrop,disposeOnNavigation:this.disposeOnNavigation});return(this.width||this.width===0)&&(r.width=this.width),(this.height||this.height===0)&&(r.height=this.height),(this.minWidth||this.minWidth===0)&&(r.minWidth=this.minWidth),(this.minHeight||this.minHeight===0)&&(r.minHeight=this.minHeight),this.backdropClass&&(r.backdropClass=this.backdropClass),this.panelClass&&(r.panelClass=this.panelClass),r}_updatePositionStrategy(e){let r=this.positions.map(i=>({originX:i.originX,originY:i.originY,overlayX:i.overlayX,overlayY:i.overlayY,offsetX:i.offsetX||this.offsetX,offsetY:i.offsetY||this.offsetY,panelClass:i.panelClass||void 0}));return e.setOrigin(this._getOrigin()).withPositions(r).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector)}_createPositionStrategy(){let e=Qa(this._injector,this._getOrigin());return this._updatePositionStrategy(e),e}_getOrigin(){return this.origin instanceof Ja?this.origin.elementRef:this.origin}_getOriginElement(){return this.origin instanceof Ja?this.origin.elementRef.nativeElement:this.origin instanceof j?this.origin.nativeElement:typeof Element<"u"&&this.origin instanceof Element?this.origin:null}attachOverlay(){this._overlayRef?this._overlayRef.getConfig().hasBackdrop=this.hasBackdrop:this._createOverlay(),this._overlayRef.hasAttached()||this._overlayRef.attach(this._templatePortal),this.hasBackdrop?this._backdropSubscription=this._overlayRef.backdropClick().subscribe(e=>{this.backdropClick.emit(e)}):this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.positionChange.observers.length>0&&(this._positionSubscription=this._position.positionChanges.pipe($l(()=>this.positionChange.observers.length>0)).subscribe(e=>{this._ngZone.run(()=>this.positionChange.emit(e)),this.positionChange.observers.length===0&&this._positionSubscription.unsubscribe()})),this.open=!0}detachOverlay(){this._overlayRef?.detach(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.open=!1}static \u0275fac=function(r){return new(r||n)};static \u0275dir=B({type:n,selectors:[["","cdk-connected-overlay",""],["","connected-overlay",""],["","cdkConnectedOverlay",""]],inputs:{origin:[0,"cdkConnectedOverlayOrigin","origin"],positions:[0,"cdkConnectedOverlayPositions","positions"],positionStrategy:[0,"cdkConnectedOverlayPositionStrategy","positionStrategy"],offsetX:[0,"cdkConnectedOverlayOffsetX","offsetX"],offsetY:[0,"cdkConnectedOverlayOffsetY","offsetY"],width:[0,"cdkConnectedOverlayWidth","width"],height:[0,"cdkConnectedOverlayHeight","height"],minWidth:[0,"cdkConnectedOverlayMinWidth","minWidth"],minHeight:[0,"cdkConnectedOverlayMinHeight","minHeight"],backdropClass:[0,"cdkConnectedOverlayBackdropClass","backdropClass"],panelClass:[0,"cdkConnectedOverlayPanelClass","panelClass"],viewportMargin:[0,"cdkConnectedOverlayViewportMargin","viewportMargin"],scrollStrategy:[0,"cdkConnectedOverlayScrollStrategy","scrollStrategy"],open:[0,"cdkConnectedOverlayOpen","open"],disableClose:[0,"cdkConnectedOverlayDisableClose","disableClose"],transformOriginSelector:[0,"cdkConnectedOverlayTransformOriginOn","transformOriginSelector"],hasBackdrop:[2,"cdkConnectedOverlayHasBackdrop","hasBackdrop",Y],lockPosition:[2,"cdkConnectedOverlayLockPosition","lockPosition",Y],flexibleDimensions:[2,"cdkConnectedOverlayFlexibleDimensions","flexibleDimensions",Y],growAfterOpen:[2,"cdkConnectedOverlayGrowAfterOpen","growAfterOpen",Y],push:[2,"cdkConnectedOverlayPush","push",Y],disposeOnNavigation:[2,"cdkConnectedOverlayDisposeOnNavigation","disposeOnNavigation",Y]},outputs:{backdropClick:"backdropClick",positionChange:"positionChange",attach:"attach",detach:"detach",overlayKeydown:"overlayKeydown",overlayOutsideClick:"overlayOutsideClick"},exportAs:["cdkConnectedOverlay"],features:[ke]})}return n})();function Iv(n){let t=d(k);return()=>Ts(t)}var Dv={provide:zh,useFactory:Iv},ec=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({providers:[$h,Dv],imports:[kt,vs,Ya,Ya]})}return n})();function Cv(n,t){if(n&1){let e=du();Qt(0,"div",1)(1,"button",2),fr("click",function(){Wl(e);let i=Oo();return Gl(i.action())}),Po(2),It()()}if(n&2){let e=Oo();Tn(2),Mo(" ",e.data.action," ")}}var Sv=["label"];function Tv(n,t){}var Rv=Math.pow(2,31)-1,Zr=class{_overlayRef;instance;containerInstance;_afterDismissed=new A;_afterOpened=new A;_onAction=new A;_durationTimeoutId;_dismissedByAction=!1;constructor(t,e){this._overlayRef=e,this.containerInstance=t,t._onExit.subscribe(()=>this._finishDismiss())}dismiss(){this._afterDismissed.closed||this.containerInstance.exit(),clearTimeout(this._durationTimeoutId)}dismissWithAction(){this._onAction.closed||(this._dismissedByAction=!0,this._onAction.next(),this._onAction.complete(),this.dismiss()),clearTimeout(this._durationTimeoutId)}closeWithAction(){this.dismissWithAction()}_dismissAfter(t){this._durationTimeoutId=setTimeout(()=>this.dismiss(),Math.min(t,Rv))}_open(){this._afterOpened.closed||(this._afterOpened.next(),this._afterOpened.complete())}_finishDismiss(){this._overlayRef.dispose(),this._onAction.closed||this._onAction.complete(),this._afterDismissed.next({dismissedByAction:this._dismissedByAction}),this._afterDismissed.complete(),this._dismissedByAction=!1}afterDismissed(){return this._afterDismissed}afterOpened(){return this.containerInstance._onEnter}onAction(){return this._onAction}},Hh=new y("MatSnackBarData"),Jn=class{politeness="polite";announcementMessage="";viewContainerRef;duration=0;panelClass;direction;data=null;horizontalPosition="center";verticalPosition="bottom"},Av=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275dir=B({type:n,selectors:[["","matSnackBarLabel",""]],hostAttrs:[1,"mat-mdc-snack-bar-label","mdc-snackbar__label"]})}return n})(),kv=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275dir=B({type:n,selectors:[["","matSnackBarActions",""]],hostAttrs:[1,"mat-mdc-snack-bar-actions","mdc-snackbar__actions"]})}return n})(),Ov=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275dir=B({type:n,selectors:[["","matSnackBarAction",""]],hostAttrs:[1,"mat-mdc-snack-bar-action","mdc-snackbar__action"]})}return n})(),Wh=(()=>{class n{snackBarRef=d(Zr);data=d(Hh);constructor(){}action(){this.snackBarRef.dismissWithAction()}get hasAction(){return!!this.data.action}static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["simple-snack-bar"]],hostAttrs:[1,"mat-mdc-simple-snack-bar"],exportAs:["matSnackBar"],decls:3,vars:2,consts:[["matSnackBarLabel",""],["matSnackBarActions",""],["matButton","","matSnackBarAction","",3,"click"]],template:function(r,i){r&1&&(Qt(0,"div",0),Po(1),It(),lu(2,Cv,3,1,"div",1)),r&2&&(Tn(),Mo(" ",i.data.message,`
`),Tn(),uu(i.hasAction?2:-1))},dependencies:[vh,Av,kv,Ov],styles:[`.mat-mdc-simple-snack-bar{display:flex}
`],encapsulation:2,changeDetection:0})}return n})(),tc="_mat-snack-bar-enter",nc="_mat-snack-bar-exit",Pv=(()=>{class n extends Xn{_ngZone=d(D);_elementRef=d(j);_changeDetectorRef=d(Dt);_platform=d($);_animationsDisabled=ft();snackBarConfig=d(Jn);_document=d(w);_trackedModals=new Set;_enterFallback;_exitFallback;_injector=d(k);_announceDelay=150;_announceTimeoutId;_destroyed=!1;_portalOutlet;_onAnnounce=new A;_onExit=new A;_onEnter=new A;_animationState="void";_live;_label;_role;_liveElementId=d(Hr).getId("mat-snack-bar-container-live-");constructor(){super();let e=this.snackBarConfig;e.politeness==="assertive"&&!e.announcementMessage?this._live="assertive":e.politeness==="off"?this._live="off":this._live="polite",this._platform.FIREFOX&&(this._live==="polite"&&(this._role="status"),this._live==="assertive"&&(this._role="alert"))}attachComponentPortal(e){this._assertNotAttached();let r=this._portalOutlet.attachComponentPortal(e);return this._afterPortalAttached(),r}attachTemplatePortal(e){this._assertNotAttached();let r=this._portalOutlet.attachTemplatePortal(e);return this._afterPortalAttached(),r}attachDomPortal=e=>{this._assertNotAttached();let r=this._portalOutlet.attachDomPortal(e);return this._afterPortalAttached(),r};onAnimationEnd(e){e===nc?this._completeExit():e===tc&&(clearTimeout(this._enterFallback),this._ngZone.run(()=>{this._onEnter.next(),this._onEnter.complete()}))}enter(){this._destroyed||(this._animationState="visible",this._changeDetectorRef.markForCheck(),this._changeDetectorRef.detectChanges(),this._screenReaderAnnounce(),this._animationsDisabled?st(()=>{this._ngZone.run(()=>queueMicrotask(()=>this.onAnimationEnd(tc)))},{injector:this._injector}):(clearTimeout(this._enterFallback),this._enterFallback=setTimeout(()=>{this._elementRef.nativeElement.classList.add("mat-snack-bar-fallback-visible"),this.onAnimationEnd(tc)},200)))}exit(){return this._destroyed?b(void 0):(this._ngZone.run(()=>{this._animationState="hidden",this._changeDetectorRef.markForCheck(),this._elementRef.nativeElement.setAttribute("mat-exit",""),clearTimeout(this._announceTimeoutId),this._animationsDisabled?st(()=>{this._ngZone.run(()=>queueMicrotask(()=>this.onAnimationEnd(nc)))},{injector:this._injector}):(clearTimeout(this._exitFallback),this._exitFallback=setTimeout(()=>this.onAnimationEnd(nc),200))}),this._onExit)}ngOnDestroy(){this._destroyed=!0,this._clearFromModals(),this._completeExit()}_completeExit(){clearTimeout(this._exitFallback),queueMicrotask(()=>{this._onExit.next(),this._onExit.complete()})}_afterPortalAttached(){let e=this._elementRef.nativeElement,r=this.snackBarConfig.panelClass;r&&(Array.isArray(r)?r.forEach(o=>e.classList.add(o)):e.classList.add(r)),this._exposeToModals();let i=this._label.nativeElement,s="mdc-snackbar__label";i.classList.toggle(s,!i.querySelector(`.${s}`))}_exposeToModals(){let e=this._liveElementId,r=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let i=0;i<r.length;i++){let s=r[i],o=s.getAttribute("aria-owns");this._trackedModals.add(s),o?o.indexOf(e)===-1&&s.setAttribute("aria-owns",o+" "+e):s.setAttribute("aria-owns",e)}}_clearFromModals(){this._trackedModals.forEach(e=>{let r=e.getAttribute("aria-owns");if(r){let i=r.replace(this._liveElementId,"").trim();i.length>0?e.setAttribute("aria-owns",i):e.removeAttribute("aria-owns")}}),this._trackedModals.clear()}_assertNotAttached(){this._portalOutlet.hasAttached()}_screenReaderAnnounce(){this._announceTimeoutId||this._ngZone.runOutsideAngular(()=>{this._announceTimeoutId=setTimeout(()=>{if(this._destroyed)return;let e=this._elementRef.nativeElement,r=e.querySelector("[aria-hidden]"),i=e.querySelector("[aria-live]");if(r&&i){let s=null;this._platform.isBrowser&&document.activeElement instanceof HTMLElement&&r.contains(document.activeElement)&&(s=document.activeElement),r.removeAttribute("aria-hidden"),i.appendChild(r),s?.focus(),this._onAnnounce.next(),this._onAnnounce.complete()}},this._announceDelay)})}static \u0275fac=function(r){return new(r||n)};static \u0275cmp=K({type:n,selectors:[["mat-snack-bar-container"]],viewQuery:function(r,i){if(r&1&&(wi(qa,7),wi(Sv,7)),r&2){let s;An(s=kn())&&(i._portalOutlet=s.first),An(s=kn())&&(i._label=s.first)}},hostAttrs:[1,"mdc-snackbar","mat-mdc-snack-bar-container"],hostVars:6,hostBindings:function(r,i){r&1&&fr("animationend",function(o){return i.onAnimationEnd(o.animationName)})("animationcancel",function(o){return i.onAnimationEnd(o.animationName)}),r&2&&Pe("mat-snack-bar-container-enter",i._animationState==="visible")("mat-snack-bar-container-exit",i._animationState==="hidden")("mat-snack-bar-container-animations-enabled",!i._animationsDisabled)},features:[it],decls:6,vars:3,consts:[["label",""],[1,"mdc-snackbar__surface","mat-mdc-snackbar-surface"],[1,"mat-mdc-snack-bar-label"],["aria-hidden","true"],["cdkPortalOutlet",""]],template:function(r,i){r&1&&(Qt(0,"div",1)(1,"div",2,0)(3,"div",3),ou(4,Tv,0,0,"ng-template",4),It(),Oe(5,"div"),It()()),r&2&&(Tn(5),ot("aria-live",i._live)("role",i._role)("id",i._liveElementId))},dependencies:[qa],styles:[`@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mat-snack-bar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mat-snack-bar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mat-snack-bar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mat-snack-bar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-snack-bar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mat-snack-bar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mat-snack-bar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-button-text-state-layer-color: currentColor;--mat-button-text-ripple-color: currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}
`],encapsulation:2})}return n})();function Mv(){return new Jn}var Nv=new y("mat-snack-bar-default-options",{providedIn:"root",factory:Mv}),xv=(()=>{class n{_live=d(Ma);_injector=d(k);_breakpointObserver=d(zr);_parentSnackBar=d(n,{optional:!0,skipSelf:!0});_defaultConfig=d(Nv);_animationsDisabled=ft();_snackBarRefAtThisLevel=null;simpleSnackBarComponent=Wh;snackBarContainerComponent=Pv;handsetCssClass="mat-mdc-snack-bar-handset";get _openedSnackBarRef(){let e=this._parentSnackBar;return e?e._openedSnackBarRef:this._snackBarRefAtThisLevel}set _openedSnackBarRef(e){this._parentSnackBar?this._parentSnackBar._openedSnackBarRef=e:this._snackBarRefAtThisLevel=e}constructor(){}openFromComponent(e,r){return this._attach(e,r)}openFromTemplate(e,r){return this._attach(e,r)}open(e,r="",i){let s=g(g({},this._defaultConfig),i);return s.data={message:e,action:r},s.announcementMessage===e&&(s.announcementMessage=void 0),this.openFromComponent(this.simpleSnackBarComponent,s)}dismiss(){this._openedSnackBarRef&&this._openedSnackBarRef.dismiss()}ngOnDestroy(){this._snackBarRefAtThisLevel&&this._snackBarRefAtThisLevel.dismiss()}_attachSnackBarContainer(e,r){let i=r&&r.viewContainerRef&&r.viewContainerRef.injector,s=k.create({parent:i||this._injector,providers:[{provide:Jn,useValue:r}]}),o=new Yn(this.snackBarContainerComponent,r.viewContainerRef,s),a=e.attach(o);return a.instance.snackBarConfig=r,a.instance}_attach(e,r){let i=g(g(g({},new Jn),this._defaultConfig),r),s=this._createOverlay(i),o=this._attachSnackBarContainer(s,i),a=new Zr(o,s);if(e instanceof Zt){let c=new un(e,null,{$implicit:i.data,snackBarRef:a});a.instance=o.attachTemplatePortal(c)}else{let c=this._createInjector(i,a),l=new Yn(e,void 0,c),u=o.attachComponentPortal(l);a.instance=u.instance}return this._breakpointObserver.observe(sh.HandsetPortrait).pipe(je(s.detachments())).subscribe(c=>{s.overlayElement.classList.toggle(this.handsetCssClass,c.matches)}),i.announcementMessage&&o._onAnnounce.subscribe(()=>{this._live.announce(i.announcementMessage,i.politeness)}),this._animateSnackBar(a,i),this._openedSnackBarRef=a,this._openedSnackBarRef}_animateSnackBar(e,r){e.afterDismissed().subscribe(()=>{this._openedSnackBarRef==e&&(this._openedSnackBarRef=null),r.announcementMessage&&this._live.clear()}),r.duration&&r.duration>0&&e.afterOpened().subscribe(()=>e._dismissAfter(r.duration)),this._openedSnackBarRef?(this._openedSnackBarRef.afterDismissed().subscribe(()=>{e.containerInstance.enter()}),this._openedSnackBarRef.dismiss()):e.containerInstance.enter()}_createOverlay(e){let r=new hn;r.direction=e.direction;let i=Rs(this._injector),s=e.direction==="rtl",o=e.horizontalPosition==="left"||e.horizontalPosition==="start"&&!s||e.horizontalPosition==="end"&&s,a=!o&&e.horizontalPosition!=="center";return o?i.left("0"):a?i.right("0"):i.centerHorizontally(),e.verticalPosition==="top"?i.top("0"):i.bottom("0"),r.positionStrategy=i,r.disableAnimations=this._animationsDisabled,Xr(this._injector,r)}_createInjector(e,r){let i=e&&e.viewContainerRef&&e.viewContainerRef.injector;return k.create({parent:i||this._injector,providers:[{provide:Zr,useValue:r},{provide:Hh,useValue:e.data}]})}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var LP=(()=>{class n{static \u0275fac=function(r){return new(r||n)};static \u0275mod=x({type:n});static \u0275inj=N({providers:[xv],imports:[ec,vs,bh,Ae,Wh,Ae]})}return n})();var Gh=()=>{};var Yh=function(n){let t=[],e=0;for(let r=0;r<n.length;r++){let i=n.charCodeAt(r);i<128?t[e++]=i:i<2048?(t[e++]=i>>6|192,t[e++]=i&63|128):(i&64512)===55296&&r+1<n.length&&(n.charCodeAt(r+1)&64512)===56320?(i=65536+((i&1023)<<10)+(n.charCodeAt(++r)&1023),t[e++]=i>>18|240,t[e++]=i>>12&63|128,t[e++]=i>>6&63|128,t[e++]=i&63|128):(t[e++]=i>>12|224,t[e++]=i>>6&63|128,t[e++]=i&63|128)}return t},Fv=function(n){let t=[],e=0,r=0;for(;e<n.length;){let i=n[e++];if(i<128)t[r++]=String.fromCharCode(i);else if(i>191&&i<224){let s=n[e++];t[r++]=String.fromCharCode((i&31)<<6|s&63)}else if(i>239&&i<365){let s=n[e++],o=n[e++],a=n[e++],c=((i&7)<<18|(s&63)<<12|(o&63)<<6|a&63)-65536;t[r++]=String.fromCharCode(55296+(c>>10)),t[r++]=String.fromCharCode(56320+(c&1023))}else{let s=n[e++],o=n[e++];t[r++]=String.fromCharCode((i&15)<<12|(s&63)<<6|o&63)}}return t.join("")},As={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:typeof atob=="function",encodeByteArray(n,t){if(!Array.isArray(n))throw Error("encodeByteArray takes an array as a parameter");this.init_();let e=t?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[];for(let i=0;i<n.length;i+=3){let s=n[i],o=i+1<n.length,a=o?n[i+1]:0,c=i+2<n.length,l=c?n[i+2]:0,u=s>>2,p=(s&3)<<4|a>>4,f=(a&15)<<2|l>>6,m=l&63;c||(m=64,o||(f=64)),r.push(e[u],e[p],e[f],e[m])}return r.join("")},encodeString(n,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(n):this.encodeByteArray(Yh(n),t)},decodeString(n,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(n):Fv(this.decodeStringToByteArray(n,t))},decodeStringToByteArray(n,t){this.init_();let e=t?this.charToByteMapWebSafe_:this.charToByteMap_,r=[];for(let i=0;i<n.length;){let s=e[n.charAt(i++)],a=i<n.length?e[n.charAt(i)]:0;++i;let l=i<n.length?e[n.charAt(i)]:64;++i;let p=i<n.length?e[n.charAt(i)]:64;if(++i,s==null||a==null||l==null||p==null)throw new ic;let f=s<<2|a>>4;if(r.push(f),l!==64){let m=a<<4&240|l>>2;if(r.push(m),p!==64){let v=l<<6&192|p;r.push(v)}}}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let n=0;n<this.ENCODED_VALS.length;n++)this.byteToCharMap_[n]=this.ENCODED_VALS.charAt(n),this.charToByteMap_[this.byteToCharMap_[n]]=n,this.byteToCharMapWebSafe_[n]=this.ENCODED_VALS_WEBSAFE.charAt(n),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[n]]=n,n>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(n)]=n,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(n)]=n)}}},ic=class extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}},Lv=function(n){let t=Yh(n);return As.encodeByteArray(t,!0)},Qr=function(n){return Lv(n).replace(/\./g,"")},ks=function(n){try{return As.decodeString(n,!0)}catch(t){console.error("base64Decode failed: ",t)}return null};function Xh(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("Unable to locate global object.")}var Uv=()=>Xh().__FIREBASE_DEFAULTS__,Bv=()=>{if(typeof process>"u"||typeof process.env>"u")return;let n=process.env.__FIREBASE_DEFAULTS__;if(n)return JSON.parse(n)},jv=()=>{if(typeof document>"u")return;let n;try{n=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch{return}let t=n&&ks(n[1]);return t&&JSON.parse(t)},oc=()=>{try{return Gh()||Uv()||Bv()||jv()}catch(n){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${n}`);return}},ac=n=>{var t,e;return(e=(t=oc())===null||t===void 0?void 0:t.emulatorHosts)===null||e===void 0?void 0:e[n]},Os=n=>{let t=ac(n);if(!t)return;let e=t.lastIndexOf(":");if(e<=0||e+1===t.length)throw new Error(`Invalid host ${t} with no separate hostname and port!`);let r=parseInt(t.substring(e+1),10);return t[0]==="["?[t.substring(1,e-1),r]:[t.substring(0,e),r]},cc=()=>{var n;return(n=oc())===null||n===void 0?void 0:n.config},lc=n=>{var t;return(t=oc())===null||t===void 0?void 0:t[`_${n}`]};var fn=class{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((t,e)=>{this.resolve=t,this.reject=e})}wrapCallback(t){return(e,r)=>{e?this.reject(e):this.resolve(r),typeof t=="function"&&(this.promise.catch(()=>{}),t.length===1?t(e):t(e,r))}}};function qe(n){return n.endsWith(".cloudworkstations.dev")}function Qn(n){return h(this,null,function*(){return(yield fetch(n,{credentials:"include"})).ok})}function Zh(n,t){if(n.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');let e={alg:"none",type:"JWT"},r=t||"demo-project",i=n.iat||0,s=n.sub||n.user_id;if(!s)throw new Error("mockUserToken must contain 'sub' or 'user_id' field!");let o=Object.assign({iss:`https://securetoken.google.com/${r}`,aud:r,iat:i,exp:i+3600,auth_time:i,sub:s,user_id:s,firebase:{sign_in_provider:"custom",identities:{}}},n);return[Qr(JSON.stringify(e)),Qr(JSON.stringify(o)),""].join(".")}var Jr={};function Vv(){let n={prod:[],emulator:[]};for(let t of Object.keys(Jr))Jr[t]?n.emulator.push(t):n.prod.push(t);return n}function $v(n){let t=document.getElementById(n),e=!1;return t||(t=document.createElement("div"),t.setAttribute("id",n),e=!0),{created:e,element:t}}var qh=!1;function er(n,t){if(typeof window>"u"||typeof document>"u"||!qe(window.location.host)||Jr[n]===t||Jr[n]||qh)return;Jr[n]=t;function e(f){return`__firebase__banner__${f}`}let r="__firebase__banner",s=Vv().prod.length>0;function o(){let f=document.getElementById(r);f&&f.remove()}function a(f){f.style.display="flex",f.style.background="#7faaf0",f.style.position="fixed",f.style.bottom="5px",f.style.left="5px",f.style.padding=".5em",f.style.borderRadius="5px",f.style.alignItems="center"}function c(f,m){f.setAttribute("width","24"),f.setAttribute("id",m),f.setAttribute("height","24"),f.setAttribute("viewBox","0 0 24 24"),f.setAttribute("fill","none"),f.style.marginLeft="-6px"}function l(){let f=document.createElement("span");return f.style.cursor="pointer",f.style.marginLeft="16px",f.style.fontSize="24px",f.innerHTML=" &times;",f.onclick=()=>{qh=!0,o()},f}function u(f,m){f.setAttribute("id",m),f.innerText="Learn more",f.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",f.setAttribute("target","__blank"),f.style.paddingLeft="5px",f.style.textDecoration="underline"}function p(){let f=$v(r),m=e("text"),v=document.getElementById(m)||document.createElement("span"),C=e("learnmore"),R=document.getElementById(C)||document.createElement("a"),X=e("preprendIcon"),H=document.getElementById(X)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(f.created){let U=f.element;a(U),u(R,C);let Z=l();c(H,X),U.append(H,v,R,Z),document.body.appendChild(U)}s?(v.innerText="Preview backend disconnected.",H.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(H.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,v.innerText="Preview backend running in this workspace."),v.setAttribute("id",m)}document.readyState==="loading"?window.addEventListener("DOMContentLoaded",p):p()}function ce(){return typeof navigator<"u"&&typeof navigator.userAgent=="string"?navigator.userAgent:""}function Jh(){return typeof window<"u"&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(ce())}function Qh(){return typeof navigator<"u"&&navigator.userAgent==="Cloudflare-Workers"}function ef(){let n=typeof chrome=="object"?chrome.runtime:typeof browser=="object"?browser.runtime:void 0;return typeof n=="object"&&n.id!==void 0}function tf(){return typeof navigator=="object"&&navigator.product==="ReactNative"}function nf(){let n=ce();return n.indexOf("MSIE ")>=0||n.indexOf("Trident/")>=0}function Ps(){try{return typeof indexedDB=="object"}catch{return!1}}function rf(){return new Promise((n,t)=>{try{let e=!0,r="validate-browser-context-for-indexeddb-analytics-module",i=self.indexedDB.open(r);i.onsuccess=()=>{i.result.close(),e||self.indexedDB.deleteDatabase(r),n(!0)},i.onupgradeneeded=()=>{e=!1},i.onerror=()=>{var s;t(((s=i.error)===null||s===void 0?void 0:s.message)||"")}}catch(e){t(e)}})}var zv="FirebaseError",fe=class n extends Error{constructor(t,e,r){super(e),this.code=t,this.customData=r,this.name=zv,Object.setPrototypeOf(this,n.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,xe.prototype.create)}},xe=class{constructor(t,e,r){this.service=t,this.serviceName=e,this.errors=r}create(t,...e){let r=e[0]||{},i=`${this.service}/${t}`,s=this.errors[t],o=s?Hv(s,r):"Error",a=`${this.serviceName}: ${o} (${i}).`;return new fe(i,a,r)}};function Hv(n,t){return n.replace(Wv,(e,r)=>{let i=t[r];return i!=null?String(i):`<${r}?>`})}var Wv=/\{\$([^}]+)}/g;function sf(n){for(let t in n)if(Object.prototype.hasOwnProperty.call(n,t))return!1;return!0}function Ot(n,t){if(n===t)return!0;let e=Object.keys(n),r=Object.keys(t);for(let i of e){if(!r.includes(i))return!1;let s=n[i],o=t[i];if(Kh(s)&&Kh(o)){if(!Ot(s,o))return!1}else if(s!==o)return!1}for(let i of r)if(!e.includes(i))return!1;return!0}function Kh(n){return n!==null&&typeof n=="object"}function ei(n){let t=[];for(let[e,r]of Object.entries(n))Array.isArray(r)?r.forEach(i=>{t.push(encodeURIComponent(e)+"="+encodeURIComponent(i))}):t.push(encodeURIComponent(e)+"="+encodeURIComponent(r));return t.length?"&"+t.join("&"):""}function of(n,t){let e=new sc(n,t);return e.subscribe.bind(e)}var sc=class{constructor(t,e){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=e,this.task.then(()=>{t(this)}).catch(r=>{this.error(r)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(t=>{t.complete()}),this.close()}subscribe(t,e,r){let i;if(t===void 0&&e===void 0&&r===void 0)throw new Error("Missing Observer.");Gv(t,["next","error","complete"])?i=t:i={next:t,error:e,complete:r},i.next===void 0&&(i.next=rc),i.error===void 0&&(i.error=rc),i.complete===void 0&&(i.complete=rc);let s=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?i.error(this.finalError):i.complete()}catch{}}),this.observers.push(i),s}unsubscribeOne(t){this.observers===void 0||this.observers[t]===void 0||(delete this.observers[t],this.observerCount-=1,this.observerCount===0&&this.onNoObservers!==void 0&&this.onNoObservers(this))}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(t,e){this.task.then(()=>{if(this.observers!==void 0&&this.observers[t]!==void 0)try{e(this.observers[t])}catch(r){typeof console<"u"&&console.error&&console.error(r)}})}close(t){this.finalized||(this.finalized=!0,t!==void 0&&(this.finalError=t),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}};function Gv(n,t){if(typeof n!="object"||n===null)return!1;for(let e of t)if(e in n&&typeof n[e]=="function")return!0;return!1}function rc(){}var $P=4*60*60*1e3;function ne(n){return n&&n._delegate?n._delegate:n}var re=class{constructor(t,e,r){this.name=t,this.instanceFactory=e,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(t){return this.instantiationMode=t,this}setMultipleInstances(t){return this.multipleInstances=t,this}setServiceProps(t){return this.serviceProps=t,this}setInstanceCreatedCallback(t){return this.onInstanceCreated=t,this}};var pn="[DEFAULT]";var uc=class{constructor(t,e){this.name=t,this.container=e,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(t){let e=this.normalizeInstanceIdentifier(t);if(!this.instancesDeferred.has(e)){let r=new fn;if(this.instancesDeferred.set(e,r),this.isInitialized(e)||this.shouldAutoInitialize())try{let i=this.getOrInitializeService({instanceIdentifier:e});i&&r.resolve(i)}catch{}}return this.instancesDeferred.get(e).promise}getImmediate(t){var e;let r=this.normalizeInstanceIdentifier(t?.identifier),i=(e=t?.optional)!==null&&e!==void 0?e:!1;if(this.isInitialized(r)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:r})}catch(s){if(i)return null;throw s}else{if(i)return null;throw Error(`Service ${this.name} is not available`)}}getComponent(){return this.component}setComponent(t){if(t.name!==this.name)throw Error(`Mismatching Component ${t.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=t,!!this.shouldAutoInitialize()){if(Kv(t))try{this.getOrInitializeService({instanceIdentifier:pn})}catch{}for(let[e,r]of this.instancesDeferred.entries()){let i=this.normalizeInstanceIdentifier(e);try{let s=this.getOrInitializeService({instanceIdentifier:i});r.resolve(s)}catch{}}}}clearInstance(t=pn){this.instancesDeferred.delete(t),this.instancesOptions.delete(t),this.instances.delete(t)}delete(){return h(this,null,function*(){let t=Array.from(this.instances.values());yield Promise.all([...t.filter(e=>"INTERNAL"in e).map(e=>e.INTERNAL.delete()),...t.filter(e=>"_delete"in e).map(e=>e._delete())])})}isComponentSet(){return this.component!=null}isInitialized(t=pn){return this.instances.has(t)}getOptions(t=pn){return this.instancesOptions.get(t)||{}}initialize(t={}){let{options:e={}}=t,r=this.normalizeInstanceIdentifier(t.instanceIdentifier);if(this.isInitialized(r))throw Error(`${this.name}(${r}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let i=this.getOrInitializeService({instanceIdentifier:r,options:e});for(let[s,o]of this.instancesDeferred.entries()){let a=this.normalizeInstanceIdentifier(s);r===a&&o.resolve(i)}return i}onInit(t,e){var r;let i=this.normalizeInstanceIdentifier(e),s=(r=this.onInitCallbacks.get(i))!==null&&r!==void 0?r:new Set;s.add(t),this.onInitCallbacks.set(i,s);let o=this.instances.get(i);return o&&t(o,i),()=>{s.delete(t)}}invokeOnInitCallbacks(t,e){let r=this.onInitCallbacks.get(e);if(r)for(let i of r)try{i(t,e)}catch{}}getOrInitializeService({instanceIdentifier:t,options:e={}}){let r=this.instances.get(t);if(!r&&this.component&&(r=this.component.instanceFactory(this.container,{instanceIdentifier:qv(t),options:e}),this.instances.set(t,r),this.instancesOptions.set(t,e),this.invokeOnInitCallbacks(r,t),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,t,r)}catch{}return r||null}normalizeInstanceIdentifier(t=pn){return this.component?this.component.multipleInstances?t:pn:t}shouldAutoInitialize(){return!!this.component&&this.component.instantiationMode!=="EXPLICIT"}};function qv(n){return n===pn?void 0:n}function Kv(n){return n.instantiationMode==="EAGER"}var Ms=class{constructor(t){this.name=t,this.providers=new Map}addComponent(t){let e=this.getProvider(t.name);if(e.isComponentSet())throw new Error(`Component ${t.name} has already been registered with ${this.name}`);e.setComponent(t)}addOrOverwriteComponent(t){this.getProvider(t.name).isComponentSet()&&this.providers.delete(t.name),this.addComponent(t)}getProvider(t){if(this.providers.has(t))return this.providers.get(t);let e=new uc(t,this);return this.providers.set(t,e),e}getProviders(){return Array.from(this.providers.values())}};var Yv=[],F=function(n){return n[n.DEBUG=0]="DEBUG",n[n.VERBOSE=1]="VERBOSE",n[n.INFO=2]="INFO",n[n.WARN=3]="WARN",n[n.ERROR=4]="ERROR",n[n.SILENT=5]="SILENT",n}(F||{}),Xv={debug:F.DEBUG,verbose:F.VERBOSE,info:F.INFO,warn:F.WARN,error:F.ERROR,silent:F.SILENT},Zv=F.INFO,Jv={[F.DEBUG]:"log",[F.VERBOSE]:"log",[F.INFO]:"info",[F.WARN]:"warn",[F.ERROR]:"error"},Qv=(n,t,...e)=>{if(t<n.logLevel)return;let r=new Date().toISOString(),i=Jv[t];if(i)console[i](`[${r}]  ${n.name}:`,...e);else throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`)},Pt=class{constructor(t){this.name=t,this._logLevel=Zv,this._logHandler=Qv,this._userLogHandler=null,Yv.push(this)}get logLevel(){return this._logLevel}set logLevel(t){if(!(t in F))throw new TypeError(`Invalid value "${t}" assigned to \`logLevel\``);this._logLevel=t}setLogLevel(t){this._logLevel=typeof t=="string"?Xv[t]:t}get logHandler(){return this._logHandler}set logHandler(t){if(typeof t!="function")throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=t}get userLogHandler(){return this._userLogHandler}set userLogHandler(t){this._userLogHandler=t}debug(...t){this._userLogHandler&&this._userLogHandler(this,F.DEBUG,...t),this._logHandler(this,F.DEBUG,...t)}log(...t){this._userLogHandler&&this._userLogHandler(this,F.VERBOSE,...t),this._logHandler(this,F.VERBOSE,...t)}info(...t){this._userLogHandler&&this._userLogHandler(this,F.INFO,...t),this._logHandler(this,F.INFO,...t)}warn(...t){this._userLogHandler&&this._userLogHandler(this,F.WARN,...t),this._logHandler(this,F.WARN,...t)}error(...t){this._userLogHandler&&this._userLogHandler(this,F.ERROR,...t),this._logHandler(this,F.ERROR,...t)}};var eb=(n,t)=>t.some(e=>n instanceof e),af,cf;function tb(){return af||(af=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function nb(){return cf||(cf=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}var lf=new WeakMap,hc=new WeakMap,uf=new WeakMap,dc=new WeakMap,pc=new WeakMap;function rb(n){let t=new Promise((e,r)=>{let i=()=>{n.removeEventListener("success",s),n.removeEventListener("error",o)},s=()=>{e(Ke(n.result)),i()},o=()=>{r(n.error),i()};n.addEventListener("success",s),n.addEventListener("error",o)});return t.then(e=>{e instanceof IDBCursor&&lf.set(e,n)}).catch(()=>{}),pc.set(t,n),t}function ib(n){if(hc.has(n))return;let t=new Promise((e,r)=>{let i=()=>{n.removeEventListener("complete",s),n.removeEventListener("error",o),n.removeEventListener("abort",o)},s=()=>{e(),i()},o=()=>{r(n.error||new DOMException("AbortError","AbortError")),i()};n.addEventListener("complete",s),n.addEventListener("error",o),n.addEventListener("abort",o)});hc.set(n,t)}var fc={get(n,t,e){if(n instanceof IDBTransaction){if(t==="done")return hc.get(n);if(t==="objectStoreNames")return n.objectStoreNames||uf.get(n);if(t==="store")return e.objectStoreNames[1]?void 0:e.objectStore(e.objectStoreNames[0])}return Ke(n[t])},set(n,t,e){return n[t]=e,!0},has(n,t){return n instanceof IDBTransaction&&(t==="done"||t==="store")?!0:t in n}};function df(n){fc=n(fc)}function sb(n){return n===IDBDatabase.prototype.transaction&&!("objectStoreNames"in IDBTransaction.prototype)?function(t,...e){let r=n.call(Ns(this),t,...e);return uf.set(r,t.sort?t.sort():[t]),Ke(r)}:nb().includes(n)?function(...t){return n.apply(Ns(this),t),Ke(lf.get(this))}:function(...t){return Ke(n.apply(Ns(this),t))}}function ob(n){return typeof n=="function"?sb(n):(n instanceof IDBTransaction&&ib(n),eb(n,tb())?new Proxy(n,fc):n)}function Ke(n){if(n instanceof IDBRequest)return rb(n);if(dc.has(n))return dc.get(n);let t=ob(n);return t!==n&&(dc.set(n,t),pc.set(t,n)),t}var Ns=n=>pc.get(n);function ff(n,t,{blocked:e,upgrade:r,blocking:i,terminated:s}={}){let o=indexedDB.open(n,t),a=Ke(o);return r&&o.addEventListener("upgradeneeded",c=>{r(Ke(o.result),c.oldVersion,c.newVersion,Ke(o.transaction),c)}),e&&o.addEventListener("blocked",c=>e(c.oldVersion,c.newVersion,c)),a.then(c=>{s&&c.addEventListener("close",()=>s()),i&&c.addEventListener("versionchange",l=>i(l.oldVersion,l.newVersion,l))}).catch(()=>{}),a}var ab=["get","getKey","getAll","getAllKeys","count"],cb=["put","add","delete","clear"],mc=new Map;function hf(n,t){if(!(n instanceof IDBDatabase&&!(t in n)&&typeof t=="string"))return;if(mc.get(t))return mc.get(t);let e=t.replace(/FromIndex$/,""),r=t!==e,i=cb.includes(e);if(!(e in(r?IDBIndex:IDBObjectStore).prototype)||!(i||ab.includes(e)))return;let s=function(o,...a){return h(this,null,function*(){let c=this.transaction(o,i?"readwrite":"readonly"),l=c.store;return r&&(l=l.index(a.shift())),(yield Promise.all([l[e](...a),i&&c.done]))[0]})};return mc.set(t,s),s}df(n=>J(g({},n),{get:(t,e,r)=>hf(t,e)||n.get(t,e,r),has:(t,e)=>!!hf(t,e)||n.has(t,e)}));var _c=class{constructor(t){this.container=t}getPlatformInfoString(){return this.container.getProviders().map(e=>{if(lb(e)){let r=e.getImmediate();return`${r.library}/${r.version}`}else return null}).filter(e=>e).join(" ")}};function lb(n){let t=n.getComponent();return t?.type==="VERSION"}var vc="@firebase/app",pf="0.13.1";var mt=new Pt("@firebase/app"),ub="@firebase/app-compat",db="@firebase/analytics-compat",hb="@firebase/analytics",fb="@firebase/app-check-compat",pb="@firebase/app-check",mb="@firebase/auth",gb="@firebase/auth-compat",_b="@firebase/database",vb="@firebase/data-connect",bb="@firebase/database-compat",yb="@firebase/functions",Eb="@firebase/functions-compat",wb="@firebase/installations",Ib="@firebase/installations-compat",Db="@firebase/messaging",Cb="@firebase/messaging-compat",Sb="@firebase/performance",Tb="@firebase/performance-compat",Rb="@firebase/remote-config",Ab="@firebase/remote-config-compat",kb="@firebase/storage",Ob="@firebase/storage-compat",Pb="@firebase/firestore",Mb="@firebase/ai",Nb="@firebase/firestore-compat",xb="firebase",Fb="11.9.0";var bc="[DEFAULT]",Lb={[vc]:"fire-core",[ub]:"fire-core-compat",[hb]:"fire-analytics",[db]:"fire-analytics-compat",[pb]:"fire-app-check",[fb]:"fire-app-check-compat",[mb]:"fire-auth",[gb]:"fire-auth-compat",[_b]:"fire-rtdb",[vb]:"fire-data-connect",[bb]:"fire-rtdb-compat",[yb]:"fire-fn",[Eb]:"fire-fn-compat",[wb]:"fire-iid",[Ib]:"fire-iid-compat",[Db]:"fire-fcm",[Cb]:"fire-fcm-compat",[Sb]:"fire-perf",[Tb]:"fire-perf-compat",[Rb]:"fire-rc",[Ab]:"fire-rc-compat",[kb]:"fire-gcs",[Ob]:"fire-gcs-compat",[Pb]:"fire-fst",[Nb]:"fire-fst-compat",[Mb]:"fire-vertex","fire-js":"fire-js",[xb]:"fire-js-all"};var ti=new Map,Ub=new Map,yc=new Map;function mf(n,t){try{n.container.addComponent(t)}catch(e){mt.debug(`Component ${t.name} failed to register with FirebaseApp ${n.name}`,e)}}function Ce(n){let t=n.name;if(yc.has(t))return mt.debug(`There were multiple attempts to register component ${t}.`),!1;yc.set(t,n);for(let e of ti.values())mf(e,n);for(let e of Ub.values())mf(e,n);return!0}function Nt(n,t){let e=n.container.getProvider("heartbeat").getImmediate({optional:!0});return e&&e.triggerHeartbeat(),n.container.getProvider(t)}function pe(n){return n==null?!1:n.settings!==void 0}var Bb={"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."},Mt=new xe("app","Firebase",Bb);var Ec=class{constructor(t,e,r){this._isDeleted=!1,this._options=Object.assign({},t),this._config=Object.assign({},e),this._name=e.name,this._automaticDataCollectionEnabled=e.automaticDataCollectionEnabled,this._container=r,this.container.addComponent(new re("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(t){this.checkDestroyed(),this._automaticDataCollectionEnabled=t}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(t){this._isDeleted=t}checkDestroyed(){if(this.isDeleted)throw Mt.create("app-deleted",{appName:this._name})}};var xt=Fb;function Dc(n,t={}){let e=n;typeof t!="object"&&(t={name:t});let r=Object.assign({name:bc,automaticDataCollectionEnabled:!0},t),i=r.name;if(typeof i!="string"||!i)throw Mt.create("bad-app-name",{appName:String(i)});if(e||(e=cc()),!e)throw Mt.create("no-options");let s=ti.get(i);if(s){if(Ot(e,s.options)&&Ot(r,s.config))return s;throw Mt.create("duplicate-app",{appName:i})}let o=new Ms(i);for(let c of yc.values())o.addComponent(c);let a=new Ec(e,r,o);return ti.set(i,a),a}function gt(n=bc){let t=ti.get(n);if(!t&&n===bc&&cc())return Dc();if(!t)throw Mt.create("no-app",{appName:n});return t}function xs(){return Array.from(ti.values())}function L(n,t,e){var r;let i=(r=Lb[n])!==null&&r!==void 0?r:n;e&&(i+=`-${e}`);let s=i.match(/\s|\//),o=t.match(/\s|\//);if(s||o){let a=[`Unable to register library "${i}" with version "${t}":`];s&&a.push(`library name "${i}" contains illegal characters (whitespace or "/")`),s&&o&&a.push("and"),o&&a.push(`version name "${t}" contains illegal characters (whitespace or "/")`),mt.warn(a.join(" "));return}Ce(new re(`${i}-version`,()=>({library:i,version:t}),"VERSION"))}var jb="firebase-heartbeat-database",Vb=1,ni="firebase-heartbeat-store",gc=null;function bf(){return gc||(gc=ff(jb,Vb,{upgrade:(n,t)=>{switch(t){case 0:try{n.createObjectStore(ni)}catch(e){console.warn(e)}}}}).catch(n=>{throw Mt.create("idb-open",{originalErrorMessage:n.message})})),gc}function $b(n){return h(this,null,function*(){try{let e=(yield bf()).transaction(ni),r=yield e.objectStore(ni).get(yf(n));return yield e.done,r}catch(t){if(t instanceof fe)mt.warn(t.message);else{let e=Mt.create("idb-get",{originalErrorMessage:t?.message});mt.warn(e.message)}}})}function gf(n,t){return h(this,null,function*(){try{let r=(yield bf()).transaction(ni,"readwrite");yield r.objectStore(ni).put(t,yf(n)),yield r.done}catch(e){if(e instanceof fe)mt.warn(e.message);else{let r=Mt.create("idb-set",{originalErrorMessage:e?.message});mt.warn(r.message)}}})}function yf(n){return`${n.name}!${n.options.appId}`}var zb=1024,Hb=30,wc=class{constructor(t){this.container=t,this._heartbeatsCache=null;let e=this.container.getProvider("app").getImmediate();this._storage=new Ic(e),this._heartbeatsCachePromise=this._storage.read().then(r=>(this._heartbeatsCache=r,r))}triggerHeartbeat(){return h(this,null,function*(){var t,e;try{let i=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),s=_f();if(((t=this._heartbeatsCache)===null||t===void 0?void 0:t.heartbeats)==null&&(this._heartbeatsCache=yield this._heartbeatsCachePromise,((e=this._heartbeatsCache)===null||e===void 0?void 0:e.heartbeats)==null)||this._heartbeatsCache.lastSentHeartbeatDate===s||this._heartbeatsCache.heartbeats.some(o=>o.date===s))return;if(this._heartbeatsCache.heartbeats.push({date:s,agent:i}),this._heartbeatsCache.heartbeats.length>Hb){let o=Gb(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(o,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(r){mt.warn(r)}})}getHeartbeatsHeader(){return h(this,null,function*(){var t;try{if(this._heartbeatsCache===null&&(yield this._heartbeatsCachePromise),((t=this._heartbeatsCache)===null||t===void 0?void 0:t.heartbeats)==null||this._heartbeatsCache.heartbeats.length===0)return"";let e=_f(),{heartbeatsToSend:r,unsentEntries:i}=Wb(this._heartbeatsCache.heartbeats),s=Qr(JSON.stringify({version:2,heartbeats:r}));return this._heartbeatsCache.lastSentHeartbeatDate=e,i.length>0?(this._heartbeatsCache.heartbeats=i,yield this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),s}catch(e){return mt.warn(e),""}})}};function _f(){return new Date().toISOString().substring(0,10)}function Wb(n,t=zb){let e=[],r=n.slice();for(let i of n){let s=e.find(o=>o.agent===i.agent);if(s){if(s.dates.push(i.date),vf(e)>t){s.dates.pop();break}}else if(e.push({agent:i.agent,dates:[i.date]}),vf(e)>t){e.pop();break}r=r.slice(1)}return{heartbeatsToSend:e,unsentEntries:r}}var Ic=class{constructor(t){this.app=t,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}runIndexedDBEnvironmentCheck(){return h(this,null,function*(){return Ps()?rf().then(()=>!0).catch(()=>!1):!1})}read(){return h(this,null,function*(){if(yield this._canUseIndexedDBPromise){let e=yield $b(this.app);return e?.heartbeats?e:{heartbeats:[]}}else return{heartbeats:[]}})}overwrite(t){return h(this,null,function*(){var e;if(yield this._canUseIndexedDBPromise){let i=yield this.read();return gf(this.app,{lastSentHeartbeatDate:(e=t.lastSentHeartbeatDate)!==null&&e!==void 0?e:i.lastSentHeartbeatDate,heartbeats:t.heartbeats})}else return})}add(t){return h(this,null,function*(){var e;if(yield this._canUseIndexedDBPromise){let i=yield this.read();return gf(this.app,{lastSentHeartbeatDate:(e=t.lastSentHeartbeatDate)!==null&&e!==void 0?e:i.lastSentHeartbeatDate,heartbeats:[...i.heartbeats,...t.heartbeats]})}else return})}};function vf(n){return Qr(JSON.stringify({version:2,heartbeats:n})).length}function Gb(n){if(n.length===0)return-1;let t=0,e=n[0].date;for(let r=1;r<n.length;r++)n[r].date<e&&(e=n[r].date,t=r);return t}function qb(n){Ce(new re("platform-logger",t=>new _c(t),"PRIVATE")),Ce(new re("heartbeat",t=>new wc(t),"PRIVATE")),L(vc,pf,n),L(vc,pf,"esm2017"),L("fire-js","")}qb("");var Kb="firebase",Yb="11.9.1";L(Kb,Yb,"app");function Ef(n){n===void 0&&(n=d(k));let t=n.get(dr);return e=>new Se(r=>{let i=t.add(),s=!1;function o(){s||(i(),s=!0)}let a=e.subscribe({next:c=>{r.next(c),o()},complete:()=>{r.complete(),o()},error:c=>{r.error(c),o()}});return a.add(()=>{r.unsubscribe(),o()}),a})}var Ye=new xo("ANGULARFIRE2_VERSION");function gn(n,t,e){if(t){if(t.length===1)return t[0];let s=t.filter(o=>o.app===e);if(s.length===1)return s[0]}return e.container.getProvider(n).getImmediate({optional:!0})}var Ft=(n,t)=>{let e=t?[t]:xs(),r=[];return e.forEach(i=>{i.container.getProvider(n).instances.forEach(o=>{r.includes(o)||r.push(o)})}),r},tr=function(n){return n[n.SILENT=0]="SILENT",n[n.WARN=1]="WARN",n[n.VERBOSE=2]="VERBOSE",n}(tr||{}),wf=Fo()&&typeof Zone<"u"?tr.WARN:tr.SILENT;var Fs=class{zone;delegate;constructor(t,e=Fl){this.zone=t,this.delegate=e}now(){return this.delegate.now()}schedule(t,e,r){let i=this.zone,s=function(o){i?i.runGuarded(()=>{t.apply(this,[o])}):t.apply(this,[o])};return this.delegate.schedule(s,e,r)}},Xe=(()=>{class n{outsideAngular;insideAngular;constructor(){let e=d(D);this.outsideAngular=e.runOutsideAngular(()=>new Fs(typeof Zone>"u"?void 0:Zone.current)),this.insideAngular=e.run(()=>new Fs(typeof Zone>"u"?void 0:Zone.current,xl))}static \u0275fac=function(r){return new(r||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})(),If=!1;function Xb(n,t){!If&&(wf>tr.SILENT||Fo())&&(If=!0,console.warn("Calling Firebase APIs outside of an Injection context may destabilize your application leading to subtle change-detection and hydration bugs. Find more at https://github.com/angular/angularfire/blob/main/docs/zones.md")),wf>=t&&console.warn(`Firebase API called outside injection context: ${n.name}`)}function Zb(n){let t=d(D,{optional:!0});return t?t.runOutsideAngular(()=>n()):n()}function mn(n){let t=d(D,{optional:!0});return t?t.run(()=>n()):n()}var Jb=(n,t,e)=>(...r)=>(t&&setTimeout(t,0),ge(e,()=>mn(()=>n.apply(void 0,r)))),_e=(n,t,e)=>(e||=t?tr.WARN:tr.VERBOSE,function(){let r,i=arguments,s,o,a;try{s=d(Xe),o=d(dr),a=d(le)}catch{return Xb(n,e),n.apply(this,i)}for(let l=0;l<arguments.length;l++)typeof i[l]=="function"&&(t&&(r||=mn(()=>o.add())),i[l]=Jb(i[l],r,a));let c=Zb(()=>n.apply(this,i));return t?c instanceof Se?c.pipe(_o(s.outsideAngular),go(s.insideAngular),Ef(a)):c instanceof Promise?mn(()=>{let l=o.add();return new Promise((u,p)=>{c.then(f=>ge(a,()=>mn(()=>u(f))),f=>ge(a,()=>mn(()=>p(f)))).finally(l)})}):typeof c=="function"&&r?function(){return setTimeout(r,0),c.apply(this,arguments)}:mn(()=>c):c instanceof Se?c.pipe(_o(s.outsideAngular),go(s.insideAngular)):mn(()=>c)});var Fe=class{constructor(t){return t}},_t=class{constructor(){return xs()}};function Qb(n){return n&&n.length===1?n[0]:new Fe(gt())}var Cc=new y("angularfire2._apps"),ey={provide:Fe,useFactory:Qb,deps:[[new q,Cc]]},ty={provide:_t,deps:[[new q,Cc]]};function ny(n){return(t,e)=>{let r=e.get(nt);L("angularfire",Ye.full,"core"),L("angularfire",Ye.full,"app"),L("angular",gu.full,r.toString());let i=t.runOutsideAngular(()=>n(e));return new Fe(i)}}function LM(n,...t){return Te([ey,ty,{provide:Cc,useFactory:ny(n),multi:!0,deps:[D,k,Xe,...t]}])}var UM=_e(Dc,!0);var ry=new Map,iy={activated:!1,tokenObservers:[]},sy={initialized:!1,enabled:!1};function Le(n){return ry.get(n)||Object.assign({},iy)}function Tf(){return sy}var oy="https://content-firebaseappcheck.googleapis.com/v1";var ay="exchangeDebugToken",Df={OFFSET_DURATION:5*60*1e3,RETRIAL_MIN_WAIT:30*1e3,RETRIAL_MAX_WAIT:16*60*1e3},qM=24*60*60*1e3;var Rc=class{constructor(t,e,r,i,s){if(this.operation=t,this.retryPolicy=e,this.getWaitDuration=r,this.lowerBound=i,this.upperBound=s,this.pending=null,this.nextErrorWaitInterval=i,i>s)throw new Error("Proactive refresh lower bound greater than upper bound!")}start(){this.nextErrorWaitInterval=this.lowerBound,this.process(!0).catch(()=>{})}stop(){this.pending&&(this.pending.reject("cancelled"),this.pending=null)}isRunning(){return!!this.pending}process(t){return h(this,null,function*(){this.stop();try{this.pending=new fn,this.pending.promise.catch(e=>{}),yield cy(this.getNextRun(t)),this.pending.resolve(),yield this.pending.promise,this.pending=new fn,this.pending.promise.catch(e=>{}),yield this.operation(),this.pending.resolve(),yield this.pending.promise,this.process(!0).catch(()=>{})}catch(e){this.retryPolicy(e)?this.process(!1).catch(()=>{}):this.stop()}})}getNextRun(t){if(t)return this.nextErrorWaitInterval=this.lowerBound,this.getWaitDuration();{let e=this.nextErrorWaitInterval;return this.nextErrorWaitInterval*=2,this.nextErrorWaitInterval>this.upperBound&&(this.nextErrorWaitInterval=this.upperBound),e}}};function cy(n){return new Promise(t=>{setTimeout(t,n)})}var ly={"already-initialized":"You have already called initializeAppCheck() for FirebaseApp {$appName} with different options. To avoid this error, call initializeAppCheck() with the same options as when it was originally called. This will return the already initialized instance.","use-before-activation":"App Check is being used before initializeAppCheck() is called for FirebaseApp {$appName}. Call initializeAppCheck() before instantiating other Firebase services.","fetch-network-error":"Fetch failed to connect to a network. Check Internet connection. Original error: {$originalErrorMessage}.","fetch-parse-error":"Fetch client could not parse response. Original error: {$originalErrorMessage}.","fetch-status-error":"Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.","storage-open":"Error thrown when opening storage. Original error: {$originalErrorMessage}.","storage-get":"Error thrown when reading from storage. Original error: {$originalErrorMessage}.","storage-set":"Error thrown when writing to storage. Original error: {$originalErrorMessage}.","recaptcha-error":"ReCAPTCHA error.","initial-throttle":"{$httpStatus} error. Attempts allowed again after {$time}",throttled:"Requests throttled due to previous {$httpStatus} error. Attempts allowed again after {$time}"},Lt=new xe("appCheck","AppCheck",ly);function Rf(n){if(!Le(n).activated)throw Lt.create("use-before-activation",{appName:n.name})}function Af(r,i){return h(this,arguments,function*({url:n,body:t},e){let s={"Content-Type":"application/json"},o=e.getImmediate({optional:!0});if(o){let m=yield o.getHeartbeatsHeader();m&&(s["X-Firebase-Client"]=m)}let a={method:"POST",body:JSON.stringify(t),headers:s},c;try{c=yield fetch(n,a)}catch(m){throw Lt.create("fetch-network-error",{originalErrorMessage:m?.message})}if(c.status!==200)throw Lt.create("fetch-status-error",{httpStatus:c.status});let l;try{l=yield c.json()}catch(m){throw Lt.create("fetch-parse-error",{originalErrorMessage:m?.message})}let u=l.ttl.match(/^([\d.]+)(s)$/);if(!u||!u[2]||isNaN(Number(u[1])))throw Lt.create("fetch-parse-error",{originalErrorMessage:`ttl field (timeToLive) is not in standard Protobuf Duration format: ${l.ttl}`});let p=Number(u[1])*1e3,f=Date.now();return{token:l.token,expireTimeMillis:f+p,issuedAtTimeMillis:f}})}function kf(n,t){let{projectId:e,appId:r,apiKey:i}=n.options;return{url:`${oy}/projects/${e}/apps/${r}:${ay}?key=${i}`,body:{debug_token:t}}}var uy="firebase-app-check-database",dy=1,Ac="firebase-app-check-store";var Ls=null;function hy(){return Ls||(Ls=new Promise((n,t)=>{try{let e=indexedDB.open(uy,dy);e.onsuccess=r=>{n(r.target.result)},e.onerror=r=>{var i;t(Lt.create("storage-open",{originalErrorMessage:(i=r.target.error)===null||i===void 0?void 0:i.message}))},e.onupgradeneeded=r=>{let i=r.target.result;switch(r.oldVersion){case 0:i.createObjectStore(Ac,{keyPath:"compositeKey"})}}}catch(e){t(Lt.create("storage-open",{originalErrorMessage:e?.message}))}}),Ls)}function fy(n,t){return py(my(n),t)}function py(n,t){return h(this,null,function*(){let r=(yield hy()).transaction(Ac,"readwrite"),s=r.objectStore(Ac).put({compositeKey:n,value:t});return new Promise((o,a)=>{s.onsuccess=c=>{o()},r.onerror=c=>{var l;a(Lt.create("storage-set",{originalErrorMessage:(l=c.target.error)===null||l===void 0?void 0:l.message}))}})})}function my(n){return`${n.options.appId}-${n.name}`}var ri=new Pt("@firebase/app-check");function Sc(n,t){return Ps()?fy(n,t).catch(e=>{ri.warn(`Failed to write token to IndexedDB. Error: ${e}`)}):Promise.resolve()}function Of(){return Tf().enabled}function Pf(){return h(this,null,function*(){let n=Tf();if(n.enabled&&n.token)return n.token.promise;throw Error(`
            Can't get debug token in production mode.
        `)})}var gy={error:"UNKNOWN_ERROR"};function _y(n){return As.encodeString(JSON.stringify(n),!1)}function kc(n,t=!1,e=!1){return h(this,null,function*(){let r=n.app;Rf(r);let i=Le(r),s=i.token,o;if(s&&!ii(s)&&(i.token=void 0,s=void 0),!s){let l=yield i.cachedTokenPromise;l&&(ii(l)?s=l:yield Sc(r,void 0))}if(!t&&s&&ii(s))return{token:s.token};let a=!1;if(Of())try{i.exchangeTokenPromise||(i.exchangeTokenPromise=Af(kf(r,yield Pf()),n.heartbeatServiceProvider).finally(()=>{i.exchangeTokenPromise=void 0}),a=!0);let l=yield i.exchangeTokenPromise;return yield Sc(r,l),i.token=l,{token:l.token}}catch(l){return l.code==="appCheck/throttled"||l.code==="appCheck/initial-throttle"?ri.warn(l.message):e&&ri.error(l),Tc(l)}try{i.exchangeTokenPromise||(i.exchangeTokenPromise=i.provider.getToken().finally(()=>{i.exchangeTokenPromise=void 0}),a=!0),s=yield Le(r).exchangeTokenPromise}catch(l){l.code==="appCheck/throttled"||l.code==="appCheck/initial-throttle"?ri.warn(l.message):e&&ri.error(l),o=l}let c;return s?o?ii(s)?c={token:s.token,internalError:o}:c=Tc(o):(c={token:s.token},i.token=s,yield Sc(r,s)):c=Tc(o),a&&Ey(r,c),c})}function vy(n){return h(this,null,function*(){let t=n.app;Rf(t);let{provider:e}=Le(t);if(Of()){let r=yield Pf(),{token:i}=yield Af(kf(t,r),n.heartbeatServiceProvider);return{token:i}}else{let{token:r}=yield e.getToken();return{token:r}}})}function by(n,t,e,r){let{app:i}=n,s=Le(i),o={next:e,error:r,type:t};if(s.tokenObservers=[...s.tokenObservers,o],s.token&&ii(s.token)){let a=s.token;Promise.resolve().then(()=>{e({token:a.token}),Cf(n)}).catch(()=>{})}s.cachedTokenPromise.then(()=>Cf(n))}function Mf(n,t){let e=Le(n),r=e.tokenObservers.filter(i=>i.next!==t);r.length===0&&e.tokenRefresher&&e.tokenRefresher.isRunning()&&e.tokenRefresher.stop(),e.tokenObservers=r}function Cf(n){let{app:t}=n,e=Le(t),r=e.tokenRefresher;r||(r=yy(n),e.tokenRefresher=r),!r.isRunning()&&e.isTokenAutoRefreshEnabled&&r.start()}function yy(n){let{app:t}=n;return new Rc(()=>h(null,null,function*(){let e=Le(t),r;if(e.token?r=yield kc(n,!0):r=yield kc(n),r.error)throw r.error;if(r.internalError)throw r.internalError}),()=>!0,()=>{let e=Le(t);if(e.token){let r=e.token.issuedAtTimeMillis+(e.token.expireTimeMillis-e.token.issuedAtTimeMillis)*.5+3e5,i=e.token.expireTimeMillis-5*60*1e3;return r=Math.min(r,i),Math.max(0,r-Date.now())}else return 0},Df.RETRIAL_MIN_WAIT,Df.RETRIAL_MAX_WAIT)}function Ey(n,t){let e=Le(n).tokenObservers;for(let r of e)try{r.type==="EXTERNAL"&&t.error!=null?r.error(t.error):r.next(t)}catch{}}function ii(n){return n.expireTimeMillis-Date.now()>0}function Tc(n){return{token:_y(gy),error:n}}var Oc=class{constructor(t,e){this.app=t,this.heartbeatServiceProvider=e}_delete(){let{tokenObservers:t}=Le(this.app);for(let e of t)Mf(this.app,e.next);return Promise.resolve()}};function wy(n,t){return new Oc(n,t)}function Iy(n){return{getToken:t=>kc(n,t),getLimitedUseToken:()=>vy(n),addTokenListener:t=>by(n,"INTERNAL",t),removeTokenListener:t=>Mf(n.app,t)}}var Dy="@firebase/app-check",Cy="0.10.0";var Sy="app-check",Sf="app-check-internal";function Ty(){Ce(new re(Sy,n=>{let t=n.getProvider("app").getImmediate(),e=n.getProvider("heartbeat");return wy(t,e)},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((n,t,e)=>{n.getProvider(Sf).initialize()})),Ce(new re(Sf,n=>{let t=n.getProvider("app-check").getImmediate();return Iy(t)},"PUBLIC").setInstantiationMode("EXPLICIT")),L(Dy,Cy)}Ty();var Ry="app-check";var Ut=class{constructor(){return Ft(Ry)}};var Ay=["localhost","0.0.0.0","127.0.0.1"],oN=typeof window<"u"&&Ay.includes(window.location.hostname);function Kf(){return{"dependent-sdk-initialized-before-auth":"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK."}}var Yf=Kf,Xf=new xe("auth","Firebase",Kf());var zs=new Pt("@firebase/auth");function ky(n,...t){zs.logLevel<=F.WARN&&zs.warn(`Auth (${xt}): ${n}`,...t)}function Bs(n,...t){zs.logLevel<=F.ERROR&&zs.error(`Auth (${xt}): ${n}`,...t)}function $t(n,...t){throw Zc(n,...t)}function _n(n,...t){return Zc(n,...t)}function Zf(n,t,e){let r=Object.assign(Object.assign({},Yf()),{[t]:e});return new xe("auth","Firebase",r).create(t,{appName:n.name})}function Vt(n){return Zf(n,"operation-not-supported-in-this-environment","Operations that alter the current user are not supported in conjunction with FirebaseServerApp")}function Zc(n,...t){if(typeof n!="string"){let e=t[0],r=[...t.slice(1)];return r[0]&&(r[0].appName=n.name),n._errorFactory.create(e,...r)}return Xf.create(n,...t)}function I(n,t,...e){if(!n)throw Zc(t,...e)}function vt(n){let t="INTERNAL ASSERTION FAILED: "+n;throw Bs(t),new Error(t)}function zt(n,t){n||vt(t)}function xc(){var n;return typeof self<"u"&&((n=self.location)===null||n===void 0?void 0:n.href)||""}function Oy(){return Nf()==="http:"||Nf()==="https:"}function Nf(){var n;return typeof self<"u"&&((n=self.location)===null||n===void 0?void 0:n.protocol)||null}function Py(){return typeof navigator<"u"&&navigator&&"onLine"in navigator&&typeof navigator.onLine=="boolean"&&(Oy()||ef()||"connection"in navigator)?navigator.onLine:!0}function My(){if(typeof navigator>"u")return null;let n=navigator;return n.languages&&n.languages[0]||n.language||null}var vn=class{constructor(t,e){this.shortDelay=t,this.longDelay=e,zt(e>t,"Short delay should be less than long delay!"),this.isMobile=Jh()||tf()}get(){return Py()?this.isMobile?this.longDelay:this.shortDelay:Math.min(5e3,this.shortDelay)}};function Jc(n,t){zt(n.emulator,"Emulator should always be set here");let{url:e}=n.emulator;return t?`${e}${t.startsWith("/")?t.slice(1):t}`:e}var Hs=class{static initialize(t,e,r){this.fetchImpl=t,e&&(this.headersImpl=e),r&&(this.responseImpl=r)}static fetch(){if(this.fetchImpl)return this.fetchImpl;if(typeof self<"u"&&"fetch"in self)return self.fetch;if(typeof globalThis<"u"&&globalThis.fetch)return globalThis.fetch;if(typeof fetch<"u")return fetch;vt("Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")}static headers(){if(this.headersImpl)return this.headersImpl;if(typeof self<"u"&&"Headers"in self)return self.Headers;if(typeof globalThis<"u"&&globalThis.Headers)return globalThis.Headers;if(typeof Headers<"u")return Headers;vt("Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")}static response(){if(this.responseImpl)return this.responseImpl;if(typeof self<"u"&&"Response"in self)return self.Response;if(typeof globalThis<"u"&&globalThis.Response)return globalThis.Response;if(typeof Response<"u")return Response;vt("Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill")}};var Ny={CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_PASSWORD:"wrong-password",MISSING_PASSWORD:"missing-password",INVALID_LOGIN_CREDENTIALS:"invalid-credential",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_REQ_TYPE:"internal-error",EMAIL_NOT_FOUND:"user-not-found",RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",INVALID_CODE:"invalid-verification-code",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",MISSING_SESSION_INFO:"missing-verification-id",SESSION_EXPIRED:"code-expired",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",BLOCKING_FUNCTION_ERROR_RESPONSE:"internal-error",RECAPTCHA_NOT_ENABLED:"recaptcha-not-enabled",MISSING_RECAPTCHA_TOKEN:"missing-recaptcha-token",INVALID_RECAPTCHA_TOKEN:"invalid-recaptcha-token",INVALID_RECAPTCHA_ACTION:"invalid-recaptcha-action",MISSING_CLIENT_TYPE:"missing-client-type",MISSING_RECAPTCHA_VERSION:"missing-recaptcha-version",INVALID_RECAPTCHA_VERSION:"invalid-recaptcha-version",INVALID_REQ_TYPE:"invalid-req-type"};var xy=["/v1/accounts:signInWithCustomToken","/v1/accounts:signInWithEmailLink","/v1/accounts:signInWithIdp","/v1/accounts:signInWithPassword","/v1/accounts:signInWithPhoneNumber","/v1/token"],Fy=new vn(3e4,6e4);function to(n,t){return n.tenantId&&!t.tenantId?Object.assign(Object.assign({},t),{tenantId:n.tenantId}):t}function nr(s,o,a,c){return h(this,arguments,function*(n,t,e,r,i={}){return Jf(n,i,()=>h(null,null,function*(){let l={},u={};r&&(t==="GET"?u=r:l={body:JSON.stringify(r)});let p=ei(Object.assign({key:n.config.apiKey},u)).slice(1),f=yield n._getAdditionalHeaders();f["Content-Type"]="application/json",n.languageCode&&(f["X-Firebase-Locale"]=n.languageCode);let m=Object.assign({method:t,headers:f},l);return Qh()||(m.referrerPolicy="no-referrer"),n.emulatorConfig&&qe(n.emulatorConfig.host)&&(m.credentials="include"),Hs.fetch()(yield ep(n,n.config.apiHost,e,p),m)}))})}function Jf(n,t,e){return h(this,null,function*(){n._canInitEmulator=!1;let r=Object.assign(Object.assign({},Ny),t);try{let i=new Fc(n),s=yield Promise.race([e(),i.promise]);i.clearNetworkTimeout();let o=yield s.json();if("needConfirmation"in o)throw Us(n,"account-exists-with-different-credential",o);if(s.ok&&!("errorMessage"in o))return o;{let a=s.ok?o.errorMessage:o.error.message,[c,l]=a.split(" : ");if(c==="FEDERATED_USER_ID_ALREADY_LINKED")throw Us(n,"credential-already-in-use",o);if(c==="EMAIL_EXISTS")throw Us(n,"email-already-in-use",o);if(c==="USER_DISABLED")throw Us(n,"user-disabled",o);let u=r[c]||c.toLowerCase().replace(/[_\s]+/g,"-");if(l)throw Zf(n,u,l);$t(n,u)}}catch(i){if(i instanceof fe)throw i;$t(n,"network-request-failed",{message:String(i)})}})}function Qf(s,o,a,c){return h(this,arguments,function*(n,t,e,r,i={}){let l=yield nr(n,t,e,r,i);return"mfaPendingCredential"in l&&$t(n,"multi-factor-auth-required",{_serverResponse:l}),l})}function ep(n,t,e,r){return h(this,null,function*(){let i=`${t}${e}?${r}`,s=n,o=s.config.emulator?Jc(n.config,i):`${n.config.apiScheme}://${i}`;return xy.includes(e)&&(yield s._persistenceManagerAvailable,s._getPersistenceType()==="COOKIE")?s._getPersistence()._getFinalTarget(o).toString():o})}var Fc=class{clearNetworkTimeout(){clearTimeout(this.timer)}constructor(t){this.auth=t,this.timer=null,this.promise=new Promise((e,r)=>{this.timer=setTimeout(()=>r(_n(this.auth,"network-request-failed")),Fy.get())})}};function Us(n,t,e){let r={appName:n.name};e.email&&(r.email=e.email),e.phoneNumber&&(r.phoneNumber=e.phoneNumber);let i=_n(n,t,r);return i.customData._tokenResponse=e,i}function Ly(n,t){return h(this,null,function*(){return nr(n,"POST","/v1/accounts:delete",t)})}function Ws(n,t){return h(this,null,function*(){return nr(n,"POST","/v1/accounts:lookup",t)})}function si(n){if(n)try{let t=new Date(Number(n));if(!isNaN(t.getTime()))return t.toUTCString()}catch{}}function Qc(n,t=!1){return h(this,null,function*(){let e=ne(n),r=yield e.getIdToken(t),i=el(r);I(i&&i.exp&&i.auth_time&&i.iat,e.auth,"internal-error");let s=typeof i.firebase=="object"?i.firebase:void 0,o=s?.sign_in_provider;return{claims:i,token:r,authTime:si(Pc(i.auth_time)),issuedAtTime:si(Pc(i.iat)),expirationTime:si(Pc(i.exp)),signInProvider:o||null,signInSecondFactor:s?.sign_in_second_factor||null}})}function Pc(n){return Number(n)*1e3}function el(n){let[t,e,r]=n.split(".");if(t===void 0||e===void 0||r===void 0)return Bs("JWT malformed, contained fewer than 3 sections"),null;try{let i=ks(e);return i?JSON.parse(i):(Bs("Failed to decode base64 JWT payload"),null)}catch(i){return Bs("Caught error parsing JWT payload as JSON",i?.toString()),null}}function xf(n){let t=el(n);return I(t,"internal-error"),I(typeof t.exp<"u","internal-error"),I(typeof t.iat<"u","internal-error"),Number(t.exp)-Number(t.iat)}function ai(n,t,e=!1){return h(this,null,function*(){if(e)return t;try{return yield t}catch(r){throw r instanceof fe&&Uy(r)&&n.auth.currentUser===n&&(yield n.auth.signOut()),r}})}function Uy({code:n}){return n==="auth/user-disabled"||n==="auth/user-token-expired"}var Lc=class{constructor(t){this.user=t,this.isRunning=!1,this.timerId=null,this.errorBackoff=3e4}_start(){this.isRunning||(this.isRunning=!0,this.schedule())}_stop(){this.isRunning&&(this.isRunning=!1,this.timerId!==null&&clearTimeout(this.timerId))}getInterval(t){var e;if(t){let r=this.errorBackoff;return this.errorBackoff=Math.min(this.errorBackoff*2,96e4),r}else{this.errorBackoff=3e4;let i=((e=this.user.stsTokenManager.expirationTime)!==null&&e!==void 0?e:0)-Date.now()-3e5;return Math.max(0,i)}}schedule(t=!1){if(!this.isRunning)return;let e=this.getInterval(t);this.timerId=setTimeout(()=>h(this,null,function*(){yield this.iteration()}),e)}iteration(){return h(this,null,function*(){try{yield this.user.getIdToken(!0)}catch(t){t?.code==="auth/network-request-failed"&&this.schedule(!0);return}this.schedule()})}};var ci=class{constructor(t,e){this.createdAt=t,this.lastLoginAt=e,this._initializeTime()}_initializeTime(){this.lastSignInTime=si(this.lastLoginAt),this.creationTime=si(this.createdAt)}_copy(t){this.createdAt=t.createdAt,this.lastLoginAt=t.lastLoginAt,this._initializeTime()}toJSON(){return{createdAt:this.createdAt,lastLoginAt:this.lastLoginAt}}};function Gs(n){return h(this,null,function*(){var t;let e=n.auth,r=yield n.getIdToken(),i=yield ai(n,Ws(e,{idToken:r}));I(i?.users.length,e,"internal-error");let s=i.users[0];n._notifyReloadListener(s);let o=!((t=s.providerUserInfo)===null||t===void 0)&&t.length?tp(s.providerUserInfo):[],a=By(n.providerData,o),c=n.isAnonymous,l=!(n.email&&s.passwordHash)&&!a?.length,u=c?l:!1,p={uid:s.localId,displayName:s.displayName||null,photoURL:s.photoUrl||null,email:s.email||null,emailVerified:s.emailVerified||!1,phoneNumber:s.phoneNumber||null,tenantId:s.tenantId||null,providerData:a,metadata:new ci(s.createdAt,s.lastLoginAt),isAnonymous:u};Object.assign(n,p)})}function tl(n){return h(this,null,function*(){let t=ne(n);yield Gs(t),yield t.auth._persistUserIfCurrent(t),t.auth._notifyListenersIfCurrent(t)})}function By(n,t){return[...n.filter(r=>!t.some(i=>i.providerId===r.providerId)),...t]}function tp(n){return n.map(t=>{var{providerId:e}=t,r=mo(t,["providerId"]);return{providerId:e,uid:r.rawId||"",displayName:r.displayName||null,email:r.email||null,phoneNumber:r.phoneNumber||null,photoURL:r.photoUrl||null}})}function jy(n,t){return h(this,null,function*(){let e=yield Jf(n,{},()=>h(null,null,function*(){let r=ei({grant_type:"refresh_token",refresh_token:t}).slice(1),{tokenApiHost:i,apiKey:s}=n.config,o=yield ep(n,i,"/v1/token",`key=${s}`),a=yield n._getAdditionalHeaders();a["Content-Type"]="application/x-www-form-urlencoded";let c={method:"POST",headers:a,body:r};return n.emulatorConfig&&qe(n.emulatorConfig.host)&&(c.credentials="include"),Hs.fetch()(o,c)}));return{accessToken:e.access_token,expiresIn:e.expires_in,refreshToken:e.refresh_token}})}function Vy(n,t){return h(this,null,function*(){return nr(n,"POST","/v2/accounts:revokeToken",to(n,t))})}var oi=class n{constructor(){this.refreshToken=null,this.accessToken=null,this.expirationTime=null}get isExpired(){return!this.expirationTime||Date.now()>this.expirationTime-3e4}updateFromServerResponse(t){I(t.idToken,"internal-error"),I(typeof t.idToken<"u","internal-error"),I(typeof t.refreshToken<"u","internal-error");let e="expiresIn"in t&&typeof t.expiresIn<"u"?Number(t.expiresIn):xf(t.idToken);this.updateTokensAndExpiration(t.idToken,t.refreshToken,e)}updateFromIdToken(t){I(t.length!==0,"internal-error");let e=xf(t);this.updateTokensAndExpiration(t,null,e)}getToken(t,e=!1){return h(this,null,function*(){return!e&&this.accessToken&&!this.isExpired?this.accessToken:(I(this.refreshToken,t,"user-token-expired"),this.refreshToken?(yield this.refresh(t,this.refreshToken),this.accessToken):null)})}clearRefreshToken(){this.refreshToken=null}refresh(t,e){return h(this,null,function*(){let{accessToken:r,refreshToken:i,expiresIn:s}=yield jy(t,e);this.updateTokensAndExpiration(r,i,Number(s))})}updateTokensAndExpiration(t,e,r){this.refreshToken=e||null,this.accessToken=t||null,this.expirationTime=Date.now()+r*1e3}static fromJSON(t,e){let{refreshToken:r,accessToken:i,expirationTime:s}=e,o=new n;return r&&(I(typeof r=="string","internal-error",{appName:t}),o.refreshToken=r),i&&(I(typeof i=="string","internal-error",{appName:t}),o.accessToken=i),s&&(I(typeof s=="number","internal-error",{appName:t}),o.expirationTime=s),o}toJSON(){return{refreshToken:this.refreshToken,accessToken:this.accessToken,expirationTime:this.expirationTime}}_assign(t){this.accessToken=t.accessToken,this.refreshToken=t.refreshToken,this.expirationTime=t.expirationTime}_clone(){return Object.assign(new n,this.toJSON())}_performRefresh(){return vt("not implemented")}};function Bt(n,t){I(typeof n=="string"||typeof n>"u","internal-error",{appName:t})}var jt=class n{constructor(t){var{uid:e,auth:r,stsTokenManager:i}=t,s=mo(t,["uid","auth","stsTokenManager"]);this.providerId="firebase",this.proactiveRefresh=new Lc(this),this.reloadUserInfo=null,this.reloadListener=null,this.uid=e,this.auth=r,this.stsTokenManager=i,this.accessToken=i.accessToken,this.displayName=s.displayName||null,this.email=s.email||null,this.emailVerified=s.emailVerified||!1,this.phoneNumber=s.phoneNumber||null,this.photoURL=s.photoURL||null,this.isAnonymous=s.isAnonymous||!1,this.tenantId=s.tenantId||null,this.providerData=s.providerData?[...s.providerData]:[],this.metadata=new ci(s.createdAt||void 0,s.lastLoginAt||void 0)}getIdToken(t){return h(this,null,function*(){let e=yield ai(this,this.stsTokenManager.getToken(this.auth,t));return I(e,this.auth,"internal-error"),this.accessToken!==e&&(this.accessToken=e,yield this.auth._persistUserIfCurrent(this),this.auth._notifyListenersIfCurrent(this)),e})}getIdTokenResult(t){return Qc(this,t)}reload(){return tl(this)}_assign(t){this!==t&&(I(this.uid===t.uid,this.auth,"internal-error"),this.displayName=t.displayName,this.photoURL=t.photoURL,this.email=t.email,this.emailVerified=t.emailVerified,this.phoneNumber=t.phoneNumber,this.isAnonymous=t.isAnonymous,this.tenantId=t.tenantId,this.providerData=t.providerData.map(e=>Object.assign({},e)),this.metadata._copy(t.metadata),this.stsTokenManager._assign(t.stsTokenManager))}_clone(t){let e=new n(Object.assign(Object.assign({},this),{auth:t,stsTokenManager:this.stsTokenManager._clone()}));return e.metadata._copy(this.metadata),e}_onReload(t){I(!this.reloadListener,this.auth,"internal-error"),this.reloadListener=t,this.reloadUserInfo&&(this._notifyReloadListener(this.reloadUserInfo),this.reloadUserInfo=null)}_notifyReloadListener(t){this.reloadListener?this.reloadListener(t):this.reloadUserInfo=t}_startProactiveRefresh(){this.proactiveRefresh._start()}_stopProactiveRefresh(){this.proactiveRefresh._stop()}_updateTokensIfNecessary(t,e=!1){return h(this,null,function*(){let r=!1;t.idToken&&t.idToken!==this.stsTokenManager.accessToken&&(this.stsTokenManager.updateFromServerResponse(t),r=!0),e&&(yield Gs(this)),yield this.auth._persistUserIfCurrent(this),r&&this.auth._notifyListenersIfCurrent(this)})}delete(){return h(this,null,function*(){if(pe(this.auth.app))return Promise.reject(Vt(this.auth));let t=yield this.getIdToken();return yield ai(this,Ly(this.auth,{idToken:t})),this.stsTokenManager.clearRefreshToken(),this.auth.signOut()})}toJSON(){return Object.assign(Object.assign({uid:this.uid,email:this.email||void 0,emailVerified:this.emailVerified,displayName:this.displayName||void 0,isAnonymous:this.isAnonymous,photoURL:this.photoURL||void 0,phoneNumber:this.phoneNumber||void 0,tenantId:this.tenantId||void 0,providerData:this.providerData.map(t=>Object.assign({},t)),stsTokenManager:this.stsTokenManager.toJSON(),_redirectEventId:this._redirectEventId},this.metadata.toJSON()),{apiKey:this.auth.config.apiKey,appName:this.auth.name})}get refreshToken(){return this.stsTokenManager.refreshToken||""}static _fromJSON(t,e){var r,i,s,o,a,c,l,u;let p=(r=e.displayName)!==null&&r!==void 0?r:void 0,f=(i=e.email)!==null&&i!==void 0?i:void 0,m=(s=e.phoneNumber)!==null&&s!==void 0?s:void 0,v=(o=e.photoURL)!==null&&o!==void 0?o:void 0,C=(a=e.tenantId)!==null&&a!==void 0?a:void 0,R=(c=e._redirectEventId)!==null&&c!==void 0?c:void 0,X=(l=e.createdAt)!==null&&l!==void 0?l:void 0,H=(u=e.lastLoginAt)!==null&&u!==void 0?u:void 0,{uid:U,emailVerified:Z,isAnonymous:Ht,providerData:Wt,stsTokenManager:pi}=e;I(U&&pi,t,"internal-error");let ar=oi.fromJSON(this.name,pi);I(typeof U=="string",t,"internal-error"),Bt(p,t.name),Bt(f,t.name),I(typeof Z=="boolean",t,"internal-error"),I(typeof Ht=="boolean",t,"internal-error"),Bt(m,t.name),Bt(v,t.name),Bt(C,t.name),Bt(R,t.name),Bt(X,t.name),Bt(H,t.name);let ho=new n({uid:U,auth:t,email:f,emailVerified:Z,displayName:p,isAnonymous:Ht,photoURL:v,phoneNumber:m,tenantId:C,stsTokenManager:ar,createdAt:X,lastLoginAt:H});return Wt&&Array.isArray(Wt)&&(ho.providerData=Wt.map(dm=>Object.assign({},dm))),R&&(ho._redirectEventId=R),ho}static _fromIdTokenResponse(t,e,r=!1){return h(this,null,function*(){let i=new oi;i.updateFromServerResponse(e);let s=new n({uid:e.localId,auth:t,stsTokenManager:i,isAnonymous:r});return yield Gs(s),s})}static _fromGetAccountInfoResponse(t,e,r){return h(this,null,function*(){let i=e.users[0];I(i.localId!==void 0,"internal-error");let s=i.providerUserInfo!==void 0?tp(i.providerUserInfo):[],o=!(i.email&&i.passwordHash)&&!s?.length,a=new oi;a.updateFromIdToken(r);let c=new n({uid:i.localId,auth:t,stsTokenManager:a,isAnonymous:o}),l={uid:i.localId,displayName:i.displayName||null,photoURL:i.photoUrl||null,email:i.email||null,emailVerified:i.emailVerified||!1,phoneNumber:i.phoneNumber||null,tenantId:i.tenantId||null,providerData:s,metadata:new ci(i.createdAt,i.lastLoginAt),isAnonymous:!(i.email&&i.passwordHash)&&!s?.length};return Object.assign(c,l),c})}};var Ff=new Map;function bt(n){zt(n instanceof Function,"Expected a class definition");let t=Ff.get(n);return t?(zt(t instanceof n,"Instance stored in cache mismatched with class"),t):(t=new n,Ff.set(n,t),t)}var $y=(()=>{class n{constructor(){this.type="NONE",this.storage={}}_isAvailable(){return h(this,null,function*(){return!0})}_set(e,r){return h(this,null,function*(){this.storage[e]=r})}_get(e){return h(this,null,function*(){let r=this.storage[e];return r===void 0?null:r})}_remove(e){return h(this,null,function*(){delete this.storage[e]})}_addListener(e,r){}_removeListener(e,r){}}return n.type="NONE",n})(),Uc=$y;function js(n,t,e){return`firebase:${n}:${t}:${e}`}var qs=class n{constructor(t,e,r){this.persistence=t,this.auth=e,this.userKey=r;let{config:i,name:s}=this.auth;this.fullUserKey=js(this.userKey,i.apiKey,s),this.fullPersistenceKey=js("persistence",i.apiKey,s),this.boundEventHandler=e._onStorageEvent.bind(e),this.persistence._addListener(this.fullUserKey,this.boundEventHandler)}setCurrentUser(t){return this.persistence._set(this.fullUserKey,t.toJSON())}getCurrentUser(){return h(this,null,function*(){let t=yield this.persistence._get(this.fullUserKey);if(!t)return null;if(typeof t=="string"){let e=yield Ws(this.auth,{idToken:t}).catch(()=>{});return e?jt._fromGetAccountInfoResponse(this.auth,e,t):null}return jt._fromJSON(this.auth,t)})}removeCurrentUser(){return this.persistence._remove(this.fullUserKey)}savePersistenceForRedirect(){return this.persistence._set(this.fullPersistenceKey,this.persistence.type)}setPersistence(t){return h(this,null,function*(){if(this.persistence===t)return;let e=yield this.getCurrentUser();if(yield this.removeCurrentUser(),this.persistence=t,e)return this.setCurrentUser(e)})}delete(){this.persistence._removeListener(this.fullUserKey,this.boundEventHandler)}static create(t,e,r="authUser"){return h(this,null,function*(){if(!e.length)return new n(bt(Uc),t,r);let i=(yield Promise.all(e.map(l=>h(null,null,function*(){if(yield l._isAvailable())return l})))).filter(l=>l),s=i[0]||bt(Uc),o=js(r,t.config.apiKey,t.name),a=null;for(let l of e)try{let u=yield l._get(o);if(u){let p;if(typeof u=="string"){let f=yield Ws(t,{idToken:u}).catch(()=>{});if(!f)break;p=yield jt._fromGetAccountInfoResponse(t,f,u)}else p=jt._fromJSON(t,u);l!==s&&(a=p),s=l;break}}catch{}let c=i.filter(l=>l._shouldAllowMigration);return!s._shouldAllowMigration||!c.length?new n(s,t,r):(s=c[0],a&&(yield s._set(o,a.toJSON())),yield Promise.all(e.map(l=>h(null,null,function*(){if(l!==s)try{yield l._remove(o)}catch{}}))),new n(s,t,r))})}};function Lf(n){let t=n.toLowerCase();if(t.includes("opera/")||t.includes("opr/")||t.includes("opios/"))return"Opera";if(sp(t))return"IEMobile";if(t.includes("msie")||t.includes("trident/"))return"IE";if(t.includes("edge/"))return"Edge";if(np(t))return"Firefox";if(t.includes("silk/"))return"Silk";if(ap(t))return"Blackberry";if(cp(t))return"Webos";if(rp(t))return"Safari";if((t.includes("chrome/")||ip(t))&&!t.includes("edge/"))return"Chrome";if(op(t))return"Android";{let e=/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/,r=n.match(e);if(r?.length===2)return r[1]}return"Other"}function np(n=ce()){return/firefox\//i.test(n)}function rp(n=ce()){let t=n.toLowerCase();return t.includes("safari/")&&!t.includes("chrome/")&&!t.includes("crios/")&&!t.includes("android")}function ip(n=ce()){return/crios\//i.test(n)}function sp(n=ce()){return/iemobile/i.test(n)}function op(n=ce()){return/android/i.test(n)}function ap(n=ce()){return/blackberry/i.test(n)}function cp(n=ce()){return/webos/i.test(n)}function nl(n=ce()){return/iphone|ipad|ipod/i.test(n)||/macintosh/i.test(n)&&/mobile/i.test(n)}function zy(n=ce()){var t;return nl(n)&&!!(!((t=window.navigator)===null||t===void 0)&&t.standalone)}function Hy(){return nf()&&document.documentMode===10}function lp(n=ce()){return nl(n)||op(n)||cp(n)||ap(n)||/windows phone/i.test(n)||sp(n)}function up(n,t=[]){let e;switch(n){case"Browser":e=Lf(ce());break;case"Worker":e=`${Lf(ce())}-${n}`;break;default:e=n}let r=t.length?t.join(","):"FirebaseCore-web";return`${e}/JsCore/${xt}/${r}`}var Bc=class{constructor(t){this.auth=t,this.queue=[]}pushCallback(t,e){let r=s=>new Promise((o,a)=>{try{let c=t(s);o(c)}catch(c){a(c)}});r.onAbort=e,this.queue.push(r);let i=this.queue.length-1;return()=>{this.queue[i]=()=>Promise.resolve()}}runMiddleware(t){return h(this,null,function*(){if(this.auth.currentUser===t)return;let e=[];try{for(let r of this.queue)yield r(t),r.onAbort&&e.push(r.onAbort)}catch(r){e.reverse();for(let i of e)try{i()}catch{}throw this.auth._errorFactory.create("login-blocked",{originalMessage:r?.message})}})}};function Wy(e){return h(this,arguments,function*(n,t={}){return nr(n,"GET","/v2/passwordPolicy",to(n,t))})}var Gy=6,jc=class{constructor(t){var e,r,i,s;let o=t.customStrengthOptions;this.customStrengthOptions={},this.customStrengthOptions.minPasswordLength=(e=o.minPasswordLength)!==null&&e!==void 0?e:Gy,o.maxPasswordLength&&(this.customStrengthOptions.maxPasswordLength=o.maxPasswordLength),o.containsLowercaseCharacter!==void 0&&(this.customStrengthOptions.containsLowercaseLetter=o.containsLowercaseCharacter),o.containsUppercaseCharacter!==void 0&&(this.customStrengthOptions.containsUppercaseLetter=o.containsUppercaseCharacter),o.containsNumericCharacter!==void 0&&(this.customStrengthOptions.containsNumericCharacter=o.containsNumericCharacter),o.containsNonAlphanumericCharacter!==void 0&&(this.customStrengthOptions.containsNonAlphanumericCharacter=o.containsNonAlphanumericCharacter),this.enforcementState=t.enforcementState,this.enforcementState==="ENFORCEMENT_STATE_UNSPECIFIED"&&(this.enforcementState="OFF"),this.allowedNonAlphanumericCharacters=(i=(r=t.allowedNonAlphanumericCharacters)===null||r===void 0?void 0:r.join(""))!==null&&i!==void 0?i:"",this.forceUpgradeOnSignin=(s=t.forceUpgradeOnSignin)!==null&&s!==void 0?s:!1,this.schemaVersion=t.schemaVersion}validatePassword(t){var e,r,i,s,o,a;let c={isValid:!0,passwordPolicy:this};return this.validatePasswordLengthOptions(t,c),this.validatePasswordCharacterOptions(t,c),c.isValid&&(c.isValid=(e=c.meetsMinPasswordLength)!==null&&e!==void 0?e:!0),c.isValid&&(c.isValid=(r=c.meetsMaxPasswordLength)!==null&&r!==void 0?r:!0),c.isValid&&(c.isValid=(i=c.containsLowercaseLetter)!==null&&i!==void 0?i:!0),c.isValid&&(c.isValid=(s=c.containsUppercaseLetter)!==null&&s!==void 0?s:!0),c.isValid&&(c.isValid=(o=c.containsNumericCharacter)!==null&&o!==void 0?o:!0),c.isValid&&(c.isValid=(a=c.containsNonAlphanumericCharacter)!==null&&a!==void 0?a:!0),c}validatePasswordLengthOptions(t,e){let r=this.customStrengthOptions.minPasswordLength,i=this.customStrengthOptions.maxPasswordLength;r&&(e.meetsMinPasswordLength=t.length>=r),i&&(e.meetsMaxPasswordLength=t.length<=i)}validatePasswordCharacterOptions(t,e){this.updatePasswordCharacterOptionsStatuses(e,!1,!1,!1,!1);let r;for(let i=0;i<t.length;i++)r=t.charAt(i),this.updatePasswordCharacterOptionsStatuses(e,r>="a"&&r<="z",r>="A"&&r<="Z",r>="0"&&r<="9",this.allowedNonAlphanumericCharacters.includes(r))}updatePasswordCharacterOptionsStatuses(t,e,r,i,s){this.customStrengthOptions.containsLowercaseLetter&&(t.containsLowercaseLetter||(t.containsLowercaseLetter=e)),this.customStrengthOptions.containsUppercaseLetter&&(t.containsUppercaseLetter||(t.containsUppercaseLetter=r)),this.customStrengthOptions.containsNumericCharacter&&(t.containsNumericCharacter||(t.containsNumericCharacter=i)),this.customStrengthOptions.containsNonAlphanumericCharacter&&(t.containsNonAlphanumericCharacter||(t.containsNonAlphanumericCharacter=s))}};var Vc=class{constructor(t,e,r,i){this.app=t,this.heartbeatServiceProvider=e,this.appCheckServiceProvider=r,this.config=i,this.currentUser=null,this.emulatorConfig=null,this.operations=Promise.resolve(),this.authStateSubscription=new Ks(this),this.idTokenSubscription=new Ks(this),this.beforeStateQueue=new Bc(this),this.redirectUser=null,this.isProactiveRefreshEnabled=!1,this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION=1,this._canInitEmulator=!0,this._isInitialized=!1,this._deleted=!1,this._initializationPromise=null,this._popupRedirectResolver=null,this._errorFactory=Xf,this._agentRecaptchaConfig=null,this._tenantRecaptchaConfigs={},this._projectPasswordPolicy=null,this._tenantPasswordPolicies={},this._resolvePersistenceManagerAvailable=void 0,this.lastNotifiedUid=void 0,this.languageCode=null,this.tenantId=null,this.settings={appVerificationDisabledForTesting:!1},this.frameworks=[],this.name=t.name,this.clientVersion=i.sdkClientVersion,this._persistenceManagerAvailable=new Promise(s=>this._resolvePersistenceManagerAvailable=s)}_initializeWithPersistence(t,e){return e&&(this._popupRedirectResolver=bt(e)),this._initializationPromise=this.queue(()=>h(this,null,function*(){var r,i,s;if(!this._deleted&&(this.persistenceManager=yield qs.create(this,t),(r=this._resolvePersistenceManagerAvailable)===null||r===void 0||r.call(this),!this._deleted)){if(!((i=this._popupRedirectResolver)===null||i===void 0)&&i._shouldInitProactively)try{yield this._popupRedirectResolver._initialize(this)}catch{}yield this.initializeCurrentUser(e),this.lastNotifiedUid=((s=this.currentUser)===null||s===void 0?void 0:s.uid)||null,!this._deleted&&(this._isInitialized=!0)}})),this._initializationPromise}_onStorageEvent(){return h(this,null,function*(){if(this._deleted)return;let t=yield this.assertedPersistence.getCurrentUser();if(!(!this.currentUser&&!t)){if(this.currentUser&&t&&this.currentUser.uid===t.uid){this._currentUser._assign(t),yield this.currentUser.getIdToken();return}yield this._updateCurrentUser(t,!0)}})}initializeCurrentUserFromIdToken(t){return h(this,null,function*(){try{let e=yield Ws(this,{idToken:t}),r=yield jt._fromGetAccountInfoResponse(this,e,t);yield this.directlySetCurrentUser(r)}catch(e){console.warn("FirebaseServerApp could not login user with provided authIdToken: ",e),yield this.directlySetCurrentUser(null)}})}initializeCurrentUser(t){return h(this,null,function*(){var e;if(pe(this.app)){let o=this.app.settings.authIdToken;return o?new Promise(a=>{setTimeout(()=>this.initializeCurrentUserFromIdToken(o).then(a,a))}):this.directlySetCurrentUser(null)}let r=yield this.assertedPersistence.getCurrentUser(),i=r,s=!1;if(t&&this.config.authDomain){yield this.getOrInitRedirectPersistenceManager();let o=(e=this.redirectUser)===null||e===void 0?void 0:e._redirectEventId,a=i?._redirectEventId,c=yield this.tryRedirectSignIn(t);(!o||o===a)&&c?.user&&(i=c.user,s=!0)}if(!i)return this.directlySetCurrentUser(null);if(!i._redirectEventId){if(s)try{yield this.beforeStateQueue.runMiddleware(i)}catch(o){i=r,this._popupRedirectResolver._overrideRedirectResult(this,()=>Promise.reject(o))}return i?this.reloadAndSetCurrentUserOrClear(i):this.directlySetCurrentUser(null)}return I(this._popupRedirectResolver,this,"argument-error"),yield this.getOrInitRedirectPersistenceManager(),this.redirectUser&&this.redirectUser._redirectEventId===i._redirectEventId?this.directlySetCurrentUser(i):this.reloadAndSetCurrentUserOrClear(i)})}tryRedirectSignIn(t){return h(this,null,function*(){let e=null;try{e=yield this._popupRedirectResolver._completeRedirectFn(this,t,!0)}catch{yield this._setRedirectUser(null)}return e})}reloadAndSetCurrentUserOrClear(t){return h(this,null,function*(){try{yield Gs(t)}catch(e){if(e?.code!=="auth/network-request-failed")return this.directlySetCurrentUser(null)}return this.directlySetCurrentUser(t)})}useDeviceLanguage(){this.languageCode=My()}_delete(){return h(this,null,function*(){this._deleted=!0})}updateCurrentUser(t){return h(this,null,function*(){if(pe(this.app))return Promise.reject(Vt(this));let e=t?ne(t):null;return e&&I(e.auth.config.apiKey===this.config.apiKey,this,"invalid-user-token"),this._updateCurrentUser(e&&e._clone(this))})}_updateCurrentUser(t,e=!1){return h(this,null,function*(){if(!this._deleted)return t&&I(this.tenantId===t.tenantId,this,"tenant-id-mismatch"),e||(yield this.beforeStateQueue.runMiddleware(t)),this.queue(()=>h(this,null,function*(){yield this.directlySetCurrentUser(t),this.notifyAuthListeners()}))})}signOut(){return h(this,null,function*(){return pe(this.app)?Promise.reject(Vt(this)):(yield this.beforeStateQueue.runMiddleware(null),(this.redirectPersistenceManager||this._popupRedirectResolver)&&(yield this._setRedirectUser(null)),this._updateCurrentUser(null,!0))})}setPersistence(t){return pe(this.app)?Promise.reject(Vt(this)):this.queue(()=>h(this,null,function*(){yield this.assertedPersistence.setPersistence(bt(t))}))}_getRecaptchaConfig(){return this.tenantId==null?this._agentRecaptchaConfig:this._tenantRecaptchaConfigs[this.tenantId]}validatePassword(t){return h(this,null,function*(){this._getPasswordPolicyInternal()||(yield this._updatePasswordPolicy());let e=this._getPasswordPolicyInternal();return e.schemaVersion!==this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION?Promise.reject(this._errorFactory.create("unsupported-password-policy-schema-version",{})):e.validatePassword(t)})}_getPasswordPolicyInternal(){return this.tenantId===null?this._projectPasswordPolicy:this._tenantPasswordPolicies[this.tenantId]}_updatePasswordPolicy(){return h(this,null,function*(){let t=yield Wy(this),e=new jc(t);this.tenantId===null?this._projectPasswordPolicy=e:this._tenantPasswordPolicies[this.tenantId]=e})}_getPersistenceType(){return this.assertedPersistence.persistence.type}_getPersistence(){return this.assertedPersistence.persistence}_updateErrorMap(t){this._errorFactory=new xe("auth","Firebase",t())}onAuthStateChanged(t,e,r){return this.registerStateListener(this.authStateSubscription,t,e,r)}beforeAuthStateChanged(t,e){return this.beforeStateQueue.pushCallback(t,e)}onIdTokenChanged(t,e,r){return this.registerStateListener(this.idTokenSubscription,t,e,r)}authStateReady(){return new Promise((t,e)=>{if(this.currentUser)t();else{let r=this.onAuthStateChanged(()=>{r(),t()},e)}})}revokeAccessToken(t){return h(this,null,function*(){if(this.currentUser){let e=yield this.currentUser.getIdToken(),r={providerId:"apple.com",tokenType:"ACCESS_TOKEN",token:t,idToken:e};this.tenantId!=null&&(r.tenantId=this.tenantId),yield Vy(this,r)}})}toJSON(){var t;return{apiKey:this.config.apiKey,authDomain:this.config.authDomain,appName:this.name,currentUser:(t=this._currentUser)===null||t===void 0?void 0:t.toJSON()}}_setRedirectUser(t,e){return h(this,null,function*(){let r=yield this.getOrInitRedirectPersistenceManager(e);return t===null?r.removeCurrentUser():r.setCurrentUser(t)})}getOrInitRedirectPersistenceManager(t){return h(this,null,function*(){if(!this.redirectPersistenceManager){let e=t&&bt(t)||this._popupRedirectResolver;I(e,this,"argument-error"),this.redirectPersistenceManager=yield qs.create(this,[bt(e._redirectPersistence)],"redirectUser"),this.redirectUser=yield this.redirectPersistenceManager.getCurrentUser()}return this.redirectPersistenceManager})}_redirectUserForId(t){return h(this,null,function*(){var e,r;return this._isInitialized&&(yield this.queue(()=>h(this,null,function*(){}))),((e=this._currentUser)===null||e===void 0?void 0:e._redirectEventId)===t?this._currentUser:((r=this.redirectUser)===null||r===void 0?void 0:r._redirectEventId)===t?this.redirectUser:null})}_persistUserIfCurrent(t){return h(this,null,function*(){if(t===this.currentUser)return this.queue(()=>h(this,null,function*(){return this.directlySetCurrentUser(t)}))})}_notifyListenersIfCurrent(t){t===this.currentUser&&this.notifyAuthListeners()}_key(){return`${this.config.authDomain}:${this.config.apiKey}:${this.name}`}_startProactiveRefresh(){this.isProactiveRefreshEnabled=!0,this.currentUser&&this._currentUser._startProactiveRefresh()}_stopProactiveRefresh(){this.isProactiveRefreshEnabled=!1,this.currentUser&&this._currentUser._stopProactiveRefresh()}get _currentUser(){return this.currentUser}notifyAuthListeners(){var t,e;if(!this._isInitialized)return;this.idTokenSubscription.next(this.currentUser);let r=(e=(t=this.currentUser)===null||t===void 0?void 0:t.uid)!==null&&e!==void 0?e:null;this.lastNotifiedUid!==r&&(this.lastNotifiedUid=r,this.authStateSubscription.next(this.currentUser))}registerStateListener(t,e,r,i){if(this._deleted)return()=>{};let s=typeof e=="function"?e:e.next.bind(e),o=!1,a=this._isInitialized?Promise.resolve():this._initializationPromise;if(I(a,this,"internal-error"),a.then(()=>{o||s(this.currentUser)}),typeof e=="function"){let c=t.addObserver(e,r,i);return()=>{o=!0,c()}}else{let c=t.addObserver(e);return()=>{o=!0,c()}}}directlySetCurrentUser(t){return h(this,null,function*(){this.currentUser&&this.currentUser!==t&&this._currentUser._stopProactiveRefresh(),t&&this.isProactiveRefreshEnabled&&t._startProactiveRefresh(),this.currentUser=t,t?yield this.assertedPersistence.setCurrentUser(t):yield this.assertedPersistence.removeCurrentUser()})}queue(t){return this.operations=this.operations.then(t,t),this.operations}get assertedPersistence(){return I(this.persistenceManager,this,"internal-error"),this.persistenceManager}_logFramework(t){!t||this.frameworks.includes(t)||(this.frameworks.push(t),this.frameworks.sort(),this.clientVersion=up(this.config.clientPlatform,this._getFrameworks()))}_getFrameworks(){return this.frameworks}_getAdditionalHeaders(){return h(this,null,function*(){var t;let e={"X-Client-Version":this.clientVersion};this.app.options.appId&&(e["X-Firebase-gmpid"]=this.app.options.appId);let r=yield(t=this.heartbeatServiceProvider.getImmediate({optional:!0}))===null||t===void 0?void 0:t.getHeartbeatsHeader();r&&(e["X-Firebase-Client"]=r);let i=yield this._getAppCheckToken();return i&&(e["X-Firebase-AppCheck"]=i),e})}_getAppCheckToken(){return h(this,null,function*(){var t;if(pe(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let e=yield(t=this.appCheckServiceProvider.getImmediate({optional:!0}))===null||t===void 0?void 0:t.getToken();return e?.error&&ky(`Error while retrieving App Check token: ${e.error}`),e?.token})}};function no(n){return ne(n)}var Ks=class{constructor(t){this.auth=t,this.observer=null,this.addObserver=of(e=>this.observer=e)}get next(){return I(this.observer,this.auth,"internal-error"),this.observer.next.bind(this.observer)}};var rl={loadJS(){return h(this,null,function*(){throw new Error("Unable to load external scripts")})},recaptchaV2Script:"",recaptchaEnterpriseScript:"",gapiScript:""};function qy(n){rl=n}function Ky(n){return rl.loadJS(n)}function Yy(){return rl.gapiScript}function dp(n){return`__${n}${Math.floor(Math.random()*1e6)}`}function il(n,t){let e=Nt(n,"auth");if(e.isInitialized()){let i=e.getImmediate(),s=e.getOptions();if(Ot(s,t??{}))return i;$t(i,"already-initialized")}return e.initialize({options:t})}function Xy(n,t){let e=t?.persistence||[],r=(Array.isArray(e)?e:[e]).map(bt);t?.errorMap&&n._updateErrorMap(t.errorMap),n._initializeWithPersistence(r,t?.popupRedirectResolver)}function sl(n,t,e){let r=no(n);I(/^https?:\/\//.test(t),r,"invalid-emulator-scheme");let i=!!e?.disableWarnings,s=hp(t),{host:o,port:a}=Zy(t),c=a===null?"":`:${a}`,l={url:`${s}//${o}${c}/`},u=Object.freeze({host:o,port:a,protocol:s.replace(":",""),options:Object.freeze({disableWarnings:i})});if(!r._canInitEmulator){I(r.config.emulator&&r.emulatorConfig,r,"emulator-config-failed"),I(Ot(l,r.config.emulator)&&Ot(u,r.emulatorConfig),r,"emulator-config-failed");return}r.config.emulator=l,r.emulatorConfig=u,r.settings.appVerificationDisabledForTesting=!0,qe(o)?(Qn(`${s}//${o}${c}`),er("Auth",!0)):i||Jy()}function hp(n){let t=n.indexOf(":");return t<0?"":n.substr(0,t+1)}function Zy(n){let t=hp(n),e=/(\/\/)?([^?#/]+)/.exec(n.substr(t.length));if(!e)return{host:"",port:null};let r=e[2].split("@").pop()||"",i=/^(\[[^\]]+\])(:|$)/.exec(r);if(i){let s=i[1];return{host:s,port:Uf(r.substr(s.length+1))}}else{let[s,o]=r.split(":");return{host:s,port:Uf(o)}}}function Uf(n){if(!n)return null;let t=Number(n);return isNaN(t)?null:t}function Jy(){function n(){let t=document.createElement("p"),e=t.style;t.innerText="Running in emulator mode. Do not use with production credentials.",e.position="fixed",e.width="100%",e.backgroundColor="#ffffff",e.border=".1em solid #000000",e.color="#b50000",e.bottom="0px",e.left="0px",e.margin="0px",e.zIndex="10000",e.textAlign="center",t.classList.add("firebase-emulator-warning"),document.body.appendChild(t)}typeof console<"u"&&typeof console.info=="function"&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials."),typeof window<"u"&&typeof document<"u"&&(document.readyState==="loading"?window.addEventListener("DOMContentLoaded",n):n())}var Ys=class{constructor(t,e){this.providerId=t,this.signInMethod=e}toJSON(){return vt("not implemented")}_getIdTokenResponse(t){return vt("not implemented")}_linkToIdToken(t,e){return vt("not implemented")}_getReauthenticationResolver(t){return vt("not implemented")}};function Mc(n,t){return h(this,null,function*(){return Qf(n,"POST","/v1/accounts:signInWithIdp",to(n,t))})}var Xs=class{constructor(t){this.providerId=t,this.defaultLanguageCode=null,this.customParameters={}}setDefaultLanguage(t){this.defaultLanguageCode=t}setCustomParameters(t){return this.customParameters=t,this}getCustomParameters(){return this.customParameters}};var $c=class extends Xs{constructor(){super(...arguments),this.scopes=[]}addScope(t){return this.scopes.includes(t)||this.scopes.push(t),this}getScopes(){return[...this.scopes]}};function Qy(n,t){return h(this,null,function*(){return Qf(n,"POST","/v1/accounts:signUp",to(n,t))})}var bn=class n{constructor(t){this.user=t.user,this.providerId=t.providerId,this._tokenResponse=t._tokenResponse,this.operationType=t.operationType}static _fromIdTokenResponse(t,e,r,i=!1){return h(this,null,function*(){let s=yield jt._fromIdTokenResponse(t,r,i),o=Bf(r);return new n({user:s,providerId:o,_tokenResponse:r,operationType:e})})}static _forOperation(t,e,r){return h(this,null,function*(){yield t._updateTokensIfNecessary(r,!0);let i=Bf(r);return new n({user:t,providerId:i,_tokenResponse:r,operationType:e})})}};function Bf(n){return n.providerId?n.providerId:"phoneNumber"in n?"phone":null}function ol(n){return h(this,null,function*(){var t;if(pe(n.app))return Promise.reject(Vt(n));let e=no(n);if(yield e._initializationPromise,!((t=e.currentUser)===null||t===void 0)&&t.isAnonymous)return new bn({user:e.currentUser,providerId:null,operationType:"signIn"});let r=yield Qy(e,{returnSecureToken:!0}),i=yield bn._fromIdTokenResponse(e,"signIn",r,!0);return yield e._updateCurrentUser(i.user),i})}var zc=class n extends fe{constructor(t,e,r,i){var s;super(e.code,e.message),this.operationType=r,this.user=i,Object.setPrototypeOf(this,n.prototype),this.customData={appName:t.name,tenantId:(s=t.tenantId)!==null&&s!==void 0?s:void 0,_serverResponse:e.customData._serverResponse,operationType:r}}static _fromErrorAndOperation(t,e,r,i){return new n(t,e,r,i)}};function fp(n,t,e,r){return(t==="reauthenticate"?e._getReauthenticationResolver(n):e._getIdTokenResponse(n)).catch(s=>{throw s.code==="auth/multi-factor-auth-required"?zc._fromErrorAndOperation(n,s,t,r):s})}function eE(n,t,e=!1){return h(this,null,function*(){let r=yield ai(n,t._linkToIdToken(n.auth,yield n.getIdToken()),e);return bn._forOperation(n,"link",r)})}function tE(n,t,e=!1){return h(this,null,function*(){let{auth:r}=n;if(pe(r.app))return Promise.reject(Vt(r));let i="reauthenticate";try{let s=yield ai(n,fp(r,i,t,n),e);I(s.idToken,r,"internal-error");let o=el(s.idToken);I(o,r,"internal-error");let{sub:a}=o;return I(n.uid===a,r,"user-mismatch"),bn._forOperation(n,i,s)}catch(s){throw s?.code==="auth/user-not-found"&&$t(r,"user-mismatch"),s}})}function nE(n,t,e=!1){return h(this,null,function*(){if(pe(n.app))return Promise.reject(Vt(n));let r="signIn",i=yield fp(n,r,t),s=yield bn._fromIdTokenResponse(n,r,i);return e||(yield n._updateCurrentUser(s.user)),s})}function al(n,t,e,r){return ne(n).onIdTokenChanged(t,e,r)}function cl(n,t,e){return ne(n).beforeAuthStateChanged(t,e)}var Zs="__sak";var Js=class{constructor(t,e){this.storageRetriever=t,this.type=e}_isAvailable(){try{return this.storage?(this.storage.setItem(Zs,"1"),this.storage.removeItem(Zs),Promise.resolve(!0)):Promise.resolve(!1)}catch{return Promise.resolve(!1)}}_set(t,e){return this.storage.setItem(t,JSON.stringify(e)),Promise.resolve()}_get(t){let e=this.storage.getItem(t);return Promise.resolve(e?JSON.parse(e):null)}_remove(t){return this.storage.removeItem(t),Promise.resolve()}get storage(){return this.storageRetriever()}};var rE=1e3,iE=10,sE=(()=>{class n extends Js{constructor(){super(()=>window.localStorage,"LOCAL"),this.boundEventHandler=(e,r)=>this.onStorageEvent(e,r),this.listeners={},this.localCache={},this.pollTimer=null,this.fallbackToPolling=lp(),this._shouldAllowMigration=!0}forAllChangedKeys(e){for(let r of Object.keys(this.listeners)){let i=this.storage.getItem(r),s=this.localCache[r];i!==s&&e(r,s,i)}}onStorageEvent(e,r=!1){if(!e.key){this.forAllChangedKeys((a,c,l)=>{this.notifyListeners(a,l)});return}let i=e.key;r?this.detachListener():this.stopPolling();let s=()=>{let a=this.storage.getItem(i);!r&&this.localCache[i]===a||this.notifyListeners(i,a)},o=this.storage.getItem(i);Hy()&&o!==e.newValue&&e.newValue!==e.oldValue?setTimeout(s,iE):s()}notifyListeners(e,r){this.localCache[e]=r;let i=this.listeners[e];if(i)for(let s of Array.from(i))s(r&&JSON.parse(r))}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>{this.forAllChangedKeys((e,r,i)=>{this.onStorageEvent(new StorageEvent("storage",{key:e,oldValue:r,newValue:i}),!0)})},rE)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}attachListener(){window.addEventListener("storage",this.boundEventHandler)}detachListener(){window.removeEventListener("storage",this.boundEventHandler)}_addListener(e,r){Object.keys(this.listeners).length===0&&(this.fallbackToPolling?this.startPolling():this.attachListener()),this.listeners[e]||(this.listeners[e]=new Set,this.localCache[e]=this.storage.getItem(e)),this.listeners[e].add(r)}_removeListener(e,r){this.listeners[e]&&(this.listeners[e].delete(r),this.listeners[e].size===0&&delete this.listeners[e]),Object.keys(this.listeners).length===0&&(this.detachListener(),this.stopPolling())}_set(e,r){return h(this,null,function*(){yield Gt(n.prototype,this,"_set").call(this,e,r),this.localCache[e]=JSON.stringify(r)})}_get(e){return h(this,null,function*(){let r=yield Gt(n.prototype,this,"_get").call(this,e);return this.localCache[e]=JSON.stringify(r),r})}_remove(e){return h(this,null,function*(){yield Gt(n.prototype,this,"_remove").call(this,e),delete this.localCache[e]})}}return n.type="LOCAL",n})(),pp=sE;var oE=(()=>{class n extends Js{constructor(){super(()=>window.sessionStorage,"SESSION")}_addListener(e,r){}_removeListener(e,r){}}return n.type="SESSION",n})(),ll=oE;function aE(n){return Promise.all(n.map(t=>h(null,null,function*(){try{return{fulfilled:!0,value:yield t}}catch(e){return{fulfilled:!1,reason:e}}})))}var cE=(()=>{class n{constructor(e){this.eventTarget=e,this.handlersMap={},this.boundEventHandler=this.handleEvent.bind(this)}static _getInstance(e){let r=this.receivers.find(s=>s.isListeningto(e));if(r)return r;let i=new n(e);return this.receivers.push(i),i}isListeningto(e){return this.eventTarget===e}handleEvent(e){return h(this,null,function*(){let r=e,{eventId:i,eventType:s,data:o}=r.data,a=this.handlersMap[s];if(!a?.size)return;r.ports[0].postMessage({status:"ack",eventId:i,eventType:s});let c=Array.from(a).map(u=>h(this,null,function*(){return u(r.origin,o)})),l=yield aE(c);r.ports[0].postMessage({status:"done",eventId:i,eventType:s,response:l})})}_subscribe(e,r){Object.keys(this.handlersMap).length===0&&this.eventTarget.addEventListener("message",this.boundEventHandler),this.handlersMap[e]||(this.handlersMap[e]=new Set),this.handlersMap[e].add(r)}_unsubscribe(e,r){this.handlersMap[e]&&r&&this.handlersMap[e].delete(r),(!r||this.handlersMap[e].size===0)&&delete this.handlersMap[e],Object.keys(this.handlersMap).length===0&&this.eventTarget.removeEventListener("message",this.boundEventHandler)}}n.receivers=[];return n})();function mp(n="",t=10){let e="";for(let r=0;r<t;r++)e+=Math.floor(Math.random()*10);return n+e}var Hc=class{constructor(t){this.target=t,this.handlers=new Set}removeMessageHandler(t){t.messageChannel&&(t.messageChannel.port1.removeEventListener("message",t.onMessage),t.messageChannel.port1.close()),this.handlers.delete(t)}_send(t,e,r=50){return h(this,null,function*(){let i=typeof MessageChannel<"u"?new MessageChannel:null;if(!i)throw new Error("connection_unavailable");let s,o;return new Promise((a,c)=>{let l=mp("",20);i.port1.start();let u=setTimeout(()=>{c(new Error("unsupported_event"))},r);o={messageChannel:i,onMessage(p){let f=p;if(f.data.eventId===l)switch(f.data.status){case"ack":clearTimeout(u),s=setTimeout(()=>{c(new Error("timeout"))},3e3);break;case"done":clearTimeout(s),a(f.data.response);break;default:clearTimeout(u),clearTimeout(s),c(new Error("invalid_response"));break}}},this.handlers.add(o),i.port1.addEventListener("message",o.onMessage),this.target.postMessage({eventType:t,eventId:l,data:e},[i.port2])}).finally(()=>{o&&this.removeMessageHandler(o)})})}};function Ze(){return window}function lE(n){Ze().location.href=n}function gp(){return typeof Ze().WorkerGlobalScope<"u"&&typeof Ze().importScripts=="function"}function uE(){return h(this,null,function*(){if(!navigator?.serviceWorker)return null;try{return(yield navigator.serviceWorker.ready).active}catch{return null}})}function dE(){var n;return((n=navigator?.serviceWorker)===null||n===void 0?void 0:n.controller)||null}function hE(){return gp()?self:null}var _p="firebaseLocalStorageDb",fE=1,Qs="firebaseLocalStorage",vp="fbase_key",yn=class{constructor(t){this.request=t}toPromise(){return new Promise((t,e)=>{this.request.addEventListener("success",()=>{t(this.request.result)}),this.request.addEventListener("error",()=>{e(this.request.error)})})}};function ro(n,t){return n.transaction([Qs],t?"readwrite":"readonly").objectStore(Qs)}function pE(){let n=indexedDB.deleteDatabase(_p);return new yn(n).toPromise()}function Wc(){let n=indexedDB.open(_p,fE);return new Promise((t,e)=>{n.addEventListener("error",()=>{e(n.error)}),n.addEventListener("upgradeneeded",()=>{let r=n.result;try{r.createObjectStore(Qs,{keyPath:vp})}catch(i){e(i)}}),n.addEventListener("success",()=>h(null,null,function*(){let r=n.result;r.objectStoreNames.contains(Qs)?t(r):(r.close(),yield pE(),t(yield Wc()))}))})}function jf(n,t,e){return h(this,null,function*(){let r=ro(n,!0).put({[vp]:t,value:e});return new yn(r).toPromise()})}function mE(n,t){return h(this,null,function*(){let e=ro(n,!1).get(t),r=yield new yn(e).toPromise();return r===void 0?null:r.value})}function Vf(n,t){let e=ro(n,!0).delete(t);return new yn(e).toPromise()}var gE=800,_E=3,vE=(()=>{class n{constructor(){this.type="LOCAL",this._shouldAllowMigration=!0,this.listeners={},this.localCache={},this.pollTimer=null,this.pendingWrites=0,this.receiver=null,this.sender=null,this.serviceWorkerReceiverAvailable=!1,this.activeServiceWorker=null,this._workerInitializationPromise=this.initializeServiceWorkerMessaging().then(()=>{},()=>{})}_openDb(){return h(this,null,function*(){return this.db?this.db:(this.db=yield Wc(),this.db)})}_withRetries(e){return h(this,null,function*(){let r=0;for(;;)try{let i=yield this._openDb();return yield e(i)}catch(i){if(r++>_E)throw i;this.db&&(this.db.close(),this.db=void 0)}})}initializeServiceWorkerMessaging(){return h(this,null,function*(){return gp()?this.initializeReceiver():this.initializeSender()})}initializeReceiver(){return h(this,null,function*(){this.receiver=cE._getInstance(hE()),this.receiver._subscribe("keyChanged",(e,r)=>h(this,null,function*(){return{keyProcessed:(yield this._poll()).includes(r.key)}})),this.receiver._subscribe("ping",(e,r)=>h(this,null,function*(){return["keyChanged"]}))})}initializeSender(){return h(this,null,function*(){var e,r;if(this.activeServiceWorker=yield uE(),!this.activeServiceWorker)return;this.sender=new Hc(this.activeServiceWorker);let i=yield this.sender._send("ping",{},800);i&&!((e=i[0])===null||e===void 0)&&e.fulfilled&&!((r=i[0])===null||r===void 0)&&r.value.includes("keyChanged")&&(this.serviceWorkerReceiverAvailable=!0)})}notifyServiceWorker(e){return h(this,null,function*(){if(!(!this.sender||!this.activeServiceWorker||dE()!==this.activeServiceWorker))try{yield this.sender._send("keyChanged",{key:e},this.serviceWorkerReceiverAvailable?800:50)}catch{}})}_isAvailable(){return h(this,null,function*(){try{if(!indexedDB)return!1;let e=yield Wc();return yield jf(e,Zs,"1"),yield Vf(e,Zs),!0}catch{}return!1})}_withPendingWrite(e){return h(this,null,function*(){this.pendingWrites++;try{yield e()}finally{this.pendingWrites--}})}_set(e,r){return h(this,null,function*(){return this._withPendingWrite(()=>h(this,null,function*(){return yield this._withRetries(i=>jf(i,e,r)),this.localCache[e]=r,this.notifyServiceWorker(e)}))})}_get(e){return h(this,null,function*(){let r=yield this._withRetries(i=>mE(i,e));return this.localCache[e]=r,r})}_remove(e){return h(this,null,function*(){return this._withPendingWrite(()=>h(this,null,function*(){return yield this._withRetries(r=>Vf(r,e)),delete this.localCache[e],this.notifyServiceWorker(e)}))})}_poll(){return h(this,null,function*(){let e=yield this._withRetries(s=>{let o=ro(s,!1).getAll();return new yn(o).toPromise()});if(!e)return[];if(this.pendingWrites!==0)return[];let r=[],i=new Set;if(e.length!==0)for(let{fbase_key:s,value:o}of e)i.add(s),JSON.stringify(this.localCache[s])!==JSON.stringify(o)&&(this.notifyListeners(s,o),r.push(s));for(let s of Object.keys(this.localCache))this.localCache[s]&&!i.has(s)&&(this.notifyListeners(s,null),r.push(s));return r})}notifyListeners(e,r){this.localCache[e]=r;let i=this.listeners[e];if(i)for(let s of Array.from(i))s(r)}startPolling(){this.stopPolling(),this.pollTimer=setInterval(()=>h(this,null,function*(){return this._poll()}),gE)}stopPolling(){this.pollTimer&&(clearInterval(this.pollTimer),this.pollTimer=null)}_addListener(e,r){Object.keys(this.listeners).length===0&&this.startPolling(),this.listeners[e]||(this.listeners[e]=new Set,this._get(e)),this.listeners[e].add(r)}_removeListener(e,r){this.listeners[e]&&(this.listeners[e].delete(r),this.listeners[e].size===0&&delete this.listeners[e]),Object.keys(this.listeners).length===0&&this.stopPolling()}}return n.type="LOCAL",n})(),bp=vE;var gN=dp("rcb"),_N=new vn(3e4,6e4);function bE(n,t){return t?bt(t):(I(n._popupRedirectResolver,n,"argument-error"),n._popupRedirectResolver)}var li=class extends Ys{constructor(t){super("custom","custom"),this.params=t}_getIdTokenResponse(t){return Mc(t,this._buildIdpRequest())}_linkToIdToken(t,e){return Mc(t,this._buildIdpRequest(e))}_getReauthenticationResolver(t){return Mc(t,this._buildIdpRequest())}_buildIdpRequest(t){let e={requestUri:this.params.requestUri,sessionId:this.params.sessionId,postBody:this.params.postBody,tenantId:this.params.tenantId,pendingToken:this.params.pendingToken,returnSecureToken:!0,returnIdpCredential:!0};return t&&(e.idToken=t),e}};function yE(n){return nE(n.auth,new li(n),n.bypassAuthState)}function EE(n){let{auth:t,user:e}=n;return I(e,t,"internal-error"),tE(e,new li(n),n.bypassAuthState)}function wE(n){return h(this,null,function*(){let{auth:t,user:e}=n;return I(e,t,"internal-error"),eE(e,new li(n),n.bypassAuthState)})}var Gc=class{constructor(t,e,r,i,s=!1){this.auth=t,this.resolver=r,this.user=i,this.bypassAuthState=s,this.pendingPromise=null,this.eventManager=null,this.filter=Array.isArray(e)?e:[e]}execute(){return new Promise((t,e)=>h(this,null,function*(){this.pendingPromise={resolve:t,reject:e};try{this.eventManager=yield this.resolver._initialize(this.auth),yield this.onExecution(),this.eventManager.registerConsumer(this)}catch(r){this.reject(r)}}))}onAuthEvent(t){return h(this,null,function*(){let{urlResponse:e,sessionId:r,postBody:i,tenantId:s,error:o,type:a}=t;if(o){this.reject(o);return}let c={auth:this.auth,requestUri:e,sessionId:r,tenantId:s||void 0,postBody:i||void 0,user:this.user,bypassAuthState:this.bypassAuthState};try{this.resolve(yield this.getIdpTask(a)(c))}catch(l){this.reject(l)}})}onError(t){this.reject(t)}getIdpTask(t){switch(t){case"signInViaPopup":case"signInViaRedirect":return yE;case"linkViaPopup":case"linkViaRedirect":return wE;case"reauthViaPopup":case"reauthViaRedirect":return EE;default:$t(this.auth,"internal-error")}}resolve(t){zt(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.resolve(t),this.unregisterAndCleanUp()}reject(t){zt(this.pendingPromise,"Pending promise was never set"),this.pendingPromise.reject(t),this.unregisterAndCleanUp()}unregisterAndCleanUp(){this.eventManager&&this.eventManager.unregisterConsumer(this),this.pendingPromise=null,this.cleanUp()}};var vN=new vn(2e3,1e4);var IE="pendingRedirect",Vs=new Map,qc=class n extends Gc{constructor(t,e,r=!1){super(t,["signInViaRedirect","linkViaRedirect","reauthViaRedirect","unknown"],e,void 0,r),this.eventId=null}execute(){return h(this,null,function*(){let t=Vs.get(this.auth._key());if(!t){try{let r=(yield DE(this.resolver,this.auth))?yield Gt(n.prototype,this,"execute").call(this):null;t=()=>Promise.resolve(r)}catch(e){t=()=>Promise.reject(e)}Vs.set(this.auth._key(),t)}return this.bypassAuthState||Vs.set(this.auth._key(),()=>Promise.resolve(null)),t()})}onAuthEvent(t){return h(this,null,function*(){if(t.type==="signInViaRedirect")return Gt(n.prototype,this,"onAuthEvent").call(this,t);if(t.type==="unknown"){this.resolve(null);return}if(t.eventId){let e=yield this.auth._redirectUserForId(t.eventId);if(e)return this.user=e,Gt(n.prototype,this,"onAuthEvent").call(this,t);this.resolve(null)}})}onExecution(){return h(this,null,function*(){})}cleanUp(){}};function DE(n,t){return h(this,null,function*(){let e=TE(t),r=SE(n);if(!(yield r._isAvailable()))return!1;let i=(yield r._get(e))==="true";return yield r._remove(e),i})}function CE(n,t){Vs.set(n._key(),t)}function SE(n){return bt(n._redirectPersistence)}function TE(n){return js(IE,n.config.apiKey,n.name)}function RE(n,t,e=!1){return h(this,null,function*(){if(pe(n.app))return Promise.reject(Vt(n));let r=no(n),i=bE(r,t),o=yield new qc(r,i,e).execute();return o&&!e&&(delete o.user._redirectEventId,yield r._persistUserIfCurrent(o.user),yield r._setRedirectUser(null,t)),o})}var AE=10*60*1e3,Kc=class{constructor(t){this.auth=t,this.cachedEventUids=new Set,this.consumers=new Set,this.queuedRedirectEvent=null,this.hasHandledPotentialRedirect=!1,this.lastProcessedEventTime=Date.now()}registerConsumer(t){this.consumers.add(t),this.queuedRedirectEvent&&this.isEventForConsumer(this.queuedRedirectEvent,t)&&(this.sendToConsumer(this.queuedRedirectEvent,t),this.saveEventToCache(this.queuedRedirectEvent),this.queuedRedirectEvent=null)}unregisterConsumer(t){this.consumers.delete(t)}onEvent(t){if(this.hasEventBeenHandled(t))return!1;let e=!1;return this.consumers.forEach(r=>{this.isEventForConsumer(t,r)&&(e=!0,this.sendToConsumer(t,r),this.saveEventToCache(t))}),this.hasHandledPotentialRedirect||!kE(t)||(this.hasHandledPotentialRedirect=!0,e||(this.queuedRedirectEvent=t,e=!0)),e}sendToConsumer(t,e){var r;if(t.error&&!yp(t)){let i=((r=t.error.code)===null||r===void 0?void 0:r.split("auth/")[1])||"internal-error";e.onError(_n(this.auth,i))}else e.onAuthEvent(t)}isEventForConsumer(t,e){let r=e.eventId===null||!!t.eventId&&t.eventId===e.eventId;return e.filter.includes(t.type)&&r}hasEventBeenHandled(t){return Date.now()-this.lastProcessedEventTime>=AE&&this.cachedEventUids.clear(),this.cachedEventUids.has($f(t))}saveEventToCache(t){this.cachedEventUids.add($f(t)),this.lastProcessedEventTime=Date.now()}};function $f(n){return[n.type,n.eventId,n.sessionId,n.tenantId].filter(t=>t).join("-")}function yp({type:n,error:t}){return n==="unknown"&&t?.code==="auth/no-auth-event"}function kE(n){switch(n.type){case"signInViaRedirect":case"linkViaRedirect":case"reauthViaRedirect":return!0;case"unknown":return yp(n);default:return!1}}function OE(e){return h(this,arguments,function*(n,t={}){return nr(n,"GET","/v1/projects",t)})}var PE=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,ME=/^https?/;function NE(n){return h(this,null,function*(){if(n.config.emulator)return;let{authorizedDomains:t}=yield OE(n);for(let e of t)try{if(xE(e))return}catch{}$t(n,"unauthorized-domain")})}function xE(n){let t=xc(),{protocol:e,hostname:r}=new URL(t);if(n.startsWith("chrome-extension://")){let o=new URL(n);return o.hostname===""&&r===""?e==="chrome-extension:"&&n.replace("chrome-extension://","")===t.replace("chrome-extension://",""):e==="chrome-extension:"&&o.hostname===r}if(!ME.test(e))return!1;if(PE.test(n))return r===n;let i=n.replace(/\./g,"\\.");return new RegExp("^(.+\\."+i+"|"+i+")$","i").test(r)}var FE=new vn(3e4,6e4);function zf(){let n=Ze().___jsl;if(n?.H){for(let t of Object.keys(n.H))if(n.H[t].r=n.H[t].r||[],n.H[t].L=n.H[t].L||[],n.H[t].r=[...n.H[t].L],n.CP)for(let e=0;e<n.CP.length;e++)n.CP[e]=null}}function LE(n){return new Promise((t,e)=>{var r,i,s;function o(){zf(),gapi.load("gapi.iframes",{callback:()=>{t(gapi.iframes.getContext())},ontimeout:()=>{zf(),e(_n(n,"network-request-failed"))},timeout:FE.get()})}if(!((i=(r=Ze().gapi)===null||r===void 0?void 0:r.iframes)===null||i===void 0)&&i.Iframe)t(gapi.iframes.getContext());else if(!((s=Ze().gapi)===null||s===void 0)&&s.load)o();else{let a=dp("iframefcb");return Ze()[a]=()=>{gapi.load?o():e(_n(n,"network-request-failed"))},Ky(`${Yy()}?onload=${a}`).catch(c=>e(c))}}).catch(t=>{throw $s=null,t})}var $s=null;function UE(n){return $s=$s||LE(n),$s}var BE=new vn(5e3,15e3),jE="__/auth/iframe",VE="emulator/auth/iframe",$E={style:{position:"absolute",top:"-100px",width:"1px",height:"1px"},"aria-hidden":"true",tabindex:"-1"},zE=new Map([["identitytoolkit.googleapis.com","p"],["staging-identitytoolkit.sandbox.googleapis.com","s"],["test-identitytoolkit.sandbox.googleapis.com","t"]]);function HE(n){let t=n.config;I(t.authDomain,n,"auth-domain-config-required");let e=t.emulator?Jc(t,VE):`https://${n.config.authDomain}/${jE}`,r={apiKey:t.apiKey,appName:n.name,v:xt},i=zE.get(n.config.apiHost);i&&(r.eid=i);let s=n._getFrameworks();return s.length&&(r.fw=s.join(",")),`${e}?${ei(r).slice(1)}`}function WE(n){return h(this,null,function*(){let t=yield UE(n),e=Ze().gapi;return I(e,n,"internal-error"),t.open({where:document.body,url:HE(n),messageHandlersFilter:e.iframes.CROSS_ORIGIN_IFRAMES_FILTER,attributes:$E,dontclear:!0},r=>new Promise((i,s)=>h(null,null,function*(){yield r.restyle({setHideOnLeave:!1});let o=_n(n,"network-request-failed"),a=Ze().setTimeout(()=>{s(o)},BE.get());function c(){Ze().clearTimeout(a),i(r)}r.ping(c).then(c,()=>{s(o)})})))})}var GE={location:"yes",resizable:"yes",statusbar:"yes",toolbar:"no"},qE=500,KE=600,YE="_blank",XE="http://localhost",eo=class{constructor(t){this.window=t,this.associatedEvent=null}close(){if(this.window)try{this.window.close()}catch{}}};function ZE(n,t,e,r=qE,i=KE){let s=Math.max((window.screen.availHeight-i)/2,0).toString(),o=Math.max((window.screen.availWidth-r)/2,0).toString(),a="",c=Object.assign(Object.assign({},GE),{width:r.toString(),height:i.toString(),top:s,left:o}),l=ce().toLowerCase();e&&(a=ip(l)?YE:e),np(l)&&(t=t||XE,c.scrollbars="yes");let u=Object.entries(c).reduce((f,[m,v])=>`${f}${m}=${v},`,"");if(zy(l)&&a!=="_self")return JE(t||"",a),new eo(null);let p=window.open(t||"",a,u);I(p,n,"popup-blocked");try{p.focus()}catch{}return new eo(p)}function JE(n,t){let e=document.createElement("a");e.href=n,e.target=t;let r=document.createEvent("MouseEvent");r.initMouseEvent("click",!0,!0,window,1,0,0,0,0,!1,!1,!1,!1,1,null),e.dispatchEvent(r)}var QE="__/auth/handler",ew="emulator/auth/handler",tw=encodeURIComponent("fac");function Hf(n,t,e,r,i,s){return h(this,null,function*(){I(n.config.authDomain,n,"auth-domain-config-required"),I(n.config.apiKey,n,"invalid-api-key");let o={apiKey:n.config.apiKey,appName:n.name,authType:e,redirectUrl:r,v:xt,eventId:i};if(t instanceof Xs){t.setDefaultLanguage(n.languageCode),o.providerId=t.providerId||"",sf(t.getCustomParameters())||(o.customParameters=JSON.stringify(t.getCustomParameters()));for(let[u,p]of Object.entries(s||{}))o[u]=p}if(t instanceof $c){let u=t.getScopes().filter(p=>p!=="");u.length>0&&(o.scopes=u.join(","))}n.tenantId&&(o.tid=n.tenantId);let a=o;for(let u of Object.keys(a))a[u]===void 0&&delete a[u];let c=yield n._getAppCheckToken(),l=c?`#${tw}=${encodeURIComponent(c)}`:"";return`${nw(n)}?${ei(a).slice(1)}${l}`})}function nw({config:n}){return n.emulator?Jc(n,ew):`https://${n.authDomain}/${QE}`}var Nc="webStorageSupport",Yc=class{constructor(){this.eventManagers={},this.iframes={},this.originValidationPromises={},this._redirectPersistence=ll,this._completeRedirectFn=RE,this._overrideRedirectResult=CE}_openPopup(t,e,r,i){return h(this,null,function*(){var s;zt((s=this.eventManagers[t._key()])===null||s===void 0?void 0:s.manager,"_initialize() not called before _openPopup()");let o=yield Hf(t,e,r,xc(),i);return ZE(t,o,mp())})}_openRedirect(t,e,r,i){return h(this,null,function*(){yield this._originValidation(t);let s=yield Hf(t,e,r,xc(),i);return lE(s),new Promise(()=>{})})}_initialize(t){let e=t._key();if(this.eventManagers[e]){let{manager:i,promise:s}=this.eventManagers[e];return i?Promise.resolve(i):(zt(s,"If manager is not set, promise should be"),s)}let r=this.initAndGetManager(t);return this.eventManagers[e]={promise:r},r.catch(()=>{delete this.eventManagers[e]}),r}initAndGetManager(t){return h(this,null,function*(){let e=yield WE(t),r=new Kc(t);return e.register("authEvent",i=>(I(i?.authEvent,t,"invalid-auth-event"),{status:r.onEvent(i.authEvent)?"ACK":"ERROR"}),gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER),this.eventManagers[t._key()]={manager:r},this.iframes[t._key()]=e,r})}_isIframeWebStorageSupported(t,e){this.iframes[t._key()].send(Nc,{type:Nc},i=>{var s;let o=(s=i?.[0])===null||s===void 0?void 0:s[Nc];o!==void 0&&e(!!o),$t(t,"internal-error")},gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER)}_originValidation(t){let e=t._key();return this.originValidationPromises[e]||(this.originValidationPromises[e]=NE(t)),this.originValidationPromises[e]}get _shouldInitProactively(){return lp()||rp()||nl()}},Ep=Yc;var Wf="@firebase/auth",Gf="1.10.7";var Xc=class{constructor(t){this.auth=t,this.internalListeners=new Map}getUid(){var t;return this.assertAuthConfigured(),((t=this.auth.currentUser)===null||t===void 0?void 0:t.uid)||null}getToken(t){return h(this,null,function*(){return this.assertAuthConfigured(),yield this.auth._initializationPromise,this.auth.currentUser?{accessToken:yield this.auth.currentUser.getIdToken(t)}:null})}addAuthTokenListener(t){if(this.assertAuthConfigured(),this.internalListeners.has(t))return;let e=this.auth.onIdTokenChanged(r=>{t(r?.stsTokenManager.accessToken||null)});this.internalListeners.set(t,e),this.updateProactiveRefresh()}removeAuthTokenListener(t){this.assertAuthConfigured();let e=this.internalListeners.get(t);e&&(this.internalListeners.delete(t),e(),this.updateProactiveRefresh())}assertAuthConfigured(){I(this.auth._initializationPromise,"dependent-sdk-initialized-before-auth")}updateProactiveRefresh(){this.internalListeners.size>0?this.auth._startProactiveRefresh():this.auth._stopProactiveRefresh()}};function rw(n){switch(n){case"Node":return"node";case"ReactNative":return"rn";case"Worker":return"webworker";case"Cordova":return"cordova";case"WebExtension":return"web-extension";default:return}}function iw(n){Ce(new re("auth",(t,{options:e})=>{let r=t.getProvider("app").getImmediate(),i=t.getProvider("heartbeat"),s=t.getProvider("app-check-internal"),{apiKey:o,authDomain:a}=r.options;I(o&&!o.includes(":"),"invalid-api-key",{appName:r.name});let c={apiKey:o,authDomain:a,clientPlatform:n,apiHost:"identitytoolkit.googleapis.com",tokenApiHost:"securetoken.googleapis.com",apiScheme:"https",sdkClientVersion:up(n)},l=new Vc(r,i,s,c);return Xy(l,e),l},"PUBLIC").setInstantiationMode("EXPLICIT").setInstanceCreatedCallback((t,e,r)=>{t.getProvider("auth-internal").initialize()})),Ce(new re("auth-internal",t=>{let e=no(t.getProvider("auth").getImmediate());return(r=>new Xc(r))(e)},"PRIVATE").setInstantiationMode("EXPLICIT")),L(Wf,Gf,rw(n)),L(Wf,Gf,"esm2017")}var sw=5*60,ow=lc("authIdTokenMaxAge")||sw,qf=null,aw=n=>t=>h(null,null,function*(){let e=t&&(yield t.getIdTokenResult()),r=e&&(new Date().getTime()-Date.parse(e.issuedAtTime))/1e3;if(r&&r>ow)return;let i=e?.token;qf!==i&&(qf=i,yield fetch(n,{method:i?"POST":"DELETE",headers:i?{Authorization:`Bearer ${i}`}:{}}))});function ul(n=gt()){let t=Nt(n,"auth");if(t.isInitialized())return t.getImmediate();let e=il(n,{popupRedirectResolver:Ep,persistence:[bp,pp,ll]}),r=lc("authTokenSyncURL");if(r&&typeof isSecureContext=="boolean"&&isSecureContext){let s=new URL(r,location.origin);if(location.origin===s.origin){let o=aw(s.toString());cl(e,o,()=>o(e.currentUser)),al(e,a=>o(a))}}let i=ac("auth");return i&&sl(e,`http://${i}`),e}function cw(){var n,t;return(t=(n=document.getElementsByTagName("head"))===null||n===void 0?void 0:n[0])!==null&&t!==void 0?t:document}qy({loadJS(n){return new Promise((t,e)=>{let r=document.createElement("script");r.setAttribute("src",n),r.onload=t,r.onerror=i=>{let s=_n("internal-error");s.customData=i,e(s)},r.type="text/javascript",r.charset="UTF-8",cw().appendChild(r)})},gapiScript:"https://apis.google.com/js/api.js",recaptchaV2Script:"https://www.google.com/recaptcha/api.js",recaptchaEnterpriseScript:"https://www.google.com/recaptcha/enterprise.js?render="});iw("Browser");var wp="auth",En=class{constructor(t){return t}},wn=class{constructor(){return Ft(wp)}};var dl=new y("angularfire2.auth-instances");function eI(n,t){let e=gn(wp,n,t);return e&&new En(e)}function tI(n){return(t,e)=>{let r=t.runOutsideAngular(()=>n(e));return new En(r)}}var nI={provide:wn,deps:[[new q,dl]]},rI={provide:En,useFactory:eI,deps:[[new q,dl],Fe]};function ax(n,...t){return L("angularfire",Ye.full,"auth"),Te([rI,nI,{provide:dl,useFactory:tI(n),multi:!0,deps:[D,k,Xe,_t,[new q,Ut],...t]}])}var cx=_e(ul,!0);var Ip=_e(ol,!0,2);var kp="firebasestorage.googleapis.com",Op="storageBucket",iI=2*60*1e3,sI=10*60*1e3;var z=class n extends fe{constructor(t,e,r=0){super(hl(t),`Firebase Storage: ${e} (${hl(t)})`),this.status_=r,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,n.prototype)}get status(){return this.status_}set status(t){this.status_=t}_codeEquals(t){return hl(t)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(t){this.customData.serverResponse=t,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}},G=function(n){return n.UNKNOWN="unknown",n.OBJECT_NOT_FOUND="object-not-found",n.BUCKET_NOT_FOUND="bucket-not-found",n.PROJECT_NOT_FOUND="project-not-found",n.QUOTA_EXCEEDED="quota-exceeded",n.UNAUTHENTICATED="unauthenticated",n.UNAUTHORIZED="unauthorized",n.UNAUTHORIZED_APP="unauthorized-app",n.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",n.INVALID_CHECKSUM="invalid-checksum",n.CANCELED="canceled",n.INVALID_EVENT_NAME="invalid-event-name",n.INVALID_URL="invalid-url",n.INVALID_DEFAULT_BUCKET="invalid-default-bucket",n.NO_DEFAULT_BUCKET="no-default-bucket",n.CANNOT_SLICE_BLOB="cannot-slice-blob",n.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",n.NO_DOWNLOAD_URL="no-download-url",n.INVALID_ARGUMENT="invalid-argument",n.INVALID_ARGUMENT_COUNT="invalid-argument-count",n.APP_DELETED="app-deleted",n.INVALID_ROOT_OPERATION="invalid-root-operation",n.INVALID_FORMAT="invalid-format",n.INTERNAL_ERROR="internal-error",n.UNSUPPORTED_ENVIRONMENT="unsupported-environment",n}(G||{});function hl(n){return"storage/"+n}function vl(){let n="An unknown error occurred, please check the error payload for server response.";return new z(G.UNKNOWN,n)}function oI(n){return new z(G.OBJECT_NOT_FOUND,"Object '"+n+"' does not exist.")}function aI(n){return new z(G.QUOTA_EXCEEDED,"Quota for bucket '"+n+"' exceeded, please view quota on https://firebase.google.com/pricing/.")}function cI(){let n="User is not authenticated, please authenticate using Firebase Authentication and try again.";return new z(G.UNAUTHENTICATED,n)}function lI(){return new z(G.UNAUTHORIZED_APP,"This app does not have permission to access Firebase Storage on this project.")}function uI(n){return new z(G.UNAUTHORIZED,"User does not have permission to access '"+n+"'.")}function dI(){return new z(G.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.")}function hI(){return new z(G.CANCELED,"User canceled the upload/download.")}function fI(n){return new z(G.INVALID_URL,"Invalid URL '"+n+"'.")}function pI(n){return new z(G.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+n+"'.")}function mI(){return new z(G.NO_DEFAULT_BUCKET,"No default bucket found. Did you set the '"+Op+"' property when initializing the app?")}function gI(){return new z(G.CANNOT_SLICE_BLOB,"Cannot slice blob for upload. Please retry the upload.")}function _I(){return new z(G.NO_DOWNLOAD_URL,"The given file does not have any download URLs.")}function vI(n){return new z(G.UNSUPPORTED_ENVIRONMENT,`${n} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`)}function fl(n){return new z(G.INVALID_ARGUMENT,n)}function Pp(){return new z(G.APP_DELETED,"The Firebase app was deleted.")}function bI(n){return new z(G.INVALID_ROOT_OPERATION,"The operation '"+n+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}function di(n,t){return new z(G.INVALID_FORMAT,"String does not match format '"+n+"': "+t)}function ui(n){throw new z(G.INTERNAL_ERROR,"Internal error: "+n)}var Ue=class n{constructor(t,e){this.bucket=t,this.path_=e}get path(){return this.path_}get isRoot(){return this.path.length===0}fullServerUrl(){let t=encodeURIComponent;return"/b/"+t(this.bucket)+"/o/"+t(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(t,e){let r;try{r=n.makeFromUrl(t,e)}catch{return new n(t,"")}if(r.path==="")return r;throw pI(t)}static makeFromUrl(t,e){let r=null,i="([A-Za-z0-9.\\-_]+)";function s(Z){Z.path.charAt(Z.path.length-1)==="/"&&(Z.path_=Z.path_.slice(0,-1))}let o="(/(.*))?$",a=new RegExp("^gs://"+i+o,"i"),c={bucket:1,path:3};function l(Z){Z.path_=decodeURIComponent(Z.path)}let u="v[A-Za-z0-9_]+",p=e.replace(/[.]/g,"\\."),f="(/([^?#]*).*)?$",m=new RegExp(`^https?://${p}/${u}/b/${i}/o${f}`,"i"),v={bucket:1,path:3},C=e===kp?"(?:storage.googleapis.com|storage.cloud.google.com)":e,R="([^?#]*)",X=new RegExp(`^https?://${C}/${i}/${R}`,"i"),U=[{regex:a,indices:c,postModify:s},{regex:m,indices:v,postModify:l},{regex:X,indices:{bucket:1,path:2},postModify:l}];for(let Z=0;Z<U.length;Z++){let Ht=U[Z],Wt=Ht.regex.exec(t);if(Wt){let pi=Wt[Ht.indices.bucket],ar=Wt[Ht.indices.path];ar||(ar=""),r=new n(pi,ar),Ht.postModify(r);break}}if(r==null)throw fI(t);return r}},pl=class{constructor(t){this.promise_=Promise.reject(t)}getPromise(){return this.promise_}cancel(t=!1){}};function yI(n,t,e){let r=1,i=null,s=null,o=!1,a=0;function c(){return a===2}let l=!1;function u(...R){l||(l=!0,t.apply(null,R))}function p(R){i=setTimeout(()=>{i=null,n(m,c())},R)}function f(){s&&clearTimeout(s)}function m(R,...X){if(l){f();return}if(R){f(),u.call(null,R,...X);return}if(c()||o){f(),u.call(null,R,...X);return}r<64&&(r*=2);let U;a===1?(a=2,U=0):U=(r+Math.random())*1e3,p(U)}let v=!1;function C(R){v||(v=!0,f(),!l&&(i!==null?(R||(a=2),clearTimeout(i),p(0)):R||(a=1)))}return p(0),s=setTimeout(()=>{o=!0,C(!0)},e),C}function EI(n){n(!1)}function wI(n){return n!==void 0}function II(n){return typeof n=="object"&&!Array.isArray(n)}function bl(n){return typeof n=="string"||n instanceof String}function Dp(n){return yl()&&n instanceof Blob}function yl(){return typeof Blob<"u"}function Cp(n,t,e,r){if(r<t)throw fl(`Invalid value for '${n}'. Expected ${t} or greater.`);if(r>e)throw fl(`Invalid value for '${n}'. Expected ${e} or less.`)}function El(n,t,e){let r=t;return e==null&&(r=`https://${t}`),`${e}://${r}/v0${n}`}function Mp(n){let t=encodeURIComponent,e="?";for(let r in n)if(n.hasOwnProperty(r)){let i=t(r)+"="+t(n[r]);e=e+i+"&"}return e=e.slice(0,-1),e}var ir=function(n){return n[n.NO_ERROR=0]="NO_ERROR",n[n.NETWORK_ERROR=1]="NETWORK_ERROR",n[n.ABORT=2]="ABORT",n}(ir||{});function DI(n,t){let e=n>=500&&n<600,i=[408,429].indexOf(n)!==-1,s=t.indexOf(n)!==-1;return e||i||s}var ml=class{constructor(t,e,r,i,s,o,a,c,l,u,p,f=!0,m=!1){this.url_=t,this.method_=e,this.headers_=r,this.body_=i,this.successCodes_=s,this.additionalRetryCodes_=o,this.callback_=a,this.errorCallback_=c,this.timeout_=l,this.progressCallback_=u,this.connectionFactory_=p,this.retry=f,this.isUsingEmulator=m,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((v,C)=>{this.resolve_=v,this.reject_=C,this.start_()})}start_(){let t=(r,i)=>{if(i){r(!1,new rr(!1,null,!0));return}let s=this.connectionFactory_();this.pendingConnection_=s;let o=a=>{let c=a.loaded,l=a.lengthComputable?a.total:-1;this.progressCallback_!==null&&this.progressCallback_(c,l)};this.progressCallback_!==null&&s.addUploadProgressListener(o),s.send(this.url_,this.method_,this.isUsingEmulator,this.body_,this.headers_).then(()=>{this.progressCallback_!==null&&s.removeUploadProgressListener(o),this.pendingConnection_=null;let a=s.getErrorCode()===ir.NO_ERROR,c=s.getStatus();if(!a||DI(c,this.additionalRetryCodes_)&&this.retry){let u=s.getErrorCode()===ir.ABORT;r(!1,new rr(!1,null,u));return}let l=this.successCodes_.indexOf(c)!==-1;r(!0,new rr(l,s))})},e=(r,i)=>{let s=this.resolve_,o=this.reject_,a=i.connection;if(i.wasSuccessCode)try{let c=this.callback_(a,a.getResponse());wI(c)?s(c):s()}catch(c){o(c)}else if(a!==null){let c=vl();c.serverResponse=a.getErrorText(),this.errorCallback_?o(this.errorCallback_(a,c)):o(c)}else if(i.canceled){let c=this.appDelete_?Pp():hI();o(c)}else{let c=dI();o(c)}};this.canceled_?e(!1,new rr(!1,null,!0)):this.backoffId_=yI(t,e,this.timeout_)}getPromise(){return this.promise_}cancel(t){this.canceled_=!0,this.appDelete_=t||!1,this.backoffId_!==null&&EI(this.backoffId_),this.pendingConnection_!==null&&this.pendingConnection_.abort()}},rr=class{constructor(t,e,r){this.wasSuccessCode=t,this.connection=e,this.canceled=!!r}};function CI(n,t){t!==null&&t.length>0&&(n.Authorization="Firebase "+t)}function SI(n,t){n["X-Firebase-Storage-Version"]="webjs/"+(t??"AppManager")}function TI(n,t){t&&(n["X-Firebase-GMPID"]=t)}function RI(n,t){t!==null&&(n["X-Firebase-AppCheck"]=t)}function AI(n,t,e,r,i,s,o=!0,a=!1){let c=Mp(n.urlParams),l=n.url+c,u=Object.assign({},n.headers);return TI(u,t),CI(u,e),SI(u,s),RI(u,r),new ml(l,n.method,u,n.body,n.successCodes,n.additionalRetryCodes,n.handler,n.errorHandler,n.timeout,n.progressCallback,i,o,a)}function kI(){return typeof BlobBuilder<"u"?BlobBuilder:typeof WebKitBlobBuilder<"u"?WebKitBlobBuilder:void 0}function OI(...n){let t=kI();if(t!==void 0){let e=new t;for(let r=0;r<n.length;r++)e.append(n[r]);return e.getBlob()}else{if(yl())return new Blob(n);throw new z(G.UNSUPPORTED_ENVIRONMENT,"This browser doesn't seem to support creating Blobs")}}function PI(n,t,e){return n.webkitSlice?n.webkitSlice(t,e):n.mozSlice?n.mozSlice(t,e):n.slice?n.slice(t,e):null}function MI(n){if(typeof atob>"u")throw vI("base-64");return atob(n)}var Je={RAW:"raw",BASE64:"base64",BASE64URL:"base64url",DATA_URL:"data_url"},hi=class{constructor(t,e){this.data=t,this.contentType=e||null}};function NI(n,t){switch(n){case Je.RAW:return new hi(Np(t));case Je.BASE64:case Je.BASE64URL:return new hi(xp(n,t));case Je.DATA_URL:return new hi(FI(t),LI(t))}throw vl()}function Np(n){let t=[];for(let e=0;e<n.length;e++){let r=n.charCodeAt(e);if(r<=127)t.push(r);else if(r<=2047)t.push(192|r>>6,128|r&63);else if((r&64512)===55296)if(!(e<n.length-1&&(n.charCodeAt(e+1)&64512)===56320))t.push(239,191,189);else{let s=r,o=n.charCodeAt(++e);r=65536|(s&1023)<<10|o&1023,t.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|r&63)}else(r&64512)===56320?t.push(239,191,189):t.push(224|r>>12,128|r>>6&63,128|r&63)}return new Uint8Array(t)}function xI(n){let t;try{t=decodeURIComponent(n)}catch{throw di(Je.DATA_URL,"Malformed data URL.")}return Np(t)}function xp(n,t){switch(n){case Je.BASE64:{let i=t.indexOf("-")!==-1,s=t.indexOf("_")!==-1;if(i||s)throw di(n,"Invalid character '"+(i?"-":"_")+"' found: is it base64url encoded?");break}case Je.BASE64URL:{let i=t.indexOf("+")!==-1,s=t.indexOf("/")!==-1;if(i||s)throw di(n,"Invalid character '"+(i?"+":"/")+"' found: is it base64 encoded?");t=t.replace(/-/g,"+").replace(/_/g,"/");break}}let e;try{e=MI(t)}catch(i){throw i.message.includes("polyfill")?i:di(n,"Invalid character found")}let r=new Uint8Array(e.length);for(let i=0;i<e.length;i++)r[i]=e.charCodeAt(i);return r}var so=class{constructor(t){this.base64=!1,this.contentType=null;let e=t.match(/^data:([^,]+)?,/);if(e===null)throw di(Je.DATA_URL,"Must be formatted 'data:[<mediatype>][;base64],<data>");let r=e[1]||null;r!=null&&(this.base64=UI(r,";base64"),this.contentType=this.base64?r.substring(0,r.length-7):r),this.rest=t.substring(t.indexOf(",")+1)}};function FI(n){let t=new so(n);return t.base64?xp(Je.BASE64,t.rest):xI(t.rest)}function LI(n){return new so(n).contentType}function UI(n,t){return n.length>=t.length?n.substring(n.length-t.length)===t:!1}var oo=class n{constructor(t,e){let r=0,i="";Dp(t)?(this.data_=t,r=t.size,i=t.type):t instanceof ArrayBuffer?(e?this.data_=new Uint8Array(t):(this.data_=new Uint8Array(t.byteLength),this.data_.set(new Uint8Array(t))),r=this.data_.length):t instanceof Uint8Array&&(e?this.data_=t:(this.data_=new Uint8Array(t.length),this.data_.set(t)),r=t.length),this.size_=r,this.type_=i}size(){return this.size_}type(){return this.type_}slice(t,e){if(Dp(this.data_)){let r=this.data_,i=PI(r,t,e);return i===null?null:new n(i)}else{let r=new Uint8Array(this.data_.buffer,t,e-t);return new n(r,!0)}}static getBlob(...t){if(yl()){let e=t.map(r=>r instanceof n?r.data_:r);return new n(OI.apply(null,e))}else{let e=t.map(o=>bl(o)?NI(Je.RAW,o).data:o.data_),r=0;e.forEach(o=>{r+=o.byteLength});let i=new Uint8Array(r),s=0;return e.forEach(o=>{for(let a=0;a<o.length;a++)i[s++]=o[a]}),new n(i,!0)}}uploadData(){return this.data_}};function Fp(n){let t;try{t=JSON.parse(n)}catch{return null}return II(t)?t:null}function BI(n){if(n.length===0)return null;let t=n.lastIndexOf("/");return t===-1?"":n.slice(0,t)}function jI(n,t){let e=t.split("/").filter(r=>r.length>0).join("/");return n.length===0?e:n+"/"+e}function Lp(n){let t=n.lastIndexOf("/",n.length-2);return t===-1?n:n.slice(t+1)}function VI(n,t){return t}var ie=class{constructor(t,e,r,i){this.server=t,this.local=e||t,this.writable=!!r,this.xform=i||VI}},io=null;function $I(n){return!bl(n)||n.length<2?n:Lp(n)}function Up(){if(io)return io;let n=[];n.push(new ie("bucket")),n.push(new ie("generation")),n.push(new ie("metageneration")),n.push(new ie("name","fullPath",!0));function t(s,o){return $I(o)}let e=new ie("name");e.xform=t,n.push(e);function r(s,o){return o!==void 0?Number(o):o}let i=new ie("size");return i.xform=r,n.push(i),n.push(new ie("timeCreated")),n.push(new ie("updated")),n.push(new ie("md5Hash",null,!0)),n.push(new ie("cacheControl",null,!0)),n.push(new ie("contentDisposition",null,!0)),n.push(new ie("contentEncoding",null,!0)),n.push(new ie("contentLanguage",null,!0)),n.push(new ie("contentType",null,!0)),n.push(new ie("metadata","customMetadata",!0)),io=n,io}function zI(n,t){function e(){let r=n.bucket,i=n.fullPath,s=new Ue(r,i);return t._makeStorageReference(s)}Object.defineProperty(n,"ref",{get:e})}function HI(n,t,e){let r={};r.type="file";let i=e.length;for(let s=0;s<i;s++){let o=e[s];r[o.local]=o.xform(r,t[o.server])}return zI(r,n),r}function Bp(n,t,e){let r=Fp(t);return r===null?null:HI(n,r,e)}function WI(n,t,e,r){let i=Fp(t);if(i===null||!bl(i.downloadTokens))return null;let s=i.downloadTokens;if(s.length===0)return null;let o=encodeURIComponent;return s.split(",").map(l=>{let u=n.bucket,p=n.fullPath,f="/b/"+o(u)+"/o/"+o(p),m=El(f,e,r),v=Mp({alt:"media",token:l});return m+v})[0]}function GI(n,t){let e={},r=t.length;for(let i=0;i<r;i++){let s=t[i];s.writable&&(e[s.server]=n[s.local])}return JSON.stringify(e)}var ao=class{constructor(t,e,r,i){this.url=t,this.method=e,this.handler=r,this.timeout=i,this.urlParams={},this.headers={},this.body=null,this.errorHandler=null,this.progressCallback=null,this.successCodes=[200],this.additionalRetryCodes=[]}};function jp(n){if(!n)throw vl()}function qI(n,t){function e(r,i){let s=Bp(n,i,t);return jp(s!==null),s}return e}function KI(n,t){function e(r,i){let s=Bp(n,i,t);return jp(s!==null),WI(s,i,n.host,n._protocol)}return e}function Vp(n){function t(e,r){let i;return e.getStatus()===401?e.getErrorText().includes("Firebase App Check token is invalid")?i=lI():i=cI():e.getStatus()===402?i=aI(n.bucket):e.getStatus()===403?i=uI(n.path):i=r,i.status=e.getStatus(),i.serverResponse=r.serverResponse,i}return t}function YI(n){let t=Vp(n);function e(r,i){let s=t(r,i);return r.getStatus()===404&&(s=oI(n.path)),s.serverResponse=i.serverResponse,s}return e}function XI(n,t,e){let r=t.fullServerUrl(),i=El(r,n.host,n._protocol),s="GET",o=n.maxOperationRetryTime,a=new ao(i,s,KI(n,e),o);return a.errorHandler=YI(t),a}function ZI(n,t){return n&&n.contentType||t&&t.type()||"application/octet-stream"}function JI(n,t,e){let r=Object.assign({},e);return r.fullPath=n.path,r.size=t.size(),r.contentType||(r.contentType=ZI(null,t)),r}function QI(n,t,e,r,i){let s=t.bucketOnlyServerUrl(),o={"X-Goog-Upload-Protocol":"multipart"};function a(){let U="";for(let Z=0;Z<2;Z++)U=U+Math.random().toString().slice(2);return U}let c=a();o["Content-Type"]="multipart/related; boundary="+c;let l=JI(t,r,i),u=GI(l,e),p="--"+c+`\r
Content-Type: application/json; charset=utf-8\r
\r
`+u+`\r
--`+c+`\r
Content-Type: `+l.contentType+`\r
\r
`,f=`\r
--`+c+"--",m=oo.getBlob(p,r,f);if(m===null)throw gI();let v={name:l.fullPath},C=El(s,n.host,n._protocol),R="POST",X=n.maxUploadRetryTime,H=new ao(C,R,qI(n,e),X);return H.urlParams=v,H.headers=o,H.body=m.uploadData(),H.errorHandler=Vp(t),H}var px=256*1024;var Sp=null,gl=class{constructor(){this.sent_=!1,this.xhr_=new XMLHttpRequest,this.initXhr(),this.errorCode_=ir.NO_ERROR,this.sendPromise_=new Promise(t=>{this.xhr_.addEventListener("abort",()=>{this.errorCode_=ir.ABORT,t()}),this.xhr_.addEventListener("error",()=>{this.errorCode_=ir.NETWORK_ERROR,t()}),this.xhr_.addEventListener("load",()=>{t()})})}send(t,e,r,i,s){if(this.sent_)throw ui("cannot .send() more than once");if(qe(t)&&r&&(this.xhr_.withCredentials=!0),this.sent_=!0,this.xhr_.open(e,t,!0),s!==void 0)for(let o in s)s.hasOwnProperty(o)&&this.xhr_.setRequestHeader(o,s[o].toString());return i!==void 0?this.xhr_.send(i):this.xhr_.send(),this.sendPromise_}getErrorCode(){if(!this.sent_)throw ui("cannot .getErrorCode() before sending");return this.errorCode_}getStatus(){if(!this.sent_)throw ui("cannot .getStatus() before sending");try{return this.xhr_.status}catch{return-1}}getResponse(){if(!this.sent_)throw ui("cannot .getResponse() before sending");return this.xhr_.response}getErrorText(){if(!this.sent_)throw ui("cannot .getErrorText() before sending");return this.xhr_.statusText}abort(){this.xhr_.abort()}getResponseHeader(t){return this.xhr_.getResponseHeader(t)}addUploadProgressListener(t){this.xhr_.upload!=null&&this.xhr_.upload.addEventListener("progress",t)}removeUploadProgressListener(t){this.xhr_.upload!=null&&this.xhr_.upload.removeEventListener("progress",t)}},_l=class extends gl{initXhr(){this.xhr_.responseType="text"}};function $p(){return Sp?Sp():new _l}var sr=class n{constructor(t,e){this._service=t,e instanceof Ue?this._location=e:this._location=Ue.makeFromUrl(e,t.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(t,e){return new n(t,e)}get root(){let t=new Ue(this._location.bucket,"");return this._newRef(this._service,t)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return Lp(this._location.path)}get storage(){return this._service}get parent(){let t=BI(this._location.path);if(t===null)return null;let e=new Ue(this._location.bucket,t);return new n(this._service,e)}_throwIfRoot(t){if(this._location.path==="")throw bI(t)}};function e0(n,t,e){n._throwIfRoot("uploadBytes");let r=QI(n.storage,n._location,Up(),new oo(t,!0),e);return n.storage.makeRequestWithTokens(r,$p).then(i=>({metadata:i,ref:n}))}function t0(n){n._throwIfRoot("getDownloadURL");let t=XI(n.storage,n._location,Up());return n.storage.makeRequestWithTokens(t,$p).then(e=>{if(e===null)throw _I();return e})}function n0(n,t){let e=jI(n._location.path,t),r=new Ue(n._location.bucket,e);return new sr(n.storage,r)}function r0(n){return/^[A-Za-z]+:\/\//.test(n)}function i0(n,t){return new sr(n,t)}function zp(n,t){if(n instanceof fi){let e=n;if(e._bucket==null)throw mI();let r=new sr(e,e._bucket);return t!=null?zp(r,t):r}else return t!==void 0?n0(n,t):n}function s0(n,t){if(t&&r0(t)){if(n instanceof fi)return i0(n,t);throw fl("To use ref(service, url), the first argument must be a Storage instance.")}else return zp(n,t)}function Tp(n,t){let e=t?.[Op];return e==null?null:Ue.makeFromBucketSpec(e,n)}function o0(n,t,e,r={}){n.host=`${t}:${e}`;let i=qe(t);i&&(Qn(`https://${n.host}/b`),er("Storage",!0)),n._isUsingEmulator=!0,n._protocol=i?"https":"http";let{mockUserToken:s}=r;s&&(n._overrideAuthToken=typeof s=="string"?s:Zh(s,n.app.options.projectId))}var fi=class{constructor(t,e,r,i,s,o=!1){this.app=t,this._authProvider=e,this._appCheckProvider=r,this._url=i,this._firebaseVersion=s,this._isUsingEmulator=o,this._bucket=null,this._host=kp,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=iI,this._maxUploadRetryTime=sI,this._requests=new Set,i!=null?this._bucket=Ue.makeFromBucketSpec(i,this._host):this._bucket=Tp(this._host,this.app.options)}get host(){return this._host}set host(t){this._host=t,this._url!=null?this._bucket=Ue.makeFromBucketSpec(this._url,t):this._bucket=Tp(t,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(t){Cp("time",0,Number.POSITIVE_INFINITY,t),this._maxUploadRetryTime=t}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(t){Cp("time",0,Number.POSITIVE_INFINITY,t),this._maxOperationRetryTime=t}_getAuthToken(){return h(this,null,function*(){if(this._overrideAuthToken)return this._overrideAuthToken;let t=this._authProvider.getImmediate({optional:!0});if(t){let e=yield t.getToken();if(e!==null)return e.accessToken}return null})}_getAppCheckToken(){return h(this,null,function*(){if(pe(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let t=this._appCheckProvider.getImmediate({optional:!0});return t?(yield t.getToken()).token:null})}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(t=>t.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(t){return new sr(this,t)}_makeRequest(t,e,r,i,s=!0){if(this._deleted)return new pl(Pp());{let o=AI(t,this._appId,r,i,e,this._firebaseVersion,s,this._isUsingEmulator);return this._requests.add(o),o.getPromise().then(()=>this._requests.delete(o),()=>this._requests.delete(o)),o}}makeRequestWithTokens(t,e){return h(this,null,function*(){let[r,i]=yield Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(t,e,r,i).getPromise()})}},Rp="@firebase/storage",Ap="0.13.13";var Hp="storage";function Wp(n,t,e){return n=ne(n),e0(n,t,e)}function Gp(n){return n=ne(n),t0(n)}function qp(n,t){return n=ne(n),s0(n,t)}function Kp(n=gt(),t){n=ne(n);let r=Nt(n,Hp).getImmediate({identifier:t}),i=Os("storage");return i&&Yp(r,...i),r}function Yp(n,t,e,r={}){o0(n,t,e,r)}function a0(n,{instanceIdentifier:t}){let e=n.getProvider("app").getImmediate(),r=n.getProvider("auth-internal"),i=n.getProvider("app-check-internal");return new fi(e,r,i,t,xt)}function c0(){Ce(new re(Hp,a0,"PUBLIC").setMultipleInstances(!0)),L(Rp,Ap,""),L(Rp,Ap,"esm2017")}c0();var In=class{constructor(t){return t}},Xp="storage",wl=class{constructor(){return Ft(Xp)}};var Il=new y("angularfire2.storage-instances");function l0(n,t){let e=gn(Xp,n,t);return e&&new In(e)}function u0(n){return(t,e)=>{let r=t.runOutsideAngular(()=>n(e));return new In(r)}}var d0={provide:wl,deps:[[new q,Il]]},h0={provide:In,useFactory:l0,deps:[[new q,Il],Fe]};function Lx(n,...t){return L("angularfire",Ye.full,"gcs"),Te([h0,d0,{provide:Il,useFactory:u0(n),multi:!0,deps:[D,k,Xe,_t,[new q,wn],[new q,Ut],...t]}])}var Zp=_e(Gp,!0);var Ux=_e(Kp,!0);var Jp=_e(qp,!0,2);var Qp=_e(Wp,!0);var f0="type.googleapis.com/google.protobuf.Int64Value",p0="type.googleapis.com/google.protobuf.UInt64Value";function rm(n,t){let e={};for(let r in n)n.hasOwnProperty(r)&&(e[r]=t(n[r]));return e}function co(n){if(n==null)return null;if(n instanceof Number&&(n=n.valueOf()),typeof n=="number"&&isFinite(n)||n===!0||n===!1||Object.prototype.toString.call(n)==="[object String]")return n;if(n instanceof Date)return n.toISOString();if(Array.isArray(n))return n.map(t=>co(t));if(typeof n=="function"||typeof n=="object")return rm(n,t=>co(t));throw new Error("Data cannot be encoded in JSON: "+n)}function or(n){if(n==null)return n;if(n["@type"])switch(n["@type"]){case f0:case p0:{let t=Number(n.value);if(isNaN(t))throw new Error("Data cannot be decoded from JSON: "+n);return t}default:throw new Error("Data cannot be decoded from JSON: "+n)}return Array.isArray(n)?n.map(t=>or(t)):typeof n=="function"||typeof n=="object"?rm(n,t=>or(t)):n}var Tl="functions";var em={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"},ve=class n extends fe{constructor(t,e,r){super(`${Tl}/${t}`,e||""),this.details=r,Object.setPrototypeOf(this,n.prototype)}};function m0(n){if(n>=200&&n<300)return"ok";switch(n){case 0:return"internal";case 400:return"invalid-argument";case 401:return"unauthenticated";case 403:return"permission-denied";case 404:return"not-found";case 409:return"aborted";case 429:return"resource-exhausted";case 499:return"cancelled";case 500:return"internal";case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline-exceeded"}return"unknown"}function lo(n,t){let e=m0(n),r=e,i;try{let s=t&&t.error;if(s){let o=s.status;if(typeof o=="string"){if(!em[o])return new ve("internal","internal");e=em[o],r=o}let a=s.message;typeof a=="string"&&(r=a),i=s.details,i!==void 0&&(i=or(i))}}catch{}return e==="ok"?null:new ve(e,r,i)}var Dl=class{constructor(t,e,r,i){this.app=t,this.auth=null,this.messaging=null,this.appCheck=null,this.serverAppAppCheckToken=null,pe(t)&&t.settings.appCheckToken&&(this.serverAppAppCheckToken=t.settings.appCheckToken),this.auth=e.getImmediate({optional:!0}),this.messaging=r.getImmediate({optional:!0}),this.auth||e.get().then(s=>this.auth=s,()=>{}),this.messaging||r.get().then(s=>this.messaging=s,()=>{}),this.appCheck||i?.get().then(s=>this.appCheck=s,()=>{})}getAuthToken(){return h(this,null,function*(){if(this.auth)try{let t=yield this.auth.getToken();return t?.accessToken}catch{return}})}getMessagingToken(){return h(this,null,function*(){if(!(!this.messaging||!("Notification"in self)||Notification.permission!=="granted"))try{return yield this.messaging.getToken()}catch{return}})}getAppCheckToken(t){return h(this,null,function*(){if(this.serverAppAppCheckToken)return this.serverAppAppCheckToken;if(this.appCheck){let e=t?yield this.appCheck.getLimitedUseToken():yield this.appCheck.getToken();return e.error?null:e.token}return null})}getContext(t){return h(this,null,function*(){let e=yield this.getAuthToken(),r=yield this.getMessagingToken(),i=yield this.getAppCheckToken(t);return{authToken:e,messagingToken:r,appCheckToken:i}})}};var Cl="us-central1",g0=/^data: (.*?)(?:\n|$)/;function _0(n){let t=null;return{promise:new Promise((e,r)=>{t=setTimeout(()=>{r(new ve("deadline-exceeded","deadline-exceeded"))},n)}),cancel:()=>{t&&clearTimeout(t)}}}var Sl=class{constructor(t,e,r,i,s=Cl,o=(...a)=>fetch(...a)){this.app=t,this.fetchImpl=o,this.emulatorOrigin=null,this.contextProvider=new Dl(t,e,r,i),this.cancelAllRequests=new Promise(a=>{this.deleteService=()=>Promise.resolve(a())});try{let a=new URL(s);this.customDomain=a.origin+(a.pathname==="/"?"":a.pathname),this.region=Cl}catch{this.customDomain=null,this.region=s}}_delete(){return this.deleteService()}_url(t){let e=this.app.options.projectId;return this.emulatorOrigin!==null?`${this.emulatorOrigin}/${e}/${this.region}/${t}`:this.customDomain!==null?`${this.customDomain}/${t}`:`https://${this.region}-${e}.cloudfunctions.net/${t}`}};function v0(n,t,e){let r=qe(t);n.emulatorOrigin=`http${r?"s":""}://${t}:${e}`,r&&(Qn(n.emulatorOrigin),er("Functions",!0))}function b0(n,t,e){let r=i=>E0(n,t,i,e||{});return r.stream=(i,s)=>I0(n,t,i,s),r}function y0(n,t,e,r){return h(this,null,function*(){e["Content-Type"]="application/json";let i;try{i=yield r(n,{method:"POST",body:JSON.stringify(t),headers:e})}catch{return{status:0,json:null}}let s=null;try{s=yield i.json()}catch{}return{status:i.status,json:s}})}function im(n,t){return h(this,null,function*(){let e={},r=yield n.contextProvider.getContext(t.limitedUseAppCheckTokens);return r.authToken&&(e.Authorization="Bearer "+r.authToken),r.messagingToken&&(e["Firebase-Instance-ID-Token"]=r.messagingToken),r.appCheckToken!==null&&(e["X-Firebase-AppCheck"]=r.appCheckToken),e})}function E0(n,t,e,r){let i=n._url(t);return w0(n,i,e,r)}function w0(n,t,e,r){return h(this,null,function*(){e=co(e);let i={data:e},s=yield im(n,r),o=r.timeout||7e4,a=_0(o),c=yield Promise.race([y0(t,i,s,n.fetchImpl),a.promise,n.cancelAllRequests]);if(a.cancel(),!c)throw new ve("cancelled","Firebase Functions instance was deleted.");let l=lo(c.status,c.json);if(l)throw l;if(!c.json)throw new ve("internal","Response is not valid JSON object.");let u=c.json.data;if(typeof u>"u"&&(u=c.json.result),typeof u>"u")throw new ve("internal","Response is missing data field.");return{data:or(u)}})}function I0(n,t,e,r){let i=n._url(t);return D0(n,i,e,r||{})}function D0(n,t,e,r){return h(this,null,function*(){var i;e=co(e);let s={data:e},o=yield im(n,r);o["Content-Type"]="application/json",o.Accept="text/event-stream";let a;try{a=yield n.fetchImpl(t,{method:"POST",body:JSON.stringify(s),headers:o,signal:r?.signal})}catch(m){if(m instanceof Error&&m.name==="AbortError"){let C=new ve("cancelled","Request was cancelled.");return{data:Promise.reject(C),stream:{[Symbol.asyncIterator](){return{next(){return Promise.reject(C)}}}}}}let v=lo(0,null);return{data:Promise.reject(v),stream:{[Symbol.asyncIterator](){return{next(){return Promise.reject(v)}}}}}}let c,l,u=new Promise((m,v)=>{c=m,l=v});(i=r?.signal)===null||i===void 0||i.addEventListener("abort",()=>{let m=new ve("cancelled","Request was cancelled.");l(m)});let p=a.body.getReader(),f=C0(p,c,l,r?.signal);return{stream:{[Symbol.asyncIterator](){let m=f.getReader();return{next(){return h(this,null,function*(){let{value:C,done:R}=yield m.read();return{value:C,done:R}})},return(){return h(this,null,function*(){return yield m.cancel(),{done:!0,value:void 0}})}}}},data:u}})}function C0(n,t,e,r){let i=(o,a)=>{let c=o.match(g0);if(!c)return;let l=c[1];try{let u=JSON.parse(l);if("result"in u){t(or(u.result));return}if("message"in u){a.enqueue(or(u.message));return}if("error"in u){let p=lo(0,u);a.error(p),e(p);return}}catch(u){if(u instanceof ve){a.error(u),e(u);return}}},s=new TextDecoder;return new ReadableStream({start(o){let a="";return c();function c(){return h(this,null,function*(){if(r?.aborted){let l=new ve("cancelled","Request was cancelled");return o.error(l),e(l),Promise.resolve()}try{let{value:l,done:u}=yield n.read();if(u){a.trim()&&i(a.trim(),o),o.close();return}if(r?.aborted){let f=new ve("cancelled","Request was cancelled");o.error(f),e(f),yield n.cancel();return}a+=s.decode(l,{stream:!0});let p=a.split(`
`);a=p.pop()||"";for(let f of p)f.trim()&&i(f.trim(),o);return c()}catch(l){let u=l instanceof ve?l:lo(0,null);o.error(u),e(u)}})}},cancel(){return n.cancel()}})}var tm="@firebase/functions",nm="0.12.8";var S0="auth-internal",T0="app-check-internal",R0="messaging-internal";function A0(n){let t=(e,{instanceIdentifier:r})=>{let i=e.getProvider("app").getImmediate(),s=e.getProvider(S0),o=e.getProvider(R0),a=e.getProvider(T0);return new Sl(i,s,o,a,r)};Ce(new re(Tl,t,"PUBLIC").setMultipleInstances(!0)),L(tm,nm,n),L(tm,nm,"esm2017")}function sm(n=gt(),t=Cl){let r=Nt(ne(n),Tl).getImmediate({identifier:t}),i=Os("functions");return i&&om(r,...i),r}function om(n,t,e){v0(ne(n),t,e)}function am(n,t,e){return b0(ne(n),t,e)}A0();var Dn=class{constructor(t){return t}},cm="functions",Rl=class{constructor(){return Ft(cm)}};var Al=new y("angularfire2.functions-instances");function k0(n,t){let e=gn(cm,n,t);return e&&new Dn(e)}function O0(n){return(t,e)=>{let r=t.runOutsideAngular(()=>n(e));return new Dn(r)}}var P0={provide:Rl,deps:[[new q,Al]]},M0={provide:Dn,useFactory:k0,deps:[[new q,Al],Fe]};function rF(n,...t){return L("angularfire",Ye.full,"fn"),Te([M0,P0,{provide:Al,useFactory:O0(n),multi:!0,deps:[D,k,Xe,_t,[new q,wn],[new q,Ut],...t]}])}var iF=_e(sm,!0),uo=_e(am,!0);var se=[];for(let n=0;n<256;++n)se.push((n+256).toString(16).slice(1));function lm(n,t=0){return(se[n[t+0]]+se[n[t+1]]+se[n[t+2]]+se[n[t+3]]+"-"+se[n[t+4]]+se[n[t+5]]+"-"+se[n[t+6]]+se[n[t+7]]+"-"+se[n[t+8]]+se[n[t+9]]+"-"+se[n[t+10]]+se[n[t+11]]+se[n[t+12]]+se[n[t+13]]+se[n[t+14]]+se[n[t+15]]).toLowerCase()}var kl,N0=new Uint8Array(16);function Ol(){if(!kl){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");kl=crypto.getRandomValues.bind(crypto)}return kl(N0)}var x0=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Pl={randomUUID:x0};function F0(n,t,e){if(Pl.randomUUID&&!t&&!n)return Pl.randomUUID();n=n||{};let r=n.random??n.rng?.()??Ol();if(r.length<16)throw new Error("Random bytes length must be >= 16");if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,t){if(e=e||0,e<0||e+16>t.length)throw new RangeError(`UUID byte range ${e}:${e+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[e+i]=r[i];return t}return lm(r)}var Ml=F0;var um=class n{auth=d(En);storage=d(In);functions=d(Dn);currentUser$=new oe(null);constructor(){this.auth.onAuthStateChanged(t=>{this.currentUser$.next(t)})}signInAnonymously(){return h(this,null,function*(){return(yield Ip(this.auth)).user})}getCurrentUser(){return this.currentUser$.asObservable()}uploadPigeonImage(t,e,r){return h(this,null,function*(){let i=this.auth.currentUser;if(!i)throw new Error("User must be authenticated to upload files");let s=Ml(),o=i.uid,a=`${e}_${r}_${t.name}`,c=`shots/${o}/${s}/${a}`,l=Jp(this.storage,c),u=yield Qp(l,t),p=yield Zp(u.ref);return{captureId:s,storageFilePath:c,downloadUrl:p}})}getAnalysisJobStatus(t){let e=uo(this.functions,"getAnalysisJobStatus");return ee(e({captureId:t})).pipe(E(r=>r.data))}getPigeonByCaptureId(t){let e=uo(this.functions,"getPigeonByCaptureId");return ee(e({captureId:t})).pipe(E(r=>r.data))}getTrainerPigeondex(t,e){let r=uo(this.functions,"getTrainerPigeondex");return ee(r({limit:t,offset:e})).pipe(E(i=>i.data))}static \u0275fac=function(e){return new(e||n)};static \u0275prov=_({token:n,factory:n.\u0275fac,providedIn:"root"})};export{ct as a,_m as b,vm as c,Em as d,Tu as e,Ko as f,Um as g,wa as h,is as i,R_ as j,O_ as k,Os as l,qe as m,Qn as n,Zh as o,er as p,fe as q,Ot as r,re as s,F as t,Pt as u,Ce as v,Nt as w,pe as x,xt as y,gt as z,L as A,Ye as B,gn as C,Ft as D,Xe as E,_e as F,Fe as G,_t as H,LM as I,UM as J,Ut as K,wn as L,ax as M,cx as N,Lx as O,Ux as P,rF as Q,iF as R,$ as S,Wn as T,Ud as U,We as V,Oa as W,Ge as X,Wd as Y,Kd as Z,Ma as _,V_ as $,Hr as aa,us as ba,xa as ca,Fa as da,z_ as ea,H_ as fa,pA as ga,CA as ha,ft as ia,FA as ja,$a as ka,XA as la,dh as ma,hh as na,ev as oa,ps as pa,Ae as qa,gh as ra,vh as sa,bh as ta,Zk as ua,Jk as va,Yn as wa,bs as xa,Zn as ya,Ka as za,Ts as Aa,Qa as Ba,Xr as Ca,Ja as Da,wv as Ea,ec as Fa,xv as Ga,LP as Ha,um as Ia};
