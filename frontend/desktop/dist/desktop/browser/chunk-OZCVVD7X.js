var fp=Object.defineProperty,pp=Object.defineProperties;var hp=Object.getOwnPropertyDescriptors;var lr=Object.getOwnPropertySymbols,gp=Object.getPrototypeOf,Tc=Object.prototype.hasOwnProperty,_c=Object.prototype.propertyIsEnumerable,mp=Reflect.get;var bc=(e,t,n)=>t in e?fp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,q=(e,t)=>{for(var n in t||={})Tc.call(t,n)&&bc(e,n,t[n]);if(lr)for(var n of lr(t))_c.call(t,n)&&bc(e,n,t[n]);return e},W=(e,t)=>pp(e,hp(t));var TE=(e,t)=>{var n={};for(var r in e)Tc.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&lr)for(var r of lr(e))t.indexOf(r)<0&&_c.call(e,r)&&(n[r]=e[r]);return n};var _E=(e,t,n)=>mp(gp(e),n,t);var Mc=(e,t,n)=>new Promise((r,o)=>{var i=c=>{try{a(n.next(c))}catch(l){o(l)}},s=c=>{try{a(n.throw(c))}catch(l){o(l)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);a((n=n.apply(e,t)).next())});function C(e){return typeof e=="function"}function Rt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var ur=Rt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function nt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var L=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(C(r))try{r()}catch(i){t=i instanceof ur?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Nc(i)}catch(s){t=t??[],s instanceof ur?t=[...t,...s.errors]:t.push(s)}}if(t)throw new ur(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Nc(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&nt(n,t)}remove(t){let{_finalizers:n}=this;n&&nt(n,t),t instanceof e&&t._removeParent(this)}};L.EMPTY=(()=>{let e=new L;return e.closed=!0,e})();var ci=L.EMPTY;function dr(e){return e instanceof L||e&&"closed"in e&&C(e.remove)&&C(e.add)&&C(e.unsubscribe)}function Nc(e){C(e)?e():e.unsubscribe()}var le={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Ot={setTimeout(e,t,...n){let{delegate:r}=Ot;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Ot;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function fr(e){Ot.setTimeout(()=>{let{onUnhandledError:t}=le;if(t)t(e);else throw e})}function ln(){}var xc=li("C",void 0,void 0);function Sc(e){return li("E",void 0,e)}function Rc(e){return li("N",e,void 0)}function li(e,t,n){return{kind:e,value:t,error:n}}var rt=null;function At(e){if(le.useDeprecatedSynchronousErrorHandling){let t=!rt;if(t&&(rt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=rt;if(rt=null,n)throw r}}else e()}function Oc(e){le.useDeprecatedSynchronousErrorHandling&&rt&&(rt.errorThrown=!0,rt.error=e)}var ot=class extends L{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,dr(t)&&t.add(this)):this.destination=Ip}static create(t,n,r){return new Ne(t,n,r)}next(t){this.isStopped?di(Rc(t),this):this._next(t)}error(t){this.isStopped?di(Sc(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?di(xc,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},yp=Function.prototype.bind;function ui(e,t){return yp.call(e,t)}var fi=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){pr(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){pr(r)}else pr(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){pr(n)}}},Ne=class extends ot{constructor(t,n,r){super();let o;if(C(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&le.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&ui(t.next,i),error:t.error&&ui(t.error,i),complete:t.complete&&ui(t.complete,i)}):o=t}this.destination=new fi(o)}};function pr(e){le.useDeprecatedSynchronousErrorHandling?Oc(e):fr(e)}function vp(e){throw e}function di(e,t){let{onStoppedNotification:n}=le;n&&Ot.setTimeout(()=>n(e,t))}var Ip={closed:!0,next:ln,error:vp,complete:ln};var kt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Q(e){return e}function Ep(...e){return pi(e)}function pi(e){return e.length===0?Q:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var _=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=wp(n)?n:new Ne(n,r,o);return At(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Ac(r),new r((o,i)=>{let s=new Ne({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[kt](){return this}pipe(...n){return pi(n)(this)}toPromise(n){return n=Ac(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Ac(e){var t;return(t=e??le.Promise)!==null&&t!==void 0?t:Promise}function Dp(e){return e&&C(e.next)&&C(e.error)&&C(e.complete)}function wp(e){return e&&e instanceof ot||Dp(e)&&dr(e)}var kc=Rt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var re=(()=>{class e extends _{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new hr(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new kc}next(n){At(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){At(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){At(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ci:(this.currentObservers=null,i.push(n),new L(()=>{this.currentObservers=null,nt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new _;return n.source=this,n}}return e.create=(t,n)=>new hr(t,n),e})(),hr=class extends re{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:ci}};function hi(e){return C(e?.lift)}function D(e){return t=>{if(hi(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function v(e,t,n,r,o){return new gi(e,t,n,r,o)}var gi=class extends ot{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function TD(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Lc(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(d){s(d)}}function c(u){try{l(r.throw(u))}catch(d){s(d)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function Pc(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function it(e){return this instanceof it?(this.v=e,this):new it(e)}function Fc(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(g){return new Promise(function(N,b){i.push([f,g,N,b])>1||c(f,g)})},h&&(o[f]=h(o[f])))}function c(f,h){try{l(r[f](h))}catch(g){p(i[0][3],g)}}function l(f){f.value instanceof it?Promise.resolve(f.value.v).then(u,d):p(i[0][2],f)}function u(f){c("next",f)}function d(f){c("throw",f)}function p(f,h){f(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function jc(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Pc=="function"?Pc(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var gr=e=>e&&typeof e.length=="number"&&typeof e!="function";function mr(e){return C(e?.then)}function yr(e){return C(e[kt])}function vr(e){return Symbol.asyncIterator&&C(e?.[Symbol.asyncIterator])}function Ir(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Cp(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Er=Cp();function Dr(e){return C(e?.[Er])}function wr(e){return Fc(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield it(n.read());if(o)return yield it(void 0);yield yield it(r)}}finally{n.releaseLock()}})}function Cr(e){return C(e?.getReader)}function R(e){if(e instanceof _)return e;if(e!=null){if(yr(e))return bp(e);if(gr(e))return Tp(e);if(mr(e))return _p(e);if(vr(e))return Vc(e);if(Dr(e))return Mp(e);if(Cr(e))return Np(e)}throw Ir(e)}function bp(e){return new _(t=>{let n=e[kt]();if(C(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Tp(e){return new _(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function _p(e){return new _(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,fr)})}function Mp(e){return new _(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Vc(e){return new _(t=>{xp(e,t).catch(n=>t.error(n))})}function Np(e){return Vc(wr(e))}function xp(e,t){var n,r,o,i;return Lc(this,void 0,void 0,function*(){try{for(n=jc(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Hc(e){return D((t,n)=>{R(e).subscribe(v(n,()=>n.complete(),ln)),!n.closed&&t.subscribe(n)})}function mi(){return D((e,t)=>{let n=null;e._refCount++;let r=v(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var yi=class extends _{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,hi(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new L;let n=this.getSubject();t.add(this.source.subscribe(v(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=L.EMPTY)}return t}refCount(){return mi()(this)}};var un=class extends re{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var dn={now(){return(dn.delegate||Date).now()},delegate:void 0};var fn=class extends re{constructor(t=1/0,n=1/0,r=dn){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var br=class extends L{constructor(t,n){super()}schedule(t,n=0){return this}};var pn={setInterval(e,t,...n){let{delegate:r}=pn;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=pn;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Pt=class extends br{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return pn.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&pn.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,nt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Lt=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};Lt.now=dn.now;var Ft=class extends Lt{constructor(t,n=Lt.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var jt=new Ft(Pt),vi=jt;var Tr=class extends Pt{constructor(t,n){super(t,n),this.scheduler=t,this.work=n}schedule(t,n=0){return n>0?super.schedule(t,n):(this.delay=n,this.state=t,this.scheduler.flush(this),this)}execute(t,n){return n>0||this.closed?super.execute(t,n):this._execute(t,n)}requestAsyncId(t,n,r=0){return r!=null&&r>0||r==null&&this.delay>0?super.requestAsyncId(t,n,r):(t.flush(this),0)}};var _r=class extends Ft{};var Sp=new _r(Tr);var st=new _(e=>e.complete());function Mr(e){return e&&C(e.schedule)}function Ii(e){return e[e.length-1]}function Nr(e){return C(Ii(e))?e.pop():void 0}function ve(e){return Mr(Ii(e))?e.pop():void 0}function Bc(e,t){return typeof Ii(e)=="number"?e.pop():t}function X(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function hn(e,t=0){return D((n,r)=>{n.subscribe(v(r,o=>X(r,e,()=>r.next(o),t),()=>X(r,e,()=>r.complete(),t),o=>X(r,e,()=>r.error(o),t)))})}function gn(e,t=0){return D((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function $c(e,t){return R(e).pipe(gn(t),hn(t))}function Uc(e,t){return R(e).pipe(gn(t),hn(t))}function qc(e,t){return new _(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Wc(e,t){return new _(n=>{let r;return X(n,t,()=>{r=e[Er](),X(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>C(r?.return)&&r.return()})}function xr(e,t){if(!e)throw new Error("Iterable cannot be null");return new _(n=>{X(n,t,()=>{let r=e[Symbol.asyncIterator]();X(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Gc(e,t){return xr(wr(e),t)}function zc(e,t){if(e!=null){if(yr(e))return $c(e,t);if(gr(e))return qc(e,t);if(mr(e))return Uc(e,t);if(vr(e))return xr(e,t);if(Dr(e))return Wc(e,t);if(Cr(e))return Gc(e,t)}throw Ir(e)}function Ie(e,t){return t?zc(e,t):R(e)}function Rp(...e){let t=ve(e);return Ie(e,t)}function Op(e,t){let n=C(e)?e:()=>e,r=o=>o.error(n());return new _(t?o=>t.schedule(r,0,o):r)}function Ap(e){return!!e&&(e instanceof _||C(e.lift)&&C(e.subscribe))}var at=Rt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Qc(e){return e instanceof Date&&!isNaN(e)}function He(e,t){return D((n,r)=>{let o=0;n.subscribe(v(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:kp}=Array;function Pp(e,t){return kp(t)?e(...t):e(t)}function Sr(e){return He(t=>Pp(e,t))}var{isArray:Lp}=Array,{getPrototypeOf:Fp,prototype:jp,keys:Vp}=Object;function Rr(e){if(e.length===1){let t=e[0];if(Lp(t))return{args:t,keys:null};if(Hp(t)){let n=Vp(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Hp(e){return e&&typeof e=="object"&&Fp(e)===jp}function Or(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Bp(...e){let t=ve(e),n=Nr(e),{args:r,keys:o}=Rr(e);if(r.length===0)return Ie([],t);let i=new _($p(r,t,o?s=>Or(o,s):Q));return n?i.pipe(Sr(n)):i}function $p(e,t,n=Q){return r=>{Zc(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Zc(t,()=>{let l=Ie(e[c],t),u=!1;l.subscribe(v(r,d=>{i[c]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Zc(e,t,n){e?X(n,e,t):t()}function Yc(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,d=!1,p=()=>{d&&!c.length&&!l&&t.complete()},f=g=>l<r?h(g):c.push(g),h=g=>{i&&t.next(g),l++;let N=!1;R(n(g,u++)).subscribe(v(t,b=>{o?.(b),i?f(b):t.next(b)},()=>{N=!0},void 0,()=>{if(N)try{for(l--;c.length&&l<r;){let b=c.shift();s?X(t,s,()=>h(b)):h(b)}p()}catch(b){t.error(b)}}))};return e.subscribe(v(t,f,()=>{d=!0,p()})),()=>{a?.()}}function ct(e,t,n=1/0){return C(t)?ct((r,o)=>He((i,s)=>t(r,i,o,s))(R(e(r,o))),n):(typeof t=="number"&&(n=t),D((r,o)=>Yc(r,o,e,n)))}function mn(e=1/0){return ct(Q,e)}function Jc(){return mn(1)}function Ar(...e){return Jc()(Ie(e,ve(e)))}function Up(e){return new _(t=>{R(e()).subscribe(t)})}function qp(...e){let t=Nr(e),{args:n,keys:r}=Rr(e),o=new _(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let d=!1;R(n[u]).subscribe(v(i,p=>{d||(d=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!d)&&(l||i.next(r?Or(r,a):a),i.complete())}))}});return t?o.pipe(Sr(t)):o}function Ei(e=0,t,n=vi){let r=-1;return t!=null&&(Mr(t)?n=t:r=t),new _(o=>{let i=Qc(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Wp(...e){let t=ve(e),n=Bc(e,1/0),r=e;return r.length?r.length===1?R(r[0]):mn(n)(Ie(r,t)):st}function lt(e,t){return D((n,r)=>{let o=0;n.subscribe(v(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Kc(e){return D((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let l=o;o=null,n.next(l)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(v(n,l=>{r=!0,o=l,i||R(e(l)).subscribe(i=v(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Gp(e,t=jt){return Kc(()=>Ei(e,t))}function Di(e){return D((t,n)=>{let r=null,o=!1,i;r=t.subscribe(v(n,void 0,void 0,s=>{i=R(e(s,Di(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Xc(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(v(s,u=>{let d=l++;c=a?e(c,u,d):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function zp(e,t){return C(t)?ct(e,t,1):ct(e,1)}function Qp(e,t=jt){return D((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let l=i;i=null,r.next(l)}};function c(){let l=s+e,u=t.now();if(u<l){o=this.schedule(void 0,l-u),r.add(o);return}a()}n.subscribe(v(r,l=>{i=l,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function yn(e){return D((t,n)=>{let r=!1;t.subscribe(v(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function wi(e){return e<=0?()=>st:D((t,n)=>{let r=0;t.subscribe(v(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Zp(e,t=Q){return e=e??Yp,D((n,r)=>{let o,i=!0;n.subscribe(v(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function Yp(e,t){return e===t}function kr(e=Jp){return D((t,n)=>{let r=!1;t.subscribe(v(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Jp(){return new at}function Kp(e){return D((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Xp(e,t){let n=arguments.length>=2;return r=>r.pipe(e?lt((o,i)=>e(o,i,r)):Q,wi(1),n?yn(t):kr(()=>new at))}function Ci(e){return e<=0?()=>st:D((t,n)=>{let r=[];t.subscribe(v(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function eh(e,t){let n=arguments.length>=2;return r=>r.pipe(e?lt((o,i)=>e(o,i,r)):Q,Ci(1),n?yn(t):kr(()=>new at))}function th(){return D((e,t)=>{let n,r=!1;e.subscribe(v(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function nh(e,t){return D(Xc(e,t,arguments.length>=2,!0))}function Ti(e={}){let{connector:t=()=>new re,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,l=0,u=!1,d=!1,p=()=>{a?.unsubscribe(),a=void 0},f=()=>{p(),s=c=void 0,u=d=!1},h=()=>{let g=s;f(),g?.unsubscribe()};return D((g,N)=>{l++,!d&&!u&&p();let b=c=c??t();N.add(()=>{l--,l===0&&!d&&!u&&(a=bi(h,o))}),b.subscribe(N),!s&&l>0&&(s=new Ne({next:ye=>b.next(ye),error:ye=>{d=!0,p(),a=bi(f,n,ye),b.error(ye)},complete:()=>{u=!0,p(),a=bi(f,r),b.complete()}}),R(g).subscribe(s))})(i)}}function bi(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Ne({next:()=>{r.unsubscribe(),e()}});return R(t(...n)).subscribe(r)}function rh(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,Ti({connector:()=>new fn(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function oh(e){return lt((t,n)=>e<=n)}function ih(...e){let t=ve(e);return D((n,r)=>{(t?Ar(e,n,t):Ar(e,n)).subscribe(r)})}function el(e,t){return D((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(v(r,c=>{o?.unsubscribe();let l=0,u=i++;R(e(c,u)).subscribe(o=v(r,d=>r.next(t?t(c,d,u,l++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function tl(e,t=!1){return D((n,r)=>{let o=0;n.subscribe(v(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function sh(e,t,n){let r=C(e)||t||n?{next:e,error:t,complete:n}:e;return r?D((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(v(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):Q}var _i;function Mi(){return _i}function xe(e){let t=_i;return _i=e,t}var ah=Symbol("NotFound"),Pr=class extends Error{name="\u0275NotFound";constructor(t){super(t)}};function Vt(e){return e===ah||e?.name==="\u0275NotFound"}function Hr(e,t){return Object.is(e,t)}var j=null,Lr=!1,Ni=1,ch=null,V=Symbol("SIGNAL");function I(e){let t=j;return j=e,t}function Br(){return j}var Be={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function ut(e){if(Lr)throw new Error("");if(j===null)return;j.consumerOnSignalRead(e);let t=j.nextProducerIndex++;if(qr(j),t<j.producerNode.length&&j.producerNode[t]!==e&&In(j)){let n=j.producerNode[t];Ur(n,j.producerIndexOfThis[t])}j.producerNode[t]!==e&&(j.producerNode[t]=e,j.producerIndexOfThis[t]=In(j)?rl(e,j,t):0),j.producerLastReadVersion[t]=e.version}function nl(){Ni++}function $r(e){if(!(In(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Ni)){if(!e.producerMustRecompute(e)&&!dt(e)){Vr(e);return}e.producerRecomputeValue(e),Vr(e)}}function xi(e){if(e.liveConsumerNode===void 0)return;let t=Lr;Lr=!0;try{for(let n of e.liveConsumerNode)n.dirty||lh(n)}finally{Lr=t}}function Si(){return j?.consumerAllowSignalWrites!==!1}function lh(e){e.dirty=!0,xi(e),e.consumerMarkedDirty?.(e)}function Vr(e){e.dirty=!1,e.lastCleanEpoch=Ni}function Se(e){return e&&(e.nextProducerIndex=0),I(e)}function $e(e,t){if(I(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(In(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Ur(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function dt(e){qr(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||($r(n),r!==n.version))return!0}return!1}function Ht(e){if(qr(e),In(e))for(let t=0;t<e.producerNode.length;t++)Ur(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function rl(e,t,n){if(ol(e),e.liveConsumerNode.length===0&&il(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=rl(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Ur(e,t){if(ol(e),e.liveConsumerNode.length===1&&il(e))for(let r=0;r<e.producerNode.length;r++)Ur(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];qr(o),o.producerIndexOfThis[r]=t}}function In(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function qr(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function ol(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function il(e){return e.producerNode!==void 0}function Wr(e){ch?.(e)}function En(e,t){let n=Object.create(uh);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if($r(n),ut(n),n.value===vn)throw n.error;return n.value};return r[V]=n,Wr(n),r}var Fr=Symbol("UNSET"),jr=Symbol("COMPUTING"),vn=Symbol("ERRORED"),uh=W(q({},Be),{value:Fr,dirty:!0,error:null,equal:Hr,kind:"computed",producerMustRecompute(e){return e.value===Fr||e.value===jr},producerRecomputeValue(e){if(e.value===jr)throw new Error("");let t=e.value;e.value=jr;let n=Se(e),r,o=!1;try{r=e.computation(),I(null),o=t!==Fr&&t!==vn&&r!==vn&&e.equal(t,r)}catch(i){r=vn,e.error=i}finally{$e(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function dh(){throw new Error}var sl=dh;function al(e){sl(e)}function Ri(e){sl=e}var fh=null;function Oi(e,t){let n=Object.create(Dn);n.value=e,t!==void 0&&(n.equal=t);let r=()=>cl(n);return r[V]=n,Wr(n),[r,s=>Bt(n,s),s=>Ai(n,s)]}function cl(e){return ut(e),e.value}function Bt(e,t){Si()||al(e),e.equal(e.value,t)||(e.value=t,ph(e))}function Ai(e,t){Si()||al(e),Bt(e,t(e.value))}var Dn=W(q({},Be),{equal:Hr,value:void 0,kind:"signal"});function ph(e){e.version++,nl(),xi(e),fh?.(e)}function ll(e){let t=I(null);try{return e()}finally{I(t)}}var Yr="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",M=class extends Error{code;constructor(t,n){super(Jr(t,n)),this.code=t}};function hh(e){return`NG0${Math.abs(e)}`}function Jr(e,t){return`${hh(e)}${t?": "+t:""}`}var Ut=globalThis;function k(e){for(let t in e)if(e[t]===k)return t;throw Error("")}function pl(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function ee(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(ee).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Kr(e,t){return e?t?`${e} ${t}`:e:t||""}var gh=k({__forward_ref__:k});function Xr(e){return e.__forward_ref__=Xr,e.toString=function(){return ee(this())},e}function G(e){return Wi(e)?e():e}function Wi(e){return typeof e=="function"&&e.hasOwnProperty(gh)&&e.__forward_ref__===Xr}function hl(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function B(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function gl(e){return{providers:e.providers||[],imports:e.imports||[]}}function _n(e){return yh(e,eo)}function mh(e){return _n(e)!==null}function yh(e,t){return e.hasOwnProperty(t)&&e[t]||null}function vh(e){let t=e?.[eo]??null;return t||null}function Pi(e){return e&&e.hasOwnProperty(zr)?e[zr]:null}var eo=k({\u0275prov:k}),zr=k({\u0275inj:k}),x=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=B({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Gi(e){return e&&!!e.\u0275providers}var zi=k({\u0275cmp:k}),Qi=k({\u0275dir:k}),Zi=k({\u0275pipe:k}),Yi=k({\u0275mod:k}),Cn=k({\u0275fac:k}),mt=k({__NG_ELEMENT_ID__:k}),ul=k({__NG_ENV_ID__:k});function Mn(e){return typeof e=="string"?e:e==null?"":String(e)}function ml(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Mn(e)}function Ji(e,t){throw new M(-200,e)}function to(e,t){throw new M(-201,!1)}var Li;function yl(){return Li}function Z(e){let t=Li;return Li=e,t}function Ki(e,t,n){let r=_n(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;to(e,"Injector")}var Ih={},ft=Ih,Fi="__NG_DI_FLAG__",ji=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=pt(n)||0;try{return this.injector.get(t,r&8?null:ft,r)}catch(o){if(Vt(o))return o;throw o}}},Qr="ngTempTokenPath",Eh="ngTokenPath",Dh=/\n/gm,wh="\u0275",dl="__source";function Ch(e,t=0){let n=Mi();if(n===void 0)throw new M(-203,!1);if(n===null)return Ki(e,void 0,t);{let r=bh(t),o=n.retrieve(e,r);if(Vt(o)){if(r.optional)return null;throw o}return o}}function Ee(e,t=0){return(yl()||Ch)(G(e),t)}function m(e,t){return Ee(e,pt(t))}function pt(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function bh(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function Vi(e){let t=[];for(let n=0;n<e.length;n++){let r=G(e[n]);if(Array.isArray(r)){if(r.length===0)throw new M(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=Th(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(Ee(o,i))}else t.push(Ee(r))}return t}function Xi(e,t){return e[Fi]=t,e.prototype[Fi]=t,e}function Th(e){return e[Fi]}function _h(e,t,n,r){let o=e[Qr];throw t[dl]&&o.unshift(t[dl]),e.message=Mh(`
`+e.message,o,n,r),e[Eh]=o,e[Qr]=null,e}function Mh(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==wh?e.slice(2):e;let o=ee(t);if(Array.isArray(t))o=t.map(ee).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):ee(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Dh,`
  `)}`}function Ue(e,t){let n=e.hasOwnProperty(Cn);return n?e[Cn]:null}function vl(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Il(e){return e.flat(Number.POSITIVE_INFINITY)}function no(e,t){e.forEach(n=>Array.isArray(n)?no(n,t):t(n))}function es(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Nn(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function El(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function Dl(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function ro(e,t,n){let r=qt(e,t);return r>=0?e[r|1]=n:(r=~r,Dl(e,r,t,n)),r}function oo(e,t){let n=qt(e,t);if(n>=0)return e[n|1]}function qt(e,t){return Nh(e,t,1)}function Nh(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var We={},z=[],Ge=new x(""),ts=new x("",-1),ns=new x(""),bn=class{get(t,n=ft){if(n===ft)throw new Pr(`NullInjectorError: No provider for ${ee(t)}!`);return n}};function rs(e){return e[Yi]||null}function Ae(e){return e[zi]||null}function os(e){return e[Qi]||null}function wl(e){return e[Zi]||null}function xn(e){return{\u0275providers:e}}function Cl(e){return xn([{provide:Ge,multi:!0,useValue:e}])}function bl(...e){return{\u0275providers:is(!0,e),\u0275fromNgModule:!0}}function is(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return no(t,s=>{let a=s;Zr(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Tl(o,i),n}function Tl(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];ss(o,i=>{t(i,r)})}}function Zr(e,t,n,r){if(e=G(e),!e)return!1;let o=null,i=Pi(e),s=!i&&Ae(e);if(!i&&!s){let c=e.ngModule;if(i=Pi(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Zr(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{no(i.imports,u=>{Zr(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&Tl(l,t)}if(!a){let l=Ue(o)||(()=>new o);t({provide:o,useFactory:l,deps:z},o),t({provide:ns,useValue:o,multi:!0},o),t({provide:Ge,useValue:()=>Ee(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;ss(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function ss(e,t){for(let n of e)Gi(n)&&(n=n.\u0275providers),Array.isArray(n)?ss(n,t):t(n)}var xh=k({provide:String,useValue:k});function _l(e){return e!==null&&typeof e=="object"&&xh in e}function Sh(e){return!!(e&&e.useExisting)}function Rh(e){return!!(e&&e.useFactory)}function ht(e){return typeof e=="function"}function Ml(e){return!!e.useClass}var as=new x(""),Gr={},fl={},ki;function Wt(){return ki===void 0&&(ki=new bn),ki}var oe=class{},gt=class extends oe{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Bi(t,s=>this.processProvider(s)),this.records.set(ts,$t(void 0,this)),o.has("environment")&&this.records.set(oe,$t(void 0,this));let i=this.records.get(as);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(ns,z,{self:!0}))}retrieve(t,n){let r=pt(n)||0;try{return this.get(t,ft,r)}catch(o){if(Vt(o))return o;throw o}}destroy(){wn(this),this._destroyed=!0;let t=I(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),I(t)}}onDestroy(t){return wn(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){wn(this);let n=xe(this),r=Z(void 0),o;try{return t()}finally{xe(n),Z(r)}}get(t,n=ft,r){if(wn(this),t.hasOwnProperty(ul))return t[ul](this);let o=pt(r),i,s=xe(this),a=Z(void 0);try{if(!(o&4)){let l=this.records.get(t);if(l===void 0){let u=Lh(t)&&_n(t);u&&this.injectableDefInScope(u)?l=$t(Hi(t),Gr):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l)}let c=o&2?Wt():this.parent;return n=o&8&&n===ft?null:n,c.get(t,n)}catch(c){if(Vt(c)){if((c[Qr]=c[Qr]||[]).unshift(ee(t)),s)throw c;return _h(c,t,"R3InjectorError",this.source)}else throw c}finally{Z(a),xe(s)}}resolveInjectorInitializers(){let t=I(null),n=xe(this),r=Z(void 0),o;try{let i=this.get(Ge,z,{self:!0});for(let s of i)s()}finally{xe(n),Z(r),I(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(ee(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=G(t);let n=ht(t)?t:G(t&&t.provide),r=Ah(t);if(!ht(t)&&t.multi===!0){let o=this.records.get(n);o||(o=$t(void 0,Gr,!0),o.factory=()=>Vi(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=I(null);try{return n.value===fl?Ji(ee(t)):n.value===Gr&&(n.value=fl,n.value=n.factory()),typeof n.value=="object"&&n.value&&Ph(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{I(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=G(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Hi(e){let t=_n(e),n=t!==null?t.factory:Ue(e);if(n!==null)return n;if(e instanceof x)throw new M(204,!1);if(e instanceof Function)return Oh(e);throw new M(204,!1)}function Oh(e){if(e.length>0)throw new M(204,!1);let n=vh(e);return n!==null?()=>n.factory(e):()=>new e}function Ah(e){if(_l(e))return $t(void 0,e.useValue);{let t=cs(e);return $t(t,Gr)}}function cs(e,t,n){let r;if(ht(e)){let o=G(e);return Ue(o)||Hi(o)}else if(_l(e))r=()=>G(e.useValue);else if(Rh(e))r=()=>e.useFactory(...Vi(e.deps||[]));else if(Sh(e))r=()=>Ee(G(e.useExisting));else{let o=G(e&&(e.useClass||e.provide));if(kh(e))r=()=>new o(...Vi(e.deps));else return Ue(o)||Hi(o)}return r}function wn(e){if(e.destroyed)throw new M(205,!1)}function $t(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function kh(e){return!!e.deps}function Ph(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Lh(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Bi(e,t){for(let n of e)Array.isArray(n)?Bi(n,t):n&&Gi(n)?Bi(n.\u0275providers,t):t(n)}function io(e,t){let n;e instanceof gt?(wn(e),n=e):n=new ji(e);let r,o=xe(n),i=Z(void 0);try{return t()}finally{xe(o),Z(i)}}function ls(){return yl()!==void 0||Mi()!=null}function us(e){if(!ls())throw new M(-203,!1)}var de=0,w=1,y=2,H=3,se=4,Y=5,yt=6,Gt=7,P=8,vt=9,De=10,O=11,zt=12,ds=13,It=14,J=15,ze=16,Et=17,we=18,Sn=19,fs=20,Re=21,so=22,ke=23,te=24,Dt=25,F=26,Nl=1,ps=6,Qe=7,Rn=8,wt=9,$=10;function Ce(e){return Array.isArray(e)&&typeof e[Nl]=="object"}function fe(e){return Array.isArray(e)&&e[Nl]===!0}function ao(e){return(e.flags&4)!==0}function Ze(e){return e.componentOffset>-1}function On(e){return(e.flags&1)===1}function be(e){return!!e.template}function Qt(e){return(e[y]&512)!==0}function Ct(e){return(e[y]&256)===256}var hs="svg",xl="math";function ae(e){for(;Array.isArray(e);)e=e[de];return e}function gs(e,t){return ae(t[e])}function pe(e,t){return ae(t[e.index])}function An(e,t){return e.data[t]}function ms(e,t){return e[t]}function ys(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function ce(e,t){let n=t[e];return Ce(n)?n:n[de]}function Sl(e){return(e[y]&4)===4}function co(e){return(e[y]&128)===128}function Rl(e){return fe(e[H])}function he(e,t){return t==null?null:e[t]}function vs(e){e[Et]=0}function Is(e){e[y]&1024||(e[y]|=1024,co(e)&&Ye(e))}function Ol(e,t){for(;e>0;)t=t[It],e--;return t}function kn(e){return!!(e[y]&9216||e[te]?.dirty)}function lo(e){e[De].changeDetectionScheduler?.notify(8),e[y]&64&&(e[y]|=1024),kn(e)&&Ye(e)}function Ye(e){e[De].changeDetectionScheduler?.notify(0);let t=qe(e);for(;t!==null&&!(t[y]&8192||(t[y]|=8192,!co(t)));)t=qe(t)}function Es(e,t){if(Ct(e))throw new M(911,!1);e[Re]===null&&(e[Re]=[]),e[Re].push(t)}function Al(e,t){if(e[Re]===null)return;let n=e[Re].indexOf(t);n!==-1&&e[Re].splice(n,1)}function qe(e){let t=e[H];return fe(t)?t[H]:t}function Ds(e){return e[Gt]??=[]}function ws(e){return e.cleanup??=[]}function kl(e,t,n,r){let o=Ds(t);o.push(n),e.firstCreatePass&&ws(e).push(r,o.length-1)}var T={lFrame:Yl(null),bindingsEnabled:!0,skipHydrationRootTNode:null},Pn=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(Pn||{}),Fh=0,$i=!1;function Pl(){return T.lFrame.elementDepthCount}function Ll(){T.lFrame.elementDepthCount++}function Fl(){T.lFrame.elementDepthCount--}function uo(){return T.bindingsEnabled}function Cs(){return T.skipHydrationRootTNode!==null}function jl(e){return T.skipHydrationRootTNode===e}function Vl(){T.skipHydrationRootTNode=null}function E(){return T.lFrame.lView}function A(){return T.lFrame.tView}function Hl(e){return T.lFrame.contextLView=e,e[P]}function Bl(e){return T.lFrame.contextLView=null,e}function U(){let e=bs();for(;e!==null&&e.type===64;)e=e.parent;return e}function bs(){return T.lFrame.currentTNode}function $l(){let e=T.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Je(e,t){let n=T.lFrame;n.currentTNode=e,n.isParent=t}function fo(){return T.lFrame.isParent}function po(){T.lFrame.isParent=!1}function Ul(){return T.lFrame.contextLView}function Ts(e){hl("Must never be called in production mode"),Fh=e}function _s(){return $i}function Zt(e){let t=$i;return $i=e,t}function Ms(){let e=T.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function ql(e){return T.lFrame.bindingIndex=e}function Ke(){return T.lFrame.bindingIndex++}function Ns(e){let t=T.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Wl(){return T.lFrame.inI18n}function Gl(e,t){let n=T.lFrame;n.bindingIndex=n.bindingRootIndex=e,ho(t)}function zl(){return T.lFrame.currentDirectiveIndex}function ho(e){T.lFrame.currentDirectiveIndex=e}function Ql(e){let t=T.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function go(){return T.lFrame.currentQueryIndex}function Ln(e){T.lFrame.currentQueryIndex=e}function jh(e){let t=e[w];return t.type===2?t.declTNode:t.type===1?e[Y]:null}function xs(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=jh(i),o===null||(i=i[It],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=T.lFrame=Zl();return r.currentTNode=t,r.lView=e,!0}function mo(e){let t=Zl(),n=e[w];T.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Zl(){let e=T.lFrame,t=e===null?null:e.child;return t===null?Yl(e):t}function Yl(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Jl(){let e=T.lFrame;return T.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Ss=Jl;function yo(){let e=Jl();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Kl(e){return(T.lFrame.contextLView=Ol(e,T.lFrame.contextLView))[P]}function Pe(){return T.lFrame.selectedIndex}function Xe(e){T.lFrame.selectedIndex=e}function Fn(){let e=T.lFrame;return An(e.tView,e.selectedIndex)}function Xl(){T.lFrame.currentNamespace=hs}function eu(){Vh()}function Vh(){T.lFrame.currentNamespace=null}function tu(){return T.lFrame.currentNamespace}var nu=!0;function jn(){return nu}function Vn(e){nu=e}function Ui(e,t=null,n=null,r){let o=Rs(e,t,n,r);return o.resolveInjectorInitializers(),o}function Rs(e,t=null,n=null,r,o=new Set){let i=[n||z,bl(e)];return r=r||(typeof e=="object"?void 0:ee(e)),new gt(i,t||Wt(),r||null,o)}var ue=class e{static THROW_IF_NOT_FOUND=ft;static NULL=new bn;static create(t,n){if(Array.isArray(t))return Ui({name:""},n,t,"");{let r=t.name??"";return Ui({name:r},t.parent,t.providers,r)}}static \u0275prov=B({token:e,providedIn:"any",factory:()=>Ee(ts)});static __NG_ELEMENT_ID__=-1},ru=new x(""),Le=(()=>{class e{static __NG_ELEMENT_ID__=Hh;static __NG_ENV_ID__=n=>n}return e})(),Tn=class extends Le{_lView;constructor(t){super(),this._lView=t}get destroyed(){return Ct(this._lView)}onDestroy(t){let n=this._lView;return Es(n,t),()=>Al(n,t)}};function Hh(){return new Tn(E())}var Oe=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Te=new x("",{providedIn:"root",factory:()=>{let e=m(oe),t;return n=>{t??=e.get(Oe),t.handleError(n)}}}),ou={provide:Ge,useValue:()=>void m(Oe),multi:!0},Bh=new x("",{providedIn:"root",factory:()=>{let e=m(ru).defaultView;if(!e)return;let t=m(Te),n=i=>{t(i.reason),i.preventDefault()},r=i=>{i.error?t(i.error):t(new Error(i.message,{cause:i})),i.preventDefault()},o=()=>{e.addEventListener("unhandledrejection",n),e.addEventListener("error",r)};typeof Zone<"u"?Zone.root.run(o):o(),m(Le).onDestroy(()=>{e.removeEventListener("error",r),e.removeEventListener("unhandledrejection",n)})}});function $h(){return xn([Cl(()=>void m(Bh))])}function Os(e){return typeof e=="function"&&e[V]!==void 0}function vo(e,t){let[n,r,o]=Oi(e,t?.equal),i=n,s=i[V];return i.set=r,i.update=o,i.asReadonly=As.bind(i),i}function As(){let e=this[V];if(e.readonlyFn===void 0){let t=()=>this();t[V]=e,e.readonlyFn=t}return e.readonlyFn}function ks(e){return Os(e)&&typeof e.set=="function"}var ie=class{},Hn=new x("",{providedIn:"root",factory:()=>!1});var Ps=new x(""),Ls=new x("");var bt=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Uh}return e})();function Uh(){return new bt(E(),U())}var Tt=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new un(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new _(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=B({token:e,providedIn:"root",factory:()=>new e})}return e})(),Fs=(()=>{class e{internalPendingTasks=m(Tt);scheduler=m(ie);errorHandler=m(Te);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){let r=this.add();n().catch(this.errorHandler).finally(r)}static \u0275prov=B({token:e,providedIn:"root",factory:()=>new e})}return e})();function _t(...e){}var Bn=(()=>{class e{static \u0275prov=B({token:e,providedIn:"root",factory:()=>new qi})}return e})(),qi=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};function on(e){return{toString:e}.toString()}var Io="__parameters__";function Yh(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Lu(e,t,n){return on(()=>{let r=Yh(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let d=c.hasOwnProperty(Io)?c[Io]:Object.defineProperty(c,Io,{value:[]})[Io];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Jh=Xi(Lu("Optional"),8);var Kh=Xi(Lu("SkipSelf"),4);function Xh(e){return typeof e=="function"}var No=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Fu(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var eg=(()=>{let e=()=>ju;return e.ngInherit=!0,e})();function ju(e){return e.type.prototype.ngOnChanges&&(e.setInput=ng),tg}function tg(){let e=Hu(this),t=e?.current;if(t){let n=e.previous;if(n===We)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function ng(e,t,n,r,o){let i=this.declaredInputs[r],s=Hu(e)||rg(e,{previous:We,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new No(l&&l.currentValue,n,c===We),Fu(e,t,o,n)}var Vu="__ngSimpleChanges__";function Hu(e){return e[Vu]||null}function rg(e,t){return e[Vu]=t}var iu=[];var S=function(e,t=null,n){for(let r=0;r<iu.length;r++){let o=iu[r];o(e,t,n)}};function og(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=ju(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function ka(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function bo(e,t,n){Bu(e,t,3,n)}function To(e,t,n,r){(e[y]&3)===n&&Bu(e,t,n,r)}function js(e,t){let n=e[y];(n&3)===t&&(n&=16383,n+=1,e[y]=n)}function Bu(e,t,n,r){let o=r!==void 0?e[Et]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Et]+=65536),(a<i||i==-1)&&(ig(e,n,t,c),e[Et]=(e[Et]&**********)+c+2),c++}function su(e,t){S(4,e,t);let n=I(null);try{t.call(e)}finally{I(n),S(5,e,t)}}function ig(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[y]>>14<e[Et]>>16&&(e[y]&3)===t&&(e[y]+=16384,su(a,i)):su(a,i)}var Jt=-1,Nt=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function sg(e){return(e.flags&8)!==0}function ag(e){return(e.flags&16)!==0}function cg(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];lg(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function $u(e){return e===3||e===4||e===6}function lg(e){return e.charCodeAt(0)===64}function Kt(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?au(e,n,o,null,t[++r]):au(e,n,o,null,null))}}return e}function au(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function Uu(e){return e!==Jt}function xo(e){return e&32767}function ug(e){return e>>16}function So(e,t){let n=ug(e),r=t;for(;n>0;)r=r[It],n--;return r}var Zs=!0;function Ro(e){let t=Zs;return Zs=e,t}var dg=256,qu=dg-1,Wu=5,fg=0,_e={};function pg(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(mt)&&(r=n[mt]),r==null&&(r=n[mt]=fg++);let o=r&qu,i=1<<o;t.data[e+(o>>Wu)]|=i}function Oo(e,t){let n=Gu(e,t);if(n!==-1)return n;let r=t[w];r.firstCreatePass&&(e.injectorIndex=t.length,Vs(r.data,e),Vs(t,null),Vs(r.blueprint,null));let o=Pa(e,t),i=e.injectorIndex;if(Uu(o)){let s=xo(o),a=So(o,t),c=a[w].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Vs(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Gu(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Pa(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Ju(o),r===null)return Jt;if(n++,o=o[It],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Jt}function Ys(e,t,n){pg(e,t,n)}function hg(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if($u(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function zu(e,t,n){if(n&8||e!==void 0)return e;to(t,"NodeInjector")}function Qu(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[vt],i=Z(void 0);try{return o?o.get(t,r,n&8):Ki(t,r,n&8)}finally{Z(i)}}return zu(r,t,n)}function Zu(e,t,n,r=0,o){if(e!==null){if(t[y]&2048&&!(r&2)){let s=Ig(e,t,n,r,_e);if(s!==_e)return s}let i=Yu(e,t,n,r,_e);if(i!==_e)return i}return Qu(t,n,r,o)}function Yu(e,t,n,r,o){let i=mg(n);if(typeof i=="function"){if(!xs(t,e,r))return r&1?zu(o,n,r):Qu(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))to(n);else return s}finally{Ss()}}else if(typeof i=="number"){let s=null,a=Gu(e,t),c=Jt,l=r&1?t[J][Y]:null;for((a===-1||r&4)&&(c=a===-1?Pa(e,t):t[a+8],c===Jt||!lu(r,!1)?a=-1:(s=t[w],a=xo(c),t=So(c,t)));a!==-1;){let u=t[w];if(cu(i,a,u.data)){let d=gg(a,t,n,s,r,l);if(d!==_e)return d}c=t[a+8],c!==Jt&&lu(r,t[w].data[a+8]===l)&&cu(i,a,t)?(s=u,a=xo(c),t=So(c,t)):a=-1}}return o}function gg(e,t,n,r,o,i){let s=t[w],a=s.data[e+8],c=r==null?Ze(a)&&Zs:r!=s&&(a.type&3)!==0,l=o&1&&i===a,u=_o(a,s,n,c,l);return u!==null?qn(t,s,u,a):_e}function _o(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,d=r?a:a+u,p=o?a+u:l;for(let f=d;f<p;f++){let h=s[f];if(f<c&&n===h||f>=c&&h.type===n)return f}if(o){let f=s[c];if(f&&be(f)&&f.type===n)return c}return null}function qn(e,t,n,r){let o=e[n],i=t.data;if(o instanceof Nt){let s=o;s.resolving&&Ji(ml(i[n]));let a=Ro(s.canSeeViewProviders);s.resolving=!0;let c=i[n].type||i[n],l,u=s.injectImpl?Z(s.injectImpl):null,d=xs(e,r,0);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&og(n,i[n],t)}finally{u!==null&&Z(u),Ro(a),s.resolving=!1,Ss()}}return o}function mg(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(mt)?e[mt]:void 0;return typeof t=="number"?t>=0?t&qu:yg:t}function cu(e,t,n){let r=1<<e;return!!(n[t+(e>>Wu)]&r)}function lu(e,t){return!(e&2)&&!(e&1&&t)}var Mt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Zu(this._tNode,this._lView,t,pt(r),n)}};function yg(){return new Mt(U(),E())}function vg(e){return on(()=>{let t=e.prototype.constructor,n=t[Cn]||Js(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Cn]||Js(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Js(e){return Wi(e)?()=>{let t=Js(G(e));return t&&t()}:Ue(e)}function Ig(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[y]&2048&&!Qt(s);){let a=Yu(i,s,n,r|2,_e);if(a!==_e)return a;let c=i.parent;if(!c){let l=s[fs];if(l){let u=l.get(n,_e,r);if(u!==_e)return u}c=Ju(s),s=s[It]}i=c}return o}function Ju(e){let t=e[w],n=t.type;return n===2?t.declTNode:n===1?e[Y]:null}function Ku(e){return hg(U(),e)}function Eg(){return sn(U(),E())}function sn(e,t){return new Kn(pe(e,t))}var Kn=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Eg}return e})();function Xu(e){return e instanceof Kn?e.nativeElement:e}function Dg(){return this._results[Symbol.iterator]()}var Ao=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new re}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Il(t);(this._changesDetected=!vl(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Dg};function ed(e){return(e.flags&128)===128}var La=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(La||{}),td=new Map,wg=0;function Cg(){return wg++}function bg(e){td.set(e[Sn],e)}function Ks(e){td.delete(e[Sn])}var uu="__ngContext__";function an(e,t){Ce(t)?(e[uu]=t[Sn],bg(t)):e[uu]=t}function nd(e){return od(e[zt])}function rd(e){return od(e[se])}function od(e){for(;e!==null&&!fe(e);)e=e[se];return e}var Xs;function Tg(e){Xs=e}function id(){if(Xs!==void 0)return Xs;if(typeof document<"u")return document;throw new M(210,!1)}var _g=new x("",{providedIn:"root",factory:()=>Mg}),Mg="ng",sd=new x(""),Ng=new x("",{providedIn:"platform",factory:()=>"unknown"});var xg=new x(""),Sg=new x("",{providedIn:"root",factory:()=>id().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Rg="h",Og="b";var ad="r";var cd="di";var ld=!1,ud=new x("",{providedIn:"root",factory:()=>ld});var Ag=(e,t,n,r)=>{};function kg(e,t,n,r){Ag(e,t,n,r)}var Pg=()=>null;function dd(e,t,n=!1){return Pg(e,t,n)}function fd(e,t){let n=e.contentQueries;if(n!==null){let r=I(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Ln(i),a.contentQueries(2,t[s],s)}}}finally{I(r)}}}function ea(e,t,n){Ln(0);let r=I(null);try{t(e,n)}finally{I(r)}}function Fa(e,t,n){if(ao(t)){let r=I(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{I(r)}}}var Xt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Xt||{}),Eo;function Lg(){if(Eo===void 0&&(Eo=null,Ut.trustedTypes))try{Eo=Ut.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Eo}function Qo(e){return Lg()?.createHTML(e)||e}var Do;function Fg(){if(Do===void 0&&(Do=null,Ut.trustedTypes))try{Do=Ut.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Do}function du(e){return Fg()?.createScriptURL(e)||e}var je=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Yr})`}},ta=class extends je{getTypeName(){return"HTML"}},na=class extends je{getTypeName(){return"Style"}},ra=class extends je{getTypeName(){return"Script"}},oa=class extends je{getTypeName(){return"URL"}},ia=class extends je{getTypeName(){return"ResourceURL"}};function Xn(e){return e instanceof je?e.changingThisBreaksApplicationSecurity:e}function ja(e,t){let n=pd(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Yr})`)}return n===t}function pd(e){return e instanceof je&&e.getTypeName()||null}function jg(e){return new ta(e)}function Vg(e){return new na(e)}function Hg(e){return new ra(e)}function Bg(e){return new oa(e)}function $g(e){return new ia(e)}function Ug(e){let t=new aa(e);return qg()?new sa(t):t}var sa=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Qo(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},aa=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Qo(t),n}};function qg(){try{return!!new window.DOMParser().parseFromString(Qo(""),"text/html")}catch{return!1}}var Wg=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Va(e){return e=String(e),e.match(Wg)?e:"unsafe:"+e}function Ve(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function er(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var hd=Ve("area,br,col,hr,img,wbr"),gd=Ve("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),md=Ve("rp,rt"),Gg=er(md,gd),zg=er(gd,Ve("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Qg=er(md,Ve("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),fu=er(hd,zg,Qg,Gg),yd=Ve("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Zg=Ve("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Yg=Ve("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Jg=er(yd,Zg,Yg),Kg=Ve("script,style,template"),ca=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=tm(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=em(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=pu(t).toLowerCase();if(!fu.hasOwnProperty(n))return this.sanitizedSomething=!0,!Kg.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Jg.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;yd[a]&&(c=Va(c)),this.buf.push(" ",s,'="',hu(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=pu(t).toLowerCase();fu.hasOwnProperty(n)&&!hd.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(hu(t))}};function Xg(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function em(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw vd(t);return t}function tm(e){let t=e.firstChild;if(t&&Xg(e,t))throw vd(t);return t}function pu(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function vd(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var nm=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,rm=/([^\#-~ |!])/g;function hu(e){return e.replace(/&/g,"&amp;").replace(nm,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(rm,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var wo;function om(e,t){let n=null;try{wo=wo||Ug(e);let r=t?String(t):"";n=wo.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=wo.getInertBodyElement(r)}while(r!==i);let a=new ca().sanitizeChildren(gu(n)||n);return Qo(a)}finally{if(n){let r=gu(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function gu(e){return"content"in e&&im(e)?e.content:null}function im(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Zo=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Zo||{});function Id(e){let t=Dd();return t?t.sanitize(Zo.URL,e)||"":ja(e,"URL")?Xn(e):Va(Mn(e))}function Ed(e){let t=Dd();if(t)return du(t.sanitize(Zo.RESOURCE_URL,e)||"");if(ja(e,"ResourceURL"))return du(Xn(e));throw new M(904,!1)}function sm(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?Ed:Id}function am(e,t,n){return sm(t,n)(e)}function Dd(){let e=E();return e&&e[De].sanitizer}var cm=/^>|^->|<!--|-->|--!>|<!-$/g,lm=/(<|>)/g,um="\u200B$1\u200B";function dm(e){return e.replace(cm,t=>t.replace(lm,um))}function wd(e){return e instanceof Function?e():e}function fm(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Cd="ng-template";function pm(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&fm(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Ha(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Ha(e){return e.type===4&&e.value!==Cd}function hm(e,t,n){let r=e.type===4&&!n?Cd:e.value;return t===r}function gm(e,t,n){let r=4,o=e.attrs,i=o!==null?vm(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!ge(r)&&!ge(c))return!1;if(s&&ge(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!hm(e,c,n)||c===""&&t.length===1){if(ge(r))return!1;s=!0}}else if(r&8){if(o===null||!pm(e,o,c,n)){if(ge(r))return!1;s=!0}}else{let l=t[++a],u=mm(c,o,Ha(e),n);if(u===-1){if(ge(r))return!1;s=!0;continue}if(l!==""){let d;if(u>i?d="":d=o[u+1].toLowerCase(),r&2&&l!==d){if(ge(r))return!1;s=!0}}}}return ge(r)||s}function ge(e){return(e&1)===0}function mm(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Im(t,e)}function bd(e,t,n=!1){for(let r=0;r<t.length;r++)if(gm(e,t[r],n))return!0;return!1}function ym(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function vm(e){for(let t=0;t<e.length;t++){let n=e[t];if($u(n))return t}return e.length}function Im(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Em(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function mu(e,t){return e?":not("+t.trim()+")":t}function Dm(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!ge(s)&&(t+=mu(i,o),o=""),r=s,i=i||!ge(r);n++}return o!==""&&(t+=mu(i,o)),t}function wm(e){return e.map(Dm).join(",")}function Cm(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!ge(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var ne={};function bm(e,t){return e.createText(t)}function Tm(e,t,n){e.setValue(t,n)}function _m(e,t){return e.createComment(dm(t))}function Td(e,t,n){return e.createElement(t,n)}function ko(e,t,n,r,o){e.insertBefore(t,n,r,o)}function _d(e,t,n){e.appendChild(t,n)}function yu(e,t,n,r,o){r!==null?ko(e,t,n,r,o):_d(e,t,n)}function Md(e,t,n){e.removeChild(null,t,n)}function Mm(e,t,n){e.setAttribute(t,"style",n)}function Nm(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Nd(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&cg(e,t,r),o!==null&&Nm(e,t,o),i!==null&&Mm(e,t,i)}function Ba(e,t,n,r,o,i,s,a,c,l,u){let d=F+r,p=d+o,f=xm(d,p),h=typeof l=="function"?l():l;return f[w]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:u}}function xm(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:ne);return n}function Sm(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Ba(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function $a(e,t,n,r,o,i,s,a,c,l,u){let d=t.blueprint.slice();return d[de]=o,d[y]=r|4|128|8|64|1024,(l!==null||e&&e[y]&2048)&&(d[y]|=2048),vs(d),d[H]=d[It]=e,d[P]=n,d[De]=s||e&&e[De],d[O]=a||e&&e[O],d[vt]=c||e&&e[vt]||null,d[Y]=i,d[Sn]=Cg(),d[yt]=u,d[fs]=l,d[J]=t.type==2?e[J]:d,d}function Rm(e,t,n){let r=pe(t,e),o=Sm(n),i=e[De].rendererFactory,s=Ua(e,$a(e,o,null,xd(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function xd(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Sd(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Ua(e,t){return e[zt]?e[ds][se]=t:e[zt]=t,e[ds]=t,t}function Om(e=1){Rd(A(),E(),Pe()+e,!1)}function Rd(e,t,n,r){if(!r)if((t[y]&3)===3){let i=e.preOrderCheckHooks;i!==null&&bo(t,i,n)}else{let i=e.preOrderHooks;i!==null&&To(t,i,0,n)}Xe(n)}var Yo=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Yo||{});function la(e,t,n,r){let o=I(null);try{let[i,s,a]=e.inputs[n],c=null;(s&Yo.SignalBased)!==0&&(c=t[i][V]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Fu(t,c,i,r)}finally{I(o)}}function Od(e,t,n,r,o){let i=Pe(),s=r&2;try{Xe(-1),s&&t.length>F&&Rd(e,t,F,!1),S(s?2:0,o,n),n(r,o)}finally{Xe(i),S(s?3:1,o,n)}}function Jo(e,t,n){jm(e,t,n),(n.flags&64)===64&&Vm(e,t,n)}function qa(e,t,n=pe){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Am(e,t,n,r){let i=r.get(ud,ld)||n===Xt.ShadowDom,s=e.selectRootElement(t,i);return km(s),s}function km(e){Pm(e)}var Pm=()=>null;function Lm(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Ad(e,t,n,r,o,i){let s=t[w];if(Ga(e,s,t,n,r)){Ze(e)&&Fm(t,e.index);return}kd(e,t,n,r,o,i)}function kd(e,t,n,r,o,i){if(e.type&3){let s=pe(e,t);n=Lm(n),r=i!=null?i(r,e.value||"",n):r,o.setProperty(s,n,r)}else e.type&12}function Fm(e,t){let n=ce(t,e);n[y]&16||(n[y]|=64)}function jm(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Ze(n)&&Rm(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Oo(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=qn(t,e,s,n);if(an(c,t),i!==null&&Um(t,s-r,c,a,n,i),be(a)){let l=ce(n.index,t);l[P]=qn(t,e,s,n)}}}function Vm(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=zl();try{Xe(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];ho(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Hm(c,l)}}finally{Xe(-1),ho(s)}}function Hm(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Wa(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];bd(t,i.selectors,!1)&&(r??=[],be(i)?r.unshift(i):r.push(i))}return r}function Bm(e,t,n,r,o,i){let s=pe(e,t);$m(t[O],s,i,e.value,n,r,o)}function $m(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Mn(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Um(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];la(r,n,c,l)}}function qm(e,t){let n=e[vt];if(!n)return;n.get(Te,null)?.(t)}function Ga(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],d=t.data[l];la(d,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];la(u,l,r,o),a=!0}return a}function Wm(e,t){let n=ce(t,e),r=n[w];Gm(r,n);let o=n[de];o!==null&&n[yt]===null&&(n[yt]=dd(o,n[vt])),S(18),za(r,n,n[P]),S(19,n[P])}function Gm(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function za(e,t,n){mo(t);try{let r=e.viewQuery;r!==null&&ea(1,r,n);let o=e.template;o!==null&&Od(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[we]?.finishViewCreation(e),e.staticContentQueries&&fd(e,t),e.staticViewQueries&&ea(2,e.viewQuery,n);let i=e.components;i!==null&&zm(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[y]&=-5,yo()}}function zm(e,t){for(let n=0;n<t.length;n++)Wm(e,t[n])}function tr(e,t,n,r){let o=I(null);try{let i=t.tView,a=e[y]&4096?4096:16,c=$a(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[ze]=l;let u=e[we];return u!==null&&(c[we]=u.createEmbeddedView(i)),za(i,c,n),c}finally{I(o)}}function en(e,t){return!t||t.firstChild===null||ed(e)}var vu=!1,Qm=new x(""),Zm;function Qa(e,t){return Zm(e,t)}var Po=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Po||{});function Ko(e){return(e.flags&32)===32}function Yt(e,t,n,r,o){if(r!=null){let i,s=!1;fe(r)?i=r:Ce(r)&&(s=!0,r=r[de]);let a=ae(r);e===0&&n!==null?o==null?_d(t,n,a):ko(t,n,a,o||null,!0):e===1&&n!==null?ko(t,n,a,o||null,!0):e===2?Md(t,a,s):e===3&&t.destroyNode(a),i!=null&&iy(t,e,i,n,o)}}function Ym(e,t){Pd(e,t),t[de]=null,t[Y]=null}function Jm(e,t,n,r,o,i){r[de]=o,r[Y]=t,ti(e,r,n,1,o,i)}function Pd(e,t){t[De].changeDetectionScheduler?.notify(9),ti(e,t,t[O],2,null,null)}function Km(e){let t=e[zt];if(!t)return Hs(e[w],e);for(;t;){let n=null;if(Ce(t))n=t[zt];else{let r=t[$];r&&(n=r)}if(!n){for(;t&&!t[se]&&t!==e;)Ce(t)&&Hs(t[w],t),t=t[H];t===null&&(t=e),Ce(t)&&Hs(t[w],t),n=t&&t[se]}t=n}}function Za(e,t){let n=e[wt],r=n.indexOf(t);n.splice(r,1)}function Xo(e,t){if(Ct(t))return;let n=t[O];n.destroyNode&&ti(e,t,n,3,null,null),Km(t)}function Hs(e,t){if(Ct(t))return;let n=I(null);try{t[y]&=-129,t[y]|=256,t[te]&&Ht(t[te]),ey(e,t),Xm(e,t),t[w].type===1&&t[O].destroy();let r=t[ze];if(r!==null&&fe(t[H])){r!==t[H]&&Za(r,t);let o=t[we];o!==null&&o.detachView(e)}Ks(t)}finally{I(n)}}function Xm(e,t){let n=e.cleanup,r=t[Gt];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Gt]=null);let o=t[Re];if(o!==null){t[Re]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[ke];if(i!==null){t[ke]=null;for(let s of i)s.destroy()}}function ey(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Nt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];S(4,a,c);try{c.call(a)}finally{S(5,a,c)}}else{S(4,o,i);try{i.call(o)}finally{S(5,o,i)}}}}}function Ld(e,t,n){return ty(e,t.parent,n)}function ty(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[de];if(Ze(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Xt.None||o===Xt.Emulated)return null}return pe(r,n)}function Fd(e,t,n){return ry(e,t,n)}function ny(e,t,n){return e.type&40?pe(e,n):null}var ry=ny,Iu;function ei(e,t,n,r){let o=Ld(e,r,t),i=t[O],s=r.parent||t[Y],a=Fd(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)yu(i,o,n[c],a,!1);else yu(i,o,n,a,!1);Iu!==void 0&&Iu(i,r,t,n,o)}function $n(e,t){if(t!==null){let n=t.type;if(n&3)return pe(t,e);if(n&4)return ua(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return $n(e,r);{let o=e[t.index];return fe(o)?ua(-1,o):ae(o)}}else{if(n&128)return $n(e,t.next);if(n&32)return Qa(t,e)()||ae(e[t.index]);{let r=jd(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=qe(e[J]);return $n(o,r)}else return $n(e,t.next)}}}return null}function jd(e,t){if(t!==null){let r=e[J][Y],o=t.projection;return r.projection[o]}return null}function ua(e,t){let n=$+e+1;if(n<t.length){let r=t[n],o=r[w].firstChild;if(o!==null)return $n(r,o)}return t[Qe]}function Ya(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&an(ae(a),r),n.flags|=2),!Ko(n))if(c&8)Ya(e,t,n.child,r,o,i,!1),Yt(t,e,o,a,i);else if(c&32){let l=Qa(n,r),u;for(;u=l();)Yt(t,e,o,u,i);Yt(t,e,o,a,i)}else c&16?Vd(e,t,r,n,o,i):Yt(t,e,o,a,i);n=s?n.projectionNext:n.next}}function ti(e,t,n,r,o,i){Ya(n,r,e.firstChild,t,o,i,!1)}function oy(e,t,n){let r=t[O],o=Ld(e,n,t),i=n.parent||t[Y],s=Fd(i,n,t);Vd(r,0,t,n,o,s)}function Vd(e,t,n,r,o,i){let s=n[J],c=s[Y].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];Yt(t,e,o,u,i)}else{let l=c,u=s[H];ed(r)&&(l.flags|=128),Ya(e,t,l,u,o,i,!0)}}function iy(e,t,n,r,o){let i=n[Qe],s=ae(n);i!==s&&Yt(t,e,r,i,o);for(let a=$;a<n.length;a++){let c=n[a];ti(c[w],c,e,t,r,i)}}function sy(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Po.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Po.Important),e.setStyle(n,r,o,i))}}function Wn(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(ae(i)),fe(i)&&Hd(i,r);let s=n.type;if(s&8)Wn(e,t,n.child,r);else if(s&32){let a=Qa(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=jd(t,n);if(Array.isArray(a))r.push(...a);else{let c=qe(t[J]);Wn(c[w],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Hd(e,t){for(let n=$;n<e.length;n++){let r=e[n],o=r[w].firstChild;o!==null&&Wn(r[w],r,o,t)}e[Qe]!==e[de]&&t.push(e[Qe])}function Bd(e){if(e[Dt]!==null){for(let t of e[Dt])t.impl.addSequence(t);e[Dt].length=0}}var $d=[];function ay(e){return e[te]??cy(e)}function cy(e){let t=$d.pop()??Object.create(uy);return t.lView=e,t}function ly(e){e.lView[te]!==e&&(e.lView=null,$d.push(e))}var uy=W(q({},Be),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Ye(e.lView)},consumerOnSignalRead(){this.lView[te]=this}});function dy(e){let t=e[te]??Object.create(fy);return t.lView=e,t}var fy=W(q({},Be),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=qe(e.lView);for(;t&&!Ud(t[w]);)t=qe(t);t&&Is(t)},consumerOnSignalRead(){this.lView[te]=this}});function Ud(e){return e.type!==2}function qd(e){if(e[ke]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[ke])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[y]&8192)}}var py=100;function Ja(e,t=0){let r=e[De].rendererFactory,o=!1;o||r.begin?.();try{hy(e,t)}finally{o||r.end?.()}}function hy(e,t){let n=_s();try{Zt(!0),da(e,t);let r=0;for(;kn(e);){if(r===py)throw new M(103,!1);r++,da(e,1)}}finally{Zt(n)}}function Wd(e,t){Ts(t?Pn.Exhaustive:Pn.OnlyDirtyViews);try{Ja(e)}finally{Ts(Pn.Off)}}function gy(e,t,n,r){if(Ct(t))return;let o=t[y],i=!1,s=!1;mo(t);let a=!0,c=null,l=null;i||(Ud(e)?(l=ay(t),c=Se(l)):Br()===null?(a=!1,l=dy(t),c=Se(l)):t[te]&&(Ht(t[te]),t[te]=null));try{vs(t),ql(e.bindingStartIndex),n!==null&&Od(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let f=e.preOrderCheckHooks;f!==null&&bo(t,f,null)}else{let f=e.preOrderHooks;f!==null&&To(t,f,0,null),js(t,0)}if(s||my(t),qd(t),Gd(t,0),e.contentQueries!==null&&fd(e,t),!i)if(u){let f=e.contentCheckHooks;f!==null&&bo(t,f)}else{let f=e.contentHooks;f!==null&&To(t,f,1),js(t,1)}vy(e,t);let d=e.components;d!==null&&Qd(t,d,0);let p=e.viewQuery;if(p!==null&&ea(2,p,r),!i)if(u){let f=e.viewCheckHooks;f!==null&&bo(t,f)}else{let f=e.viewHooks;f!==null&&To(t,f,2),js(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[so]){for(let f of t[so])f();t[so]=null}i||(Bd(t),t[y]&=-73)}catch(u){throw i||Ye(t),u}finally{l!==null&&($e(l,c),a&&ly(l)),yo()}}function Gd(e,t){for(let n=nd(e);n!==null;n=rd(n))for(let r=$;r<n.length;r++){let o=n[r];zd(o,t)}}function my(e){for(let t=nd(e);t!==null;t=rd(t)){if(!(t[y]&2))continue;let n=t[wt];for(let r=0;r<n.length;r++){let o=n[r];Is(o)}}}function yy(e,t,n){S(18);let r=ce(t,e);zd(r,n),S(19,r[P])}function zd(e,t){co(e)&&da(e,t)}function da(e,t){let r=e[w],o=e[y],i=e[te],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&dt(i)),s||=!1,i&&(i.dirty=!1),e[y]&=-9217,s)gy(r,e,r.template,e[P]);else if(o&8192){let a=I(null);try{qd(e),Gd(e,1);let c=r.components;c!==null&&Qd(e,c,1),Bd(e)}finally{I(a)}}}function Qd(e,t,n){for(let r=0;r<t.length;r++)yy(e,t[r],n)}function vy(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Xe(~o);else{let i=o,s=n[++r],a=n[++r];Gl(s,i);let c=t[i];S(24,c),a(2,c),S(25,c)}}}finally{Xe(-1)}}function Ka(e,t){let n=_s()?64:1088;for(e[De].changeDetectionScheduler?.notify(t);e;){e[y]|=n;let r=qe(e);if(Qt(e)&&!r)return e;e=r}return null}function Zd(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Yd(e,t){let n=$+t;if(n<e.length)return e[n]}function nr(e,t,n,r=!0){let o=t[w];if(Iy(o,t,e,n),r){let s=ua(n,e),a=t[O],c=a.parentNode(e[Qe]);c!==null&&Jm(o,e[Y],a,t,c,s)}let i=t[yt];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Jd(e,t){let n=Gn(e,t);return n!==void 0&&Xo(n[w],n),n}function Gn(e,t){if(e.length<=$)return;let n=$+t,r=e[n];if(r){let o=r[ze];o!==null&&o!==e&&Za(o,r),t>0&&(e[n-1][se]=r[se]);let i=Nn(e,$+t);Ym(r[w],r);let s=i[we];s!==null&&s.detachView(i[w]),r[H]=null,r[se]=null,r[y]&=-129}return r}function Iy(e,t,n,r){let o=$+r,i=n.length;r>0&&(n[o-1][se]=t),r<i-$?(t[se]=n[o],es(n,$+r,t)):(n.push(t),t[se]=null),t[H]=n;let s=t[ze];s!==null&&n!==s&&Kd(s,t);let a=t[we];a!==null&&a.insertView(e),lo(t),t[y]|=128}function Kd(e,t){let n=e[wt],r=t[H];if(Ce(r))e[y]|=2;else{let o=r[H][J];t[J]!==o&&(e[y]|=2)}n===null?e[wt]=[t]:n.push(t)}var et=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[w];return Wn(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[P]}set context(t){this._lView[P]=t}get destroyed(){return Ct(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[H];if(fe(t)){let n=t[Rn],r=n?n.indexOf(this):-1;r>-1&&(Gn(t,r),Nn(n,r))}this._attachedToViewContainer=!1}Xo(this._lView[w],this._lView)}onDestroy(t){Es(this._lView,t)}markForCheck(){Ka(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[y]&=-129}reattach(){lo(this._lView),this._lView[y]|=128}detectChanges(){this._lView[y]|=1024,Ja(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[vt].get(Qm,vu)}catch{this.exhaustive=vu}}attachToViewContainerRef(){if(this._appRef)throw new M(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Qt(this._lView),n=this._lView[ze];n!==null&&!t&&Za(n,this._lView),Pd(this._lView[w],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new M(902,!1);this._appRef=t;let n=Qt(this._lView),r=this._lView[ze];r!==null&&!n&&Kd(r,this._lView),lo(this._lView)}};var zn=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=Ey;constructor(n,r,o){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,o){let i=tr(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:o});return new et(i)}}return e})();function Ey(){return ni(U(),E())}function ni(e,t){return e.type&4?new zn(t,e,sn(e,t)):null}function rr(e,t,n,r,o){let i=e.data[t];if(i===null)i=Dy(e,t,n,r,o),Wl()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=$l();i.injectorIndex=s===null?-1:s.injectorIndex}return Je(i,!0),i}function Dy(e,t,n,r,o){let i=bs(),s=fo(),a=s?i:i&&i.parent,c=e.data[t]=Cy(e,a,n,t,r,o);return wy(e,c,i,s),c}function wy(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Cy(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Cs()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var gx=new RegExp(`^(\\d+)*(${Og}|${Rg})*(.*)`);function by(e){let t=e[ps]??[],r=e[H][O],o=[];for(let i of t)i.data[cd]!==void 0?o.push(i):Ty(i,r);e[ps]=o}function Ty(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[ad];for(;n<o;){let i=r.nextSibling;Md(t,r,!1),r=i,n++}}}var _y=()=>null,My=()=>null;function Lo(e,t){return _y(e,t)}function Xd(e,t,n){return My(e,t,n)}var ef=class{},ri=class{},fa=class{resolveComponentFactory(t){throw new M(917,!1)}},or=class{static NULL=new fa},Qn=class{},Ny=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>xy()}return e})();function xy(){let e=E(),t=U(),n=ce(t.index,e);return(Ce(n)?n:e)[O]}var tf=(()=>{class e{static \u0275prov=B({token:e,providedIn:"root",factory:()=>null})}return e})();var Mo={},pa=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,Mo,r);return o!==Mo||n===Mo?o:this.parentInjector.get(t,n,r)}};function ha(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Kr(o,a);else if(i==2){let c=a,l=t[++s];r=Kr(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function ir(e,t=0){let n=E();if(n===null)return Ee(e,t);let r=U();return Zu(r,n,G(e),t)}function Sy(){let e="invalid";throw new Error(e)}function Xa(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,l=null;for(let u of s)if(u.resolveHostDirectives!==null){[a,c,l]=u.resolveHostDirectives(s);break}Ay(e,t,n,a,i,c,l)}i!==null&&r!==null&&Ry(n,r,i)}function Ry(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new M(-301,!1);r.push(t[o],i)}}function Oy(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Ay(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&be(f)&&(c=!0,Oy(e,n,p)),Ys(Oo(n,t),e,f.type)}Vy(n,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let l=!1,u=!1,d=Sd(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(n.mergedAttrs=Kt(n.mergedAttrs,f.hostAttrs),Py(e,n,t,d,f),jy(d,f,o),s!==null&&s.has(f)){let[g,N]=s.get(f);n.directiveToIndex.set(f.type,[d,g+n.directiveStart,N+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let h=f.type.prototype;!l&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),d++}ky(e,n,i)}function ky(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Eu(0,t,o,r),Eu(1,t,o,r),wu(t,r,!1);else{let i=n.get(o);Du(0,t,i,r),Du(1,t,i,r),wu(t,r,!0)}}}function Eu(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),nf(t,i)}}function Du(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),nf(t,s)}}function nf(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function wu(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Ha(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function Py(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Ue(o.type,!0)),s=new Nt(i,be(o),ir);e.blueprint[r]=s,n[r]=s,Ly(e,t,r,Sd(e,n,o.hostVars,ne),o)}function Ly(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Fy(s)!=a&&s.push(a),s.push(n,r,i)}}function Fy(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function jy(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;be(t)&&(n[""]=e)}}function Vy(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function rf(e,t,n,r,o,i,s,a){let c=t.consts,l=he(c,s),u=rr(t,e,2,r,l);return i&&Xa(t,n,u,he(c,a),o),u.mergedAttrs=Kt(u.mergedAttrs,u.attrs),u.attrs!==null&&ha(u,u.attrs,!1),u.mergedAttrs!==null&&ha(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function of(e,t){ka(e,t),ao(t)&&e.queries.elementEnd(t)}function sf(e,t,n){return e[t]=n}function Hy(e,t){return e[t]}function Me(e,t,n){if(n===ne)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Bs(e,t,n){return function r(o){let i=Ze(e)?ce(e.index,t):t;Ka(i,5);let s=t[P],a=Cu(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=Cu(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Cu(e,t,n,r){let o=I(null);try{return S(6,t,n),n(r)!==!1}catch(i){return qm(e,i),!1}finally{S(7,t,n),I(o)}}function By(e,t,n,r,o,i,s,a){let c=On(e),l=!1,u=null;if(!r&&c&&(u=$y(t,n,i,e.index)),u!==null){let d=u.__ngLastListenerFn__||u;d.__ngNextListenerFn__=s,u.__ngLastListenerFn__=s,l=!0}else{let d=pe(e,n),p=r?r(d):d;kg(n,p,i,a);let f=o.listen(p,i,a),h=r?g=>r(ae(g[e.index])):e.index;af(h,t,n,i,a,f,!1)}return l}function $y(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Gt],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function af(e,t,n,r,o,i,s){let a=t.firstCreatePass?ws(t):null,c=Ds(n),l=c.length;c.push(o,i),a&&a.push(r,e,l,(l+1)*(s?-1:1))}function bu(e,t,n,r,o,i){let s=t[n],a=t[w],l=a.data[n].outputs[r],d=s[l].subscribe(i);af(e.index,a,t,o,i,d,!0)}var ga=Symbol("BINDING");var Fo=class extends or{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Ae(t);return new xt(n,this.ngModule)}};function Uy(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&Yo.SignalBased)!==0};return o&&(i.transform=o),i})}function qy(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function Wy(e,t,n){let r=t instanceof oe?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new pa(n,r):n}function Gy(e){let t=e.get(Qn,null);if(t===null)throw new M(407,!1);let n=e.get(tf,null),r=e.get(ie,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function zy(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Td(t,n,n==="svg"?hs:n==="math"?xl:null)}var xt=class extends ri{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Uy(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=qy(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=wm(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){S(22);let a=I(null);try{let c=this.componentDef,l=Qy(r,c,s,i),u=Wy(c,o||this.ngModule,t),d=Gy(u),p=d.rendererFactory.createRenderer(null,c),f=r?Am(p,r,c.encapsulation,u):zy(c,p),h=s?.some(Tu)||i?.some(b=>typeof b!="function"&&b.bindings.some(Tu)),g=$a(null,l,null,512|xd(c),null,null,d,p,u,null,dd(f,u,!0));g[F]=f,mo(g);let N=null;try{let b=rf(F,l,g,"#host",()=>l.directiveRegistry,!0,0);f&&(Nd(p,f,b),an(f,g)),Jo(l,g,b),Fa(l,b,g),of(l,b),n!==void 0&&Yy(b,this.ngContentSelectors,n),N=ce(b.index,g),g[P]=N[P],za(l,g,null)}catch(b){throw N!==null&&Ks(N),Ks(g),b}finally{S(23),yo()}return new jo(this.componentType,g,!!h)}finally{I(a)}}};function Qy(e,t,n,r){let o=e?["ng-version","20.0.4"]:Cm(t.selectors[0]),i=null,s=null,a=0;if(n)for(let u of n)a+=u[ga].requiredVars,u.create&&(u.targetIdx=0,(i??=[]).push(u)),u.update&&(u.targetIdx=0,(s??=[]).push(u));if(r)for(let u=0;u<r.length;u++){let d=r[u];if(typeof d!="function")for(let p of d.bindings){a+=p[ga].requiredVars;let f=u+1;p.create&&(p.targetIdx=f,(i??=[]).push(p)),p.update&&(p.targetIdx=f,(s??=[]).push(p))}}let c=[t];if(r)for(let u of r){let d=typeof u=="function"?u:u.type,p=os(d);c.push(p)}return Ba(0,null,Zy(i,s),1,a,c,null,null,null,[o],null)}function Zy(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function Tu(e){let t=e[ga].kind;return t==="input"||t==="twoWay"}var jo=class extends ef{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=An(n[w],F),this.location=sn(this._tNode,n),this.instance=ce(this._tNode.index,n)[P],this.hostView=this.changeDetectorRef=new et(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Ga(r,o[w],o,t,n);this.previousInputValues.set(t,n);let s=ce(r.index,o);Ka(s,1)}get injector(){return new Mt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Yy(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var oi=(()=>{class e{static __NG_ELEMENT_ID__=Jy}return e})();function Jy(){let e=U();return lf(e,E())}var Ky=oi,cf=class extends Ky{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return sn(this._hostTNode,this._hostLView)}get injector(){return new Mt(this._hostTNode,this._hostLView)}get parentInjector(){let t=Pa(this._hostTNode,this._hostLView);if(Uu(t)){let n=So(t,this._hostLView),r=xo(t),o=n[w].data[r+8];return new Mt(o,n)}else return new Mt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=_u(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-$}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Lo(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,en(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!Xh(t),l;if(c)l=n;else{let N=n||{};l=N.index,r=N.injector,o=N.projectableNodes,i=N.environmentInjector||N.ngModuleRef,s=N.directives,a=N.bindings}let u=c?t:new xt(Ae(t)),d=r||this.parentInjector;if(!i&&u.ngModule==null){let b=(c?d:this.parentInjector).get(oe,null);b&&(i=b)}let p=Ae(u.componentType??{}),f=Lo(this._lContainer,p?.id??null),h=f?.firstChild??null,g=u.create(d,o,h,i,s,a);return this.insertImpl(g.hostView,l,en(this._hostTNode,f)),g}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Rl(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[H],l=new cf(c,c[Y],c[H]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return nr(s,o,i,r),t.attachToViewContainerRef(),es($s(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=_u(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Gn(this._lContainer,n);r&&(Nn($s(this._lContainer),n),Xo(r[w],r))}detach(t){let n=this._adjustIndex(t,-1),r=Gn(this._lContainer,n);return r&&Nn($s(this._lContainer),n)!=null?new et(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function _u(e){return e[Rn]}function $s(e){return e[Rn]||(e[Rn]=[])}function lf(e,t){let n,r=t[e.index];return fe(r)?n=r:(n=Zd(r,t,null,e),t[e.index]=n,Ua(t,n)),ev(n,t,e,r),new cf(n,e,t)}function Xy(e,t){let n=e[O],r=n.createComment(""),o=pe(t,e),i=n.parentNode(o);return ko(n,i,r,n.nextSibling(o),!1),r}var ev=rv,tv=()=>!1;function nv(e,t,n){return tv(e,t,n)}function rv(e,t,n,r){if(e[Qe])return;let o;n.type&8?o=ae(r):o=Xy(t,n),e[Qe]=o}var ma=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},ya=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)tc(t,n).matches!==null&&this.queries[n].setDirty()}},Vo=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=cv(t):this.predicate=t}},va=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Ia=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,ov(n,i)),this.matchTNodeWithReadOption(t,n,_o(n,t,i,!1,!1))}else r===zn?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,_o(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Kn||o===oi||o===zn&&n.type&4)this.addMatch(n.index,-2);else{let i=_o(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function ov(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function iv(e,t){return e.type&11?sn(e,t):e.type&4?ni(e,t):null}function sv(e,t,n,r){return n===-1?iv(t,e):n===-2?av(e,t,r):qn(e,e[w],n,t)}function av(e,t,n){if(n===Kn)return sn(t,e);if(n===zn)return ni(t,e);if(n===oi)return lf(t,e)}function uf(e,t,n,r){let o=t[we].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(sv(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Ea(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=uf(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let d=$;d<u.length;d++){let p=u[d];p[ze]===p[H]&&Ea(p[w],p,l,r)}if(u[wt]!==null){let d=u[wt];for(let p=0;p<d.length;p++){let f=d[p];Ea(f[w],f,l,r)}}}}}return r}function ec(e,t){return e[we].queries[t].queryList}function df(e,t,n){let r=new Ao((n&4)===4);return kl(e,t,r,r.destroy),(t[we]??=new ya).queries.push(new ma(r))-1}function ff(e,t,n){let r=A();return r.firstCreatePass&&(hf(r,new Vo(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),df(r,E(),t)}function pf(e,t,n,r){let o=A();if(o.firstCreatePass){let i=U();hf(o,new Vo(t,n,r),i.index),lv(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return df(o,E(),n)}function cv(e){return e.split(",").map(t=>t.trim())}function hf(e,t,n){e.queries===null&&(e.queries=new va),e.queries.track(new Ia(t,n))}function lv(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function tc(e,t){return e.queries.getByIndex(t)}function gf(e,t){let n=e[w],r=tc(n,t);return r.crossesNgTemplate?Ea(n,e,t,[]):uf(n,e,r,t)}function mf(e,t,n){let r,o=En(()=>{r._dirtyCounter();let i=uv(r,e);if(t&&i===void 0)throw new M(-951,!1);return i});return r=o[V],r._dirtyCounter=vo(0),r._flatValue=void 0,o}function nc(e){return mf(!0,!1,e)}function rc(e){return mf(!0,!0,e)}function yf(e,t){let n=e[V];n._lView=E(),n._queryIndex=t,n._queryList=ec(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function uv(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[y]&4)return t?void 0:z;let o=ec(n,r),i=gf(n,r);return o.reset(i,Xu),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}var Mu=new Set;function tt(e){Mu.has(e)||(Mu.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var tn=class{},vf=class{};var Ho=class extends tn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Fo(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=rs(t);this._bootstrapComponents=wd(i.bootstrap),this._r3Injector=Rs(t,n,[{provide:tn,useValue:this},{provide:or,useValue:this.componentFactoryResolver},...r],ee(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Bo=class extends vf{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Ho(this.moduleType,t,[])}};var Zn=class extends tn{injector;componentFactoryResolver=new Fo(this);instance=null;constructor(t){super();let n=new gt([...t.providers,{provide:tn,useValue:this},{provide:or,useValue:this.componentFactoryResolver}],t.parent||Wt(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function If(e,t,n=null){return new Zn({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var dv=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=is(!1,n.type),o=r.length>0?If([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=B({token:e,providedIn:"environment",factory:()=>new e(Ee(oe))})}return e})();function fv(e){return on(()=>{let t=Ef(e),n=W(q({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===La.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(dv).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Xt.Emulated,styles:e.styles||z,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&tt("NgStandalone"),Df(n);let r=e.dependencies;return n.directiveDefs=Nu(r,!1),n.pipeDefs=Nu(r,!0),n.id=Ev(n),n})}function pv(e){return Ae(e)||os(e)}function hv(e){return e!==null}function gv(e){return on(()=>({type:e.type,bootstrap:e.bootstrap||z,declarations:e.declarations||z,imports:e.imports||z,exports:e.exports||z,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function mv(e,t){if(e==null)return We;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Yo.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function yv(e){if(e==null)return We;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function vv(e){return on(()=>{let t=Ef(e);return Df(t),t})}function Iv(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Ef(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||We,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||z,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:mv(e.inputs,t),outputs:yv(e.outputs),debugInfo:null}}function Df(e){e.features?.forEach(t=>t(e))}function Nu(e,t){if(!e)return null;let n=t?wl:pv;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(hv)}function Ev(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Dv(e){return Object.getPrototypeOf(e.prototype).constructor}function wf(e){let t=Dv(e.type),n=!0,r=[e];for(;t;){let o;if(be(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new M(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Us(e.inputs),s.declaredInputs=Us(e.declaredInputs),s.outputs=Us(e.outputs);let a=o.hostBindings;a&&_v(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&bv(e,c),l&&Tv(e,l),wv(e,o),pl(e.outputs,o.outputs),be(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===wf&&(n=!1)}}t=Object.getPrototypeOf(t)}Cv(r)}function wv(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function Cv(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Kt(o.hostAttrs,n=Kt(n,o.hostAttrs))}}function Us(e){return e===We?{}:e===z?[]:e}function bv(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Tv(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function _v(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Mv(e,t,n,r,o,i,s,a,c){let l=t.consts,u=rr(t,e,4,s||null,a||null);uo()&&Xa(t,n,u,he(l,c),Wa),u.mergedAttrs=Kt(u.mergedAttrs,u.attrs),ka(t,u);let d=u.tView=Ba(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function nn(e,t,n,r,o,i,s,a,c,l,u){let d=n+F,p=t.firstCreatePass?Mv(d,t,e,r,o,i,s,a,l):t.data[d];c&&(p.flags|=c),Je(p,!1);let f=Nv(t,e,p,n);jn()&&ei(t,e,f,p),an(f,e);let h=Zd(f,e,f,p);return e[d]=h,Ua(e,h),nv(h,p,e),On(p)&&Jo(t,e,p),l!=null&&qa(e,p,u),p}function Cf(e,t,n,r,o,i,s,a){let c=E(),l=A(),u=he(l.consts,i);return nn(c,l,e,t,n,r,o,u,void 0,s,a),Cf}var Nv=xv;function xv(e,t,n,r){return Vn(!0),t[O].createComment("")}var ii=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(ii||{}),cn=new x(""),bf=!1,Da=class extends re{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,ls()&&(this.destroyRef=m(Le,{optional:!0})??void 0,this.pendingTasks=m(Tt,{optional:!0})??void 0)}emit(t){let n=I(null);try{super.next(t)}finally{I(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof L&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},Fe=Da;function Tf(e){let t,n;function r(){e=_t;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function xu(e){return queueMicrotask(()=>e()),()=>{e=_t}}var oc="isAngularZone",$o=oc+"_ID",Sv=0,K=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new Fe(!1);onMicrotaskEmpty=new Fe(!1);onStable=new Fe(!1);onError=new Fe(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=bf}=t;if(typeof Zone>"u")throw new M(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Av(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(oc)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new M(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new M(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Rv,_t,_t);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Rv={};function ic(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ov(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Tf(()=>{e.callbackScheduled=!1,wa(e),e.isCheckStableRunning=!0,ic(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),wa(e)}function Av(e){let t=()=>{Ov(e)},n=Sv++;e._inner=e._inner.fork({name:"angular",properties:{[oc]:!0,[$o]:n,[$o+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(kv(c))return r.invokeTask(i,s,a,c);try{return Su(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Ru(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return Su(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Pv(c)&&t(),Ru(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,wa(e),ic(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function wa(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Su(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Ru(e){e._nesting--,ic(e)}var Uo=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new Fe;onMicrotaskEmpty=new Fe;onStable=new Fe;onError=new Fe;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function kv(e){return _f(e,"__ignore_ng_zone__")}function Pv(e){return _f(e,"__scheduler_tick__")}function _f(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var si=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=B({token:e,providedIn:"root",factory:()=>new e})}return e})(),sc=[0,1,2,3],ac=(()=>{class e{ngZone=m(K);scheduler=m(ie);errorHandler=m(Oe,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){m(cn,{optional:!0})}execute(){let n=this.sequences.size>0;n&&S(16),this.executing=!0;for(let r of sc)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&S(17)}register(n){let{view:r}=n;r!==void 0?((r[Dt]??=[]).push(n),Ye(r),r[y]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(ii.AFTER_NEXT_RENDER,n):n()}static \u0275prov=B({token:e,providedIn:"root",factory:()=>new e})}return e})(),Yn=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[Dt];t&&(this.view[Dt]=t.filter(n=>n!==this))}};function Mf(e,t){!t?.injector&&us(Mf);let n=t?.injector??m(ue);return tt("NgAfterNextRender"),Fv(e,n,t,!0)}function Lv(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Fv(e,t,n,r){let o=t.get(si);o.impl??=t.get(ac);let i=t.get(cn,null,{optional:!0}),s=n?.manualCleanup!==!0?t.get(Le):null,a=t.get(bt,null,{optional:!0}),c=new Yn(o.impl,Lv(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}var jv=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Nf=new x("");function cc(e){return!!e&&typeof e.then=="function"}function xf(e){return!!e&&typeof e.subscribe=="function"}var Sf=new x("");var lc=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=m(Sf,{optional:!0})??[];injector=m(ue);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=io(this.injector,o);if(cc(i))n.push(i);else if(xf(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Rf=new x("");function Of(){Ri(()=>{let e="";throw new M(600,e)})}function Af(e){return e.isBoundToModule}var Vv=10;var sr=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=m(Te);afterRenderManager=m(si);zonelessEnabled=m(Hn);rootEffectScheduler=m(Bn);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new re;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=m(Tt);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(He(n=>!n))}constructor(){m(cn,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=m(oe);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=ue.NULL){return this._injector.get(K).run(()=>{S(10);let s=n instanceof ri;if(!this._injector.get(lc).done){let h="";throw new M(405,h)}let c;s?c=n:c=this._injector.get(or).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let l=Af(c)?void 0:this._injector.get(tn),u=r||c.selector,d=c.create(o,[],u,l),p=d.location.nativeElement,f=d.injector.get(Nf,null);return f?.registerApplication(p),d.onDestroy(()=>{this.detachView(d.hostView),Un(this.components,d),f?.unregisterApplication(p)}),this._loadComponent(d),S(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){S(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(ii.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new M(101,!1);let n=I(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,I(n),this.afterTick.next(),S(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Qn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<Vv;)S(14),this.synchronizeOnce(),S(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!kn(o))continue;let i=r&&!this.zonelessEnabled?0:1;Ja(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>kn(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Un(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(Rf,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Un(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new M(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Un(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function kf(e,t,n,r){let o=E(),i=Ke();if(Me(o,i,t)){let s=A(),a=Fn();Bm(a,o,e,t,n,r)}return kf}var Ca=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function qs(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function Hv(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let l=e.at(i),u=t[i],d=qs(i,l,i,u,n);if(d!==0){d<0&&e.updateValue(i,u),i++;continue}let p=e.at(s),f=t[c],h=qs(s,p,c,f,n);if(h!==0){h<0&&e.updateValue(s,f),s--,c--;continue}let g=n(i,l),N=n(s,p),b=n(i,u);if(Object.is(b,N)){let ye=n(c,f);Object.is(ye,g)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,u),i++;continue}if(r??=new qo,o??=Au(e,i,s,n),ba(e,r,i,b))e.updateValue(i,u),i++,s++;else if(o.has(b))r.set(g,e.detach(i)),s--;else{let ye=e.create(i,t[i]);e.attach(i,ye),i++,s++}}for(;i<=c;)Ou(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),l=c.next();for(;!l.done&&i<=s;){let u=e.at(i),d=l.value,p=qs(i,u,i,d,n);if(p!==0)p<0&&e.updateValue(i,d),i++,l=c.next();else{r??=new qo,o??=Au(e,i,s,n);let f=n(i,d);if(ba(e,r,i,f))e.updateValue(i,d),i++,s++,l=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,l=c.next();else{let h=n(i,u);r.set(h,e.detach(i)),s--}}}for(;!l.done;)Ou(e,r,n,e.length,l.value),l=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function ba(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function Ou(e,t,n,r,o){if(ba(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Au(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var qo=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function Bv(e,t,n,r,o,i,s,a){tt("NgControlFlow");let c=E(),l=A(),u=he(l.consts,i);return nn(c,l,e,t,n,r,o,u,256,s,a),uc}function uc(e,t,n,r,o,i,s,a){tt("NgControlFlow");let c=E(),l=A(),u=he(l.consts,i);return nn(c,l,e,t,n,r,o,u,512,s,a),uc}function $v(e,t){tt("NgControlFlow");let n=E(),r=Ke(),o=n[r]!==ne?n[r]:-1,i=o!==-1?Wo(n,F+o):void 0,s=0;if(Me(n,r,e)){let a=I(null);try{if(i!==void 0&&Jd(i,s),e!==-1){let c=F+e,l=Wo(n,c),u=Na(n[w],c),d=Xd(l,u,n),p=tr(n,u,t,{dehydratedView:d});nr(l,p,s,en(u,d))}}finally{I(a)}}else if(i!==void 0){let a=Yd(i,s);a!==void 0&&(a[P]=t)}}var Ta=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-$}};function Uv(e,t){return t}var _a=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function qv(e,t,n,r,o,i,s,a,c,l,u,d,p){tt("NgControlFlow");let f=E(),h=A(),g=c!==void 0,N=E(),b=a?s.bind(N[J][P]):s,ye=new _a(g,b);N[F+e]=ye,nn(f,h,e+1,t,n,r,o,he(h.consts,i),256),g&&nn(f,h,e+2,c,l,u,d,he(h.consts,p),512)}var Ma=class extends Ca{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-$}at(t){return this.getLView(t)[P].$implicit}attach(t,n){let r=n[yt];this.needsIndexUpdate||=t!==this.length,nr(this.lContainer,n,t,en(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,Gv(this.lContainer,t)}create(t,n){let r=Lo(this.lContainer,this.templateTNode.tView.ssrId),o=tr(this.hostLView,this.templateTNode,new Ta(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){Xo(t[w],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[P].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[P].$index=t}getLView(t){return zv(this.lContainer,t)}};function Wv(e){let t=I(null),n=Pe();try{let r=E(),o=r[w],i=r[n],s=n+1,a=Wo(r,s);if(i.liveCollection===void 0){let l=Na(o,s);i.liveCollection=new Ma(a,r,l)}else i.liveCollection.reset();let c=i.liveCollection;if(Hv(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let l=Ke(),u=c.length===0;if(Me(r,l,u)){let d=n+2,p=Wo(r,d);if(u){let f=Na(o,d),h=Xd(p,f,r),g=tr(r,f,void 0,{dehydratedView:h});nr(p,g,0,en(f,h))}else o.firstUpdatePass&&by(p),Jd(p,0)}}}finally{I(t)}}function Wo(e,t){return e[t]}function Gv(e,t){return Gn(e,t)}function zv(e,t){return Yd(e,t)}function Na(e,t){return An(e,t)}function Pf(e,t,n){let r=E(),o=Ke();if(Me(r,o,t)){let i=A(),s=Fn();Ad(s,r,e,t,r[O],n)}return Pf}function xa(e,t,n,r,o){Ga(t,e,n,o?"class":"style",r)}function dc(e,t,n,r){let o=E(),i=A(),s=F+e,a=o[O],c=i.firstCreatePass?rf(s,i,o,t,Wa,uo(),n,r):i.data[s],l=Qv(i,o,c,a,t,e);o[s]=l;let u=On(c);return Je(c,!0),Nd(a,l,c),!Ko(c)&&jn()&&ei(i,o,l,c),(Pl()===0||u)&&an(l,o),Ll(),u&&(Jo(i,o,c),Fa(i,c,o)),r!==null&&qa(o,c),dc}function fc(){let e=U();fo()?po():(e=e.parent,Je(e,!1));let t=e;jl(t)&&Vl(),Fl();let n=A();return n.firstCreatePass&&of(n,t),t.classesWithoutHost!=null&&sg(t)&&xa(n,t,E(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&ag(t)&&xa(n,t,E(),t.stylesWithoutHost,!1),fc}function Lf(e,t,n,r){return dc(e,t,n,r),fc(),Lf}var Qv=(e,t,n,r,o,i)=>(Vn(!0),Td(r,o,tu()));function Zv(e,t,n,r,o){let i=t.consts,s=he(i,r),a=rr(t,e,8,"ng-container",s);s!==null&&ha(a,s,!0);let c=he(i,o);return uo()&&Xa(t,n,a,c,Wa),a.mergedAttrs=Kt(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function pc(e,t,n){let r=E(),o=A(),i=e+F,s=o.firstCreatePass?Zv(i,o,r,t,n):o.data[i];Je(s,!0);let a=Yv(o,r,s,e);return r[i]=a,jn()&&ei(o,r,a,s),an(a,r),On(s)&&(Jo(o,r,s),Fa(o,s,r)),n!=null&&qa(r,s),pc}function hc(){let e=U(),t=A();return fo()?po():(e=e.parent,Je(e,!1)),t.firstCreatePass&&(ka(t,e),ao(e)&&t.queries.elementEnd(e)),hc}function Ff(e,t,n){return pc(e,t,n),hc(),Ff}var Yv=(e,t,n,r)=>(Vn(!0),_m(t[O],""));function Jv(){return E()}function jf(e,t,n){let r=E(),o=Ke();if(Me(r,o,t)){let i=A(),s=Fn();kd(s,r,e,t,r[O],n)}return jf}var ar="en-US";var Kv=ar;function Vf(e){typeof e=="string"&&(Kv=e.toLowerCase().replace(/_/g,"-"))}function Hf(e,t,n){let r=E(),o=A(),i=U();return Bf(o,r,r[O],i,e,t,n),Hf}function Bf(e,t,n,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=Bs(r,t,i),By(r,e,t,s,n,o,i,c)&&(a=!1)),a){let l=r.outputs?.[o],u=r.hostDirectiveOutputs?.[o];if(u&&u.length)for(let d=0;d<u.length;d+=2){let p=u[d],f=u[d+1];c??=Bs(r,t,i),bu(r,t,p,f,o,c)}if(l&&l.length)for(let d of l)c??=Bs(r,t,i),bu(r,t,d,o,o,c)}}function Xv(e=1){return Kl(e)}function eI(e,t){let n=null,r=ym(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?bd(e,i,!0):Em(r,i))return o}return n}function tI(e){let t=E()[J][Y];if(!t.projection){let n=e?e.length:1,r=t.projection=El(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?eI(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function nI(e,t=0,n,r,o,i){let s=E(),a=A(),c=r?e+1:null;c!==null&&nn(s,a,c,r,o,i,null,n);let l=rr(a,F+e,16,null,n||null);l.projection===null&&(l.projection=t),po();let d=!s[yt]||Cs();s[J][Y].projection[l.projection]===null&&c!==null?rI(s,a,c):d&&!Ko(l)&&oy(a,s,l)}function rI(e,t,n){let r=F+n,o=t.data[r],i=e[r],s=Lo(i,o.tView.ssrId),a=tr(e,o,void 0,{dehydratedView:s});nr(i,a,0,en(o,s))}function oI(e,t,n,r){pf(e,t,n,r)}function iI(e,t,n){ff(e,t,n)}function sI(e){let t=E(),n=A(),r=go();Ln(r+1);let o=tc(n,r);if(e.dirty&&Sl(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=gf(t,r);e.reset(i,Xu),e.notifyOnChanges()}return!0}return!1}function aI(){return ec(E(),go())}function cI(e,t,n,r,o){yf(t,pf(e,n,r,o))}function lI(e,t,n,r){yf(e,ff(t,n,r))}function uI(e=1){Ln(go()+e)}function dI(e){let t=Ul();return ms(t,F+e)}function Co(e,t){return e<<17|t<<2}function St(e){return e>>17&32767}function fI(e){return(e&2)==2}function pI(e,t){return e&131071|t<<17}function Sa(e){return e|2}function rn(e){return(e&131068)>>2}function Ws(e,t){return e&-131069|t<<2}function hI(e){return(e&1)===1}function Ra(e){return e|1}function gI(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=St(s),c=rn(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||qt(d,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=St(e[a+1]);e[r+1]=Co(p,a),p!==0&&(e[p+1]=Ws(e[p+1],r)),e[a+1]=pI(e[a+1],r)}else e[r+1]=Co(a,0),a!==0&&(e[a+1]=Ws(e[a+1],r)),a=r;else e[r+1]=Co(c,0),a===0?a=r:e[c+1]=Ws(e[c+1],r),c=r;l&&(e[r+1]=Sa(e[r+1])),ku(e,u,r,!0),ku(e,u,r,!1),mI(t,u,e,r,i),s=Co(a,c),i?t.classBindings=s:t.styleBindings=s}function mI(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&qt(i,t)>=0&&(n[r+1]=Ra(n[r+1]))}function ku(e,t,n,r){let o=e[n+1],i=t===null,s=r?St(o):rn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];yI(c,t)&&(a=!0,e[s+1]=r?Ra(l):Sa(l)),s=r?St(l):rn(l)}a&&(e[n+1]=r?Sa(o):Ra(o))}function yI(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?qt(e,t)>=0:!1}var me={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function vI(e){return e.substring(me.key,me.keyEnd)}function II(e){return EI(e),$f(e,Uf(e,0,me.textEnd))}function $f(e,t){let n=me.textEnd;return n===t?-1:(t=me.keyEnd=DI(e,me.key=t,n),Uf(e,t,n))}function EI(e){me.key=0,me.keyEnd=0,me.value=0,me.valueEnd=0,me.textEnd=e.length}function Uf(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function DI(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function qf(e,t,n){return Gf(e,t,n,!1),qf}function Wf(e,t){return Gf(e,t,null,!0),Wf}function wI(e){bI(SI,CI,e,!0)}function CI(e,t){for(let n=II(t);n>=0;n=$f(t,n))ro(e,vI(t),!0)}function Gf(e,t,n,r){let o=E(),i=A(),s=Ns(2);if(i.firstUpdatePass&&Qf(i,e,s,r),t!==ne&&Me(o,s,t)){let a=i.data[Pe()];Zf(i,a,o,o[O],e,o[s+1]=OI(t,n),r,s)}}function bI(e,t,n,r){let o=A(),i=Ns(2);o.firstUpdatePass&&Qf(o,null,i,r);let s=E();if(n!==ne&&Me(s,i,n)){let a=o.data[Pe()];if(Yf(a,r)&&!zf(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Kr(c,n||"")),xa(o,a,s,n,r)}else RI(o,a,s,s[O],s[i+1],s[i+1]=xI(e,t,n),r,i)}}function zf(e,t){return t>=e.expandoStartIndex}function Qf(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Pe()],s=zf(e,n);Yf(i,r)&&t===null&&!s&&(t=!1),t=TI(o,i,t,r),gI(o,i,t,n,s,r)}}function TI(e,t,n,r){let o=Ql(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Gs(null,e,t,n,r),n=Jn(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Gs(o,e,t,n,r),i===null){let c=_I(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Gs(null,e,t,c[1],r),c=Jn(c,t.attrs,r),MI(e,t,r,c))}else i=NI(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function _I(e,t,n){let r=n?t.classBindings:t.styleBindings;if(rn(r)!==0)return e[St(r)]}function MI(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[St(o)]=r}function NI(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Jn(r,s,n)}return Jn(r,t.attrs,n)}function Gs(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Jn(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Jn(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),ro(e,s,n?!0:t[++i]))}return e===void 0?null:e}function xI(e,t,n){if(n==null||n==="")return z;let r=[],o=Xn(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function SI(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&ro(e,r,n)}function RI(e,t,n,r,o,i,s,a){o===ne&&(o=z);let c=0,l=0,u=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;u!==null||d!==null;){let p=c<o.length?o[c+1]:void 0,f=l<i.length?i[l+1]:void 0,h=null,g;u===d?(c+=2,l+=2,p!==f&&(h=d,g=f)):d===null||u!==null&&u<d?(c+=2,h=u):(l+=2,h=d,g=f),h!==null&&Zf(e,t,n,r,h,g,s,a),u=c<o.length?o[c]:null,d=l<i.length?i[l]:null}}function Zf(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=hI(l)?Pu(c,t,n,o,rn(l),s):void 0;if(!Go(u)){Go(i)||fI(l)&&(i=Pu(c,null,n,o,a,s));let d=gs(Pe(),n);sy(r,s,d,o,i)}}function Pu(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,d=u===null,p=n[o+1];p===ne&&(p=d?z:void 0);let f=d?oo(p,r):u===r?p:void 0;if(l&&!Go(f)&&(f=oo(c,r)),Go(f)&&(a=f,s))return a;let h=e[o+1];o=s?St(h):rn(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=oo(c,r))}return a}function Go(e){return e!==void 0}function OI(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=ee(Xn(e)))),e}function Yf(e,t){return(e.flags&(t?8:16))!==0}function AI(e,t=""){let n=E(),r=A(),o=e+F,i=r.firstCreatePass?rr(r,o,1,t,null):r.data[o],s=kI(r,n,i,t,e);n[o]=s,jn()&&ei(r,n,s,i),Je(i,!1)}var kI=(e,t,n,r,o)=>(Vn(!0),bm(t[O],r));function Jf(e,t,n,r=""){return Me(e,Ke(),n)?t+Mn(n)+r:ne}function Kf(e){return gc("",e),Kf}function gc(e,t,n){let r=E(),o=Jf(r,e,t,n);return o!==ne&&PI(r,Pe(),o),gc}function PI(e,t,n){let r=gs(t,e);Tm(e[O],r,n)}function Xf(e,t,n){ks(t)&&(t=t());let r=E(),o=Ke();if(Me(r,o,t)){let i=A(),s=Fn();Ad(s,r,e,t,r[O],n)}return Xf}function LI(e,t){let n=ks(e);return n&&e.set(t),n}function ep(e,t){let n=E(),r=A(),o=U();return Bf(r,n,n[O],o,e,t),ep}function FI(e,t,n=""){return Jf(E(),e,t,n)}function jI(e,t,n){let r=A();if(r.firstCreatePass){let o=be(e);Oa(n,r.data,r.blueprint,o,!0),Oa(t,r.data,r.blueprint,o,!1)}}function Oa(e,t,n,r,o){if(e=G(e),Array.isArray(e))for(let i=0;i<e.length;i++)Oa(e[i],t,n,r,o);else{let i=A(),s=E(),a=U(),c=ht(e)?e:G(e.provide),l=cs(e),u=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(ht(e)||!e.multi){let f=new Nt(l,o,ir),h=Qs(c,t,o?u:u+p,d);h===-1?(Ys(Oo(a,s),i,c),zs(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=Qs(c,t,u+p,d),h=Qs(c,t,u,u+p),g=f>=0&&n[f],N=h>=0&&n[h];if(o&&!N||!o&&!g){Ys(Oo(a,s),i,c);let b=BI(o?HI:VI,n.length,o,r,l);!o&&N&&(n[h].providerFactory=b),zs(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(b),s.push(b)}else{let b=tp(n[o?h:f],l,!o&&r);zs(i,e,f>-1?f:h,b)}!o&&r&&N&&n[h].componentProviders++}}}function zs(e,t,n,r){let o=ht(t),i=Ml(t);if(o||i){let c=(i?G(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function tp(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Qs(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function VI(e,t,n,r){return Aa(this.multi,[])}function HI(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=qn(n,n[w],this.providerFactory.index,r);i=a.slice(0,s),Aa(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],Aa(o,i);return i}function Aa(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function BI(e,t,n,r,o){let i=new Nt(e,n,ir);return i.multi=[],i.index=t,i.componentProviders=0,tp(i,o,r&&!n),i}function $I(e,t=[]){return n=>{n.providersResolver=(r,o)=>jI(r,o?o(e):e,t)}}function UI(e,t,n){let r=Ms()+e,o=E();return o[r]===ne?sf(o,r,n?t.call(n):t()):Hy(o,r)}function qI(e,t){let n=e[t];return n===ne?void 0:n}function WI(e,t,n,r,o,i){let s=t+n;return Me(e,s,o)?sf(e,s+1,i?r.call(i,o):r(o)):qI(e,s+1)}function GI(e,t){let n=A(),r,o=e+F;n.firstCreatePass?(r=zI(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Ue(r.type,!0)),s,a=Z(ir);try{let c=Ro(!1),l=i();return Ro(c),ys(n,E(),o,l),l}finally{Z(a)}}function zI(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function QI(e,t,n){let r=e+F,o=E(),i=ms(o,r);return ZI(o,r)?WI(o,Ms(),t,i.transform,n,i):i.transform(n)}function ZI(e,t){return e[w].data[t].pure}function YI(e,t){return ni(e,t)}var zo=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},JI=(()=>{class e{compileModuleSync(n){return new Bo(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=rs(n),i=wd(o.declarations).reduce((s,a)=>{let c=Ae(a);return c&&s.push(new xt(c)),s},[]);return new zo(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var KI=(()=>{class e{zone=m(K);changeDetectionScheduler=m(ie);applicationRef=m(sr);applicationErrorHandler=m(Te);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),np=new x("",{factory:()=>!1});function mc({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new K(W(q({},yc()),{scheduleInRootZone:n})),[{provide:K,useFactory:e},{provide:Ge,multi:!0,useFactory:()=>{let r=m(KI,{optional:!0});return()=>r.initialize()}},{provide:Ge,multi:!0,useFactory:()=>{let r=m(eE);return()=>{r.initialize()}}},t===!0?{provide:Ps,useValue:!0}:[],{provide:Ls,useValue:n??bf},{provide:Te,useFactory:()=>{let r=m(K),o=m(oe),i;return s=>{i??=o.get(Oe),r.runOutsideAngular(()=>i.handleError(s))}}}]}function XI(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=mc({ngZoneFactory:()=>{let o=yc(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&tt("NgZone_CoalesceEvent"),new K(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return xn([{provide:np,useValue:!0},{provide:Hn,useValue:!1},r])}function yc(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var eE=(()=>{class e{subscription=new L;initialized=!1;zone=m(K);pendingTasks=m(Tt);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{K.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{K.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var rp=(()=>{class e{applicationErrorHandler=m(Te);appRef=m(sr);taskService=m(Tt);ngZone=m(K);zonelessEnabled=m(Hn);tracing=m(cn,{optional:!0});disableScheduling=m(Ps,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new L;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get($o):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(m(Ls,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Uo||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?xu:Tf;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get($o+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,xu(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=B({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function tE(){return typeof $localize<"u"&&$localize.locale||ar}var vc=new x("",{providedIn:"root",factory:()=>m(vc,{optional:!0,skipSelf:!0})||tE()});function nE(e){return ll(e)}function rE(e,t){return En(e,t?.equal)}var Ic=class{[V];constructor(t){this[V]=t}destroy(){this[V].destroy()}};function oE(e,t){let n=t?.injector??m(ue),r=t?.manualCleanup!==!0?n.get(Le):null,o,i=n.get(bt,null,{optional:!0}),s=n.get(ie);return i!==null?(o=aE(i.view,s,e),r instanceof Tn&&r._lView===i.view&&(r=null)):o=cE(e,n.get(Bn),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new Ic(o)}var op=W(q({},Be),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:_t,run(){if(this.dirty=!1,this.hasRun&&!dt(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Se(this),n=Zt(!1);try{this.maybeCleanup(),this.fn(e)}finally{Zt(n),$e(this,t)}},maybeCleanup(){if(!this.cleanupFns?.length)return;let e=I(null);try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[],I(e)}}}),iE=W(q({},op),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){Ht(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),sE=W(q({},op),{consumerMarkedDirty(){this.view[y]|=8192,Ye(this.view),this.notifier.notify(13)},destroy(){Ht(this),this.onDestroyFn(),this.maybeCleanup(),this.view[ke]?.delete(this)}});function aE(e,t,n){let r=Object.create(sE);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[ke]??=new Set,e[ke].add(r),r.consumerMarkedDirty(r),r}function cE(e,t,n){let r=Object.create(iE);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.add(r),r.notifier.notify(12),r}var lp=Symbol("InputSignalNode#UNSET"),uE=W(q({},Dn),{transformFn:void 0,applyValueToInputSignal(e,t){Bt(e,t)}});function up(e,t){let n=Object.create(uE);n.value=e,n.transformFn=t?.transform;function r(){if(ut(n),n.value===lp){let o=null;throw new M(-950,o)}return n.value}return r[V]=n,r}var ip=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>Ku(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},dE=new x("");dE.__NG_ELEMENT_ID__=e=>{let t=U();if(t===null)throw new M(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new M(204,!1)};function sp(e,t){return up(e,t)}function fE(e){return up(lp,e)}var fP=(sp.required=fE,sp);function ap(e,t){return nc(t)}function pE(e,t){return rc(t)}var pP=(ap.required=pE,ap);function cp(e,t){return nc(t)}function hE(e,t){return rc(t)}var hP=(cp.required=hE,cp);var Dc=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},gP=new Dc("20.0.4");var wc=new x(""),gE=new x("");function cr(e){return!e.moduleRef}function mE(e){let t=cr(e)?e.r3Injector:e.moduleRef.injector,n=t.get(K);return n.run(()=>{cr(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Te),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),cr(e)){let i=()=>t.destroy(),s=e.platformInjector.get(wc);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(wc);s.add(i),e.moduleRef.onDestroy(()=>{Un(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return vE(r,n,()=>{let i=t.get(lc);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(vc,ar);if(Vf(s||ar),!t.get(gE,!0))return cr(e)?t.get(sr):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(cr(e)){let c=t.get(sr);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return yE?.(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}var yE;function vE(e,t,n){try{let r=n();return cc(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var ai=null;function IE(e=[],t){return ue.create({name:t,providers:[{provide:as,useValue:"platform"},{provide:wc,useValue:new Set([()=>ai=null])},...e]})}function EE(e=[]){if(ai)return ai;let t=IE(e);return ai=t,Of(),DE(t),t}function DE(e){let t=e.get(sd,null);io(e,()=>{t?.forEach(n=>n())})}function mP(){return!1}var yP=(()=>{class e{static __NG_ELEMENT_ID__=wE}return e})();function wE(e){return CE(U(),E(),(e&16)===16)}function CE(e,t,n){if(Ze(e)&&!n){let r=ce(e.index,t);return new et(r,r)}else if(e.type&175){let r=t[J];return new et(r,t)}return null}function vP(e){S(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=EE(r),i=[mc({}),{provide:ie,useExisting:rp},ou,...n||[]],s=new Zn({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return mE({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{S(9)}}function IP(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function EP(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}var Ec=Symbol("NOT_SET"),dp=new Set,bE=W(q({},Dn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,value:Ec,cleanup:null,consumerMarkedDirty(){if(this.sequence.impl.executing){if(this.sequence.lastPhase===null||this.sequence.lastPhase<this.phase)return;this.sequence.erroredOrDestroyed=!0}this.sequence.scheduler.notify(7)},phaseFn(e){if(this.sequence.lastPhase=this.phase,!this.dirty)return this.signal;if(this.dirty=!1,this.value!==Ec&&!dt(this))return this.signal;try{for(let o of this.cleanup??dp)o()}finally{this.cleanup?.clear()}let t=[];e!==void 0&&t.push(e),t.push(this.registerCleanupFn);let n=Se(this),r;try{r=this.userFn.apply(null,t)}finally{$e(this,n)}return(this.value===Ec||!this.equal(this.value,r))&&(this.value=r,this.version++),this.signal}}),Cc=class extends Yn{scheduler;lastPhase=null;nodes=[void 0,void 0,void 0,void 0];constructor(t,n,r,o,i,s=null){super(t,[void 0,void 0,void 0,void 0],r,!1,i,s),this.scheduler=o;for(let a of sc){let c=n[a];if(c===void 0)continue;let l=Object.create(bE);l.sequence=this,l.phase=a,l.userFn=c,l.dirty=!0,l.signal=()=>(ut(l),l.value),l.signal[V]=l,l.registerCleanupFn=u=>(l.cleanup??=new Set).add(u),this.nodes[a]=l,this.hooks[a]=u=>l.phaseFn(u)}}afterRun(){super.afterRun(),this.lastPhase=null}destroy(){super.destroy();for(let t of this.nodes)for(let n of t?.cleanup??dp)n()}};function DP(e,t){let n=t?.injector??m(ue),r=n.get(ie),o=n.get(si),i=n.get(cn,null,{optional:!0});o.impl??=n.get(ac);let s=e;typeof s=="function"&&(s={mixedReadWrite:e});let a=n.get(bt,null,{optional:!0}),c=new Cc(o.impl,[s.earlyRead,s.write,s.mixedReadWrite,s.read],a?.view,r,n.get(Le),i?.snapshot(null));return o.impl.register(c),c}function wP(e,t){let n=Ae(e),r=t.elementInjector||Wt();return new xt(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector,t.directives,t.bindings)}export{q as a,W as b,TE as c,_E as d,Mc as e,L as f,Ep as g,_ as h,mi as i,yi as j,re as k,un as l,fn as m,jt as n,Sp as o,st as p,TD as q,hn as r,gn as s,Ie as t,Rp as u,Op as v,Ap as w,at as x,He as y,Bp as z,ct as A,mn as B,Ar as C,Up as D,qp as E,Ei as F,Wp as G,lt as H,Gp as I,Di as J,zp as K,Qp as L,yn as M,wi as N,Zp as O,Kp as P,Xp as Q,Ci as R,eh as S,th as T,nh as U,Ti as V,rh as W,oh as X,ih as Y,el as Z,Hc as _,tl as $,sh as aa,M as ba,Xr as ca,B as da,gl as ea,mh as fa,x as ga,Ee as ha,m as ia,xn as ja,as as ka,oe as la,io as ma,Hl as na,Bl as oa,Xl as pa,eu as qa,ue as ra,ru as sa,Le as ta,Oe as ua,Te as va,$h as wa,Os as xa,vo as ya,ie as za,Tt as Aa,Fs as Ba,Jh as Ca,Kh as Da,eg as Ea,vg as Fa,Ku as Ga,Kn as Ha,Ao as Ia,Tg as Ja,_g as Ka,sd as La,Ng as Ma,xg as Na,Sg as Oa,Xt as Pa,Xn as Qa,ja as Ra,jg as Sa,Vg as Ta,Hg as Ua,Bg as Va,$g as Wa,Va as Xa,om as Ya,Zo as Za,am as _a,Om as $a,Po as ab,zn as bb,Qn as cb,Ny as db,ir as eb,Sy as fb,oi as gb,tt as hb,tn as ib,vf as jb,If as kb,fv as lb,gv as mb,vv as nb,Iv as ob,wf as pb,Cf as qb,cn as rb,Fe as sb,K as tb,Mf as ub,jv as vb,cc as wb,Rf as xb,sr as yb,kf as zb,Bv as Ab,$v as Bb,Uv as Cb,qv as Db,Wv as Eb,Pf as Fb,dc as Gb,fc as Hb,Lf as Ib,Ff as Jb,Jv as Kb,jf as Lb,Hf as Mb,Xv as Nb,tI as Ob,nI as Pb,oI as Qb,iI as Rb,sI as Sb,aI as Tb,cI as Ub,lI as Vb,uI as Wb,dI as Xb,qf as Yb,Wf as Zb,wI as _b,AI as $b,Kf as ac,gc as bc,Xf as cc,LI as dc,ep as ec,FI as fc,$I as gc,UI as hc,GI as ic,QI as jc,YI as kc,JI as lc,XI as mc,nE as nc,rE as oc,oE as pc,ip as qc,dE as rc,fP as sc,pP as tc,hP as uc,Dc as vc,gP as wc,mP as xc,yP as yc,vP as zc,IP as Ac,EP as Bc,DP as Cc,wP as Dc};
