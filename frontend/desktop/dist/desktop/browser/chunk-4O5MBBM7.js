import{a as F,b as T,c as R,d as B,e as z,f as N,g as U,h as V,x as De,y as Ie,z as Ae}from"./chunk-V3JWCIZ3.js";import{Ga as J,Ha as q,Ia as j,e as w,i as we,ia as Oe,ja as le,oa as ke,qa as G,sa as O,ta as k,ua as D,va as I}from"./chunk-RDAQWP7I.js";import{$ as Ce,$a as a,$b as n,Ab as c,Bb as d,Bc as se,F as _e,Fb as S,Gb as r,Ha as ye,Hb as t,Ib as g,J as xe,Kb as E,Mb as P,Nb as m,Xb as Ee,Yb as ae,Z as ve,Zb as b,_ as oe,_b as A,ac as x,bc as h,da as be,db as Pe,e as H,ea as W,ga as he,ia as u,k as pe,l as ge,lb as v,mb as $,na as f,oa as _,sb as Y,tb as Me,u as ue,y as fe,ya as M,yc as Se,zb as Z}from"./chunk-OZCVVD7X.js";var K=class o{firebaseService=u(j);activePolls=new Map;startPolling(i,e=5*60*1e3,s=2e3){if(this.activePolls.has(i))return this.activePolls.get(i).asObservable();let l=new ge({captureId:i,status:"PENDING",progress:0,message:"Starting analysis..."});this.activePolls.set(i,l);let C=Date.now();return _e(0,s).pipe(ve(()=>this.firebaseService.getAnalysisJobStatus(i)),fe(p=>{let L=Date.now()-C,re=Math.min(L/e*100,95);if(!p)return{captureId:i,status:"PENDING",progress:Math.max(re,10),message:"Waiting for analysis to start...",job:void 0};switch(p.status){case"PENDING":return{captureId:i,status:"PENDING",progress:Math.max(re,25),message:"Analyzing pigeon image...",job:p};case"FINISHED":return{captureId:i,status:"FINISHED",progress:100,message:"Analysis complete! Pigeon captured successfully.",job:p};case"ERROR":return{captureId:i,status:"ERROR",progress:100,message:p.errorMessage||"Analysis failed",job:p,error:p.errorCode};default:return{captureId:i,status:"PENDING",progress:re,message:"Processing...",job:p}}}),xe(p=>(console.error("Polling error:",p),ue({captureId:i,status:"ERROR",progress:100,message:"Failed to check analysis status",error:p.message||"Unknown error"}))),Ce(p=>{let L=p.status==="PENDING"&&Date.now()-C<e;return l.next(p),L||setTimeout(()=>{this.activePolls.delete(i),l.complete()},1e3),L},!0)).subscribe({next:p=>{},error:p=>{console.error("Polling subscription error:",p),l.error(p),this.activePolls.delete(i)}}),l.asObservable()}stopPolling(i){let e=this.activePolls.get(i);e&&(e.complete(),this.activePolls.delete(i))}stopAllPolling(){this.activePolls.forEach((i,e)=>{i.complete()}),this.activePolls.clear()}getPollingStatus(i){let e=this.activePolls.get(i);return e?e.value:null}isPolling(i){return this.activePolls.has(i)}static \u0275fac=function(e){return new(e||o)};static \u0275prov=be({token:o,factory:o.\u0275fac,providedIn:"root"})};function Ue(o,i){o&1&&g(0,"div",2)}var Ve=new he("MAT_PROGRESS_BAR_DEFAULT_OPTIONS");var Q=(()=>{class o{_elementRef=u(ye);_ngZone=u(Me);_changeDetectorRef=u(Se);_renderer=u(Pe);_cleanupTransitionEnd;constructor(){let e=u(Ve,{optional:!0});e&&(e.color&&(this.color=this._defaultColor=e.color),this.mode=e.mode||this.mode)}_isNoopAnimation=Oe();get color(){return this._color||this._defaultColor}set color(e){this._color=e}_color;_defaultColor="primary";get value(){return this._value}set value(e){this._value=Fe(e||0),this._changeDetectorRef.markForCheck()}_value=0;get bufferValue(){return this._bufferValue||0}set bufferValue(e){this._bufferValue=Fe(e||0),this._changeDetectorRef.markForCheck()}_bufferValue=0;animationEnd=new Y;get mode(){return this._mode}set mode(e){this._mode=e,this._changeDetectorRef.markForCheck()}_mode="determinate";ngAfterViewInit(){this._ngZone.runOutsideAngular(()=>{this._cleanupTransitionEnd=this._renderer.listen(this._elementRef.nativeElement,"transitionend",this._transitionendHandler)})}ngOnDestroy(){this._cleanupTransitionEnd?.()}_getPrimaryBarTransform(){return`scaleX(${this._isIndeterminate()?1:this.value/100})`}_getBufferBarFlexBasis(){return`${this.mode==="buffer"?this.bufferValue:100}%`}_isIndeterminate(){return this.mode==="indeterminate"||this.mode==="query"}_transitionendHandler=e=>{this.animationEnd.observers.length===0||!e.target||!e.target.classList.contains("mdc-linear-progress__primary-bar")||(this.mode==="determinate"||this.mode==="buffer")&&this._ngZone.run(()=>this.animationEnd.next({value:this.value}))};static \u0275fac=function(s){return new(s||o)};static \u0275cmp=v({type:o,selectors:[["mat-progress-bar"]],hostAttrs:["role","progressbar","aria-valuemin","0","aria-valuemax","100","tabindex","-1",1,"mat-mdc-progress-bar","mdc-linear-progress"],hostVars:10,hostBindings:function(s,l){s&2&&(Z("aria-valuenow",l._isIndeterminate()?null:l.value)("mode",l.mode),A("mat-"+l.color),b("_mat-animation-noopable",l._isNoopAnimation)("mdc-linear-progress--animation-ready",!l._isNoopAnimation)("mdc-linear-progress--indeterminate",l._isIndeterminate()))},inputs:{color:"color",value:[2,"value","value",se],bufferValue:[2,"bufferValue","bufferValue",se],mode:"mode"},outputs:{animationEnd:"animationEnd"},exportAs:["matProgressBar"],decls:7,vars:5,consts:[["aria-hidden","true",1,"mdc-linear-progress__buffer"],[1,"mdc-linear-progress__buffer-bar"],[1,"mdc-linear-progress__buffer-dots"],["aria-hidden","true",1,"mdc-linear-progress__bar","mdc-linear-progress__primary-bar"],[1,"mdc-linear-progress__bar-inner"],["aria-hidden","true",1,"mdc-linear-progress__bar","mdc-linear-progress__secondary-bar"]],template:function(s,l){s&1&&(r(0,"div",0),g(1,"div",1),c(2,Ue,1,0,"div",2),t(),r(3,"div",3),g(4,"span",4),t(),r(5,"div",5),g(6,"span",4),t()),s&2&&(a(),ae("flex-basis",l._getBufferBarFlexBasis()),a(),d(l.mode==="buffer"?2:-1),a(),ae("transform",l._getPrimaryBarTransform()))},styles:[`.mat-mdc-progress-bar{display:block;text-align:start}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);height:max(var(--mat-progress-bar-track-height, 4px),var(--mat-progress-bar-active-indicator-height, 4px))}@media(forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);height:var(--mat-progress-bar-active-indicator-height, 4px)}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}[dir=rtl] .mdc-linear-progress__bar{right:0;transform-origin:center right}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid;border-color:var(--mat-progress-bar-active-indicator-color, var(--mat-sys-primary));border-top-width:var(--mat-progress-bar-active-indicator-height, 4px)}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden;height:var(--mat-progress-bar-track-height, 4px);border-radius:var(--mat-progress-bar-track-shape, var(--mat-sys-corner-none))}.mdc-linear-progress__buffer-dots{-webkit-mask-image:url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E");mask-image:url("data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E");background-repeat:repeat-x;flex:auto;transform:rotate(180deg);animation:mdc-linear-progress-buffering 250ms infinite linear;background-color:var(--mat-progress-bar-track-color, var(--mat-sys-surface-variant))}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}[dir=rtl] .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1);background-color:var(--mat-progress-bar-track-color, var(--mat-sys-surface-variant))}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mat-progress-bar-track-height, 4px) * -2.5))}}@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(83.67142%)}100%{transform:translateX(200.611057%)}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(37.651913%)}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(84.386165%)}100%{transform:translateX(160.277782%)}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(-83.67142%)}100%{transform:translateX(-200.611057%)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(-37.651913%)}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(-84.386165%)}100%{transform:translateX(-160.277782%)}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}
`],encapsulation:2,changeDetection:0})}return o})();function Fe(o,i=0,e=100){return Math.max(i,Math.min(e,o))}var ee=(()=>{class o{static \u0275fac=function(s){return new(s||o)};static \u0275mod=$({type:o});static \u0275inj=W({imports:[G]})}return o})();function je(o,i){o&1&&(r(0,"mat-icon",7),n(1,"cloud_upload"),t(),r(2,"h3"),n(3,"Uploading..."),t(),g(4,"mat-progress-bar",8))}function Xe(o,i){o&1&&(r(0,"mat-icon",7),n(1,"add_photo_alternate"),t(),r(2,"h3"),n(3,"Drop your pigeon photo here"),t(),r(4,"p"),n(5,"or click to select a file"),t(),r(6,"button",9)(7,"mat-icon"),n(8,"upload"),t(),n(9," Choose File "),t())}function Ge(o,i){if(o&1){let e=E();r(0,"div",5)(1,"mat-icon"),n(2,"image"),t(),r(3,"span"),n(4),t(),r(5,"button",10),P("click",function(){f(e);let l=m();return _(l.clearFile())}),r(6,"mat-icon"),n(7,"close"),t()()()}if(o&2){let e,s=m();a(4),x((e=s.selectedFile())==null?null:e.name),a(),S("disabled",s.isUploading())}}function Le(o,i){if(o&1){let e=E();r(0,"div",6)(1,"h4"),n(2,"Location Information"),t(),r(3,"p"),n(4,"We'll use your current location for the pigeon capture."),t(),r(5,"button",11),P("click",function(){f(e);let l=m();return _(l.uploadFile())}),r(6,"mat-icon"),n(7,"send"),t(),n(8," Upload & Analyze Pigeon "),t()()}if(o&2){let e=m();a(5),S("disabled",!e.selectedFile())}}var te=class o{firebaseService=u(j);snackBar=u(J);uploadComplete=new Y;selectedFile=M(null);isDragOver=M(!1);isUploading=M(!1);onDragOver(i){i.preventDefault(),i.stopPropagation(),this.isDragOver.set(!0)}onDragLeave(i){i.preventDefault(),i.stopPropagation(),this.isDragOver.set(!1)}onDrop(i){i.preventDefault(),i.stopPropagation(),this.isDragOver.set(!1);let e=i.dataTransfer?.files;e&&e.length>0&&this.handleFile(e[0])}onFileSelected(i){let e=i.target;e.files&&e.files.length>0&&this.handleFile(e.files[0])}handleFile(i){if(!i.type.startsWith("image/")){this.snackBar.open("Please select an image file","Close",{duration:3e3});return}if(i.size>10*1024*1024){this.snackBar.open("File size must be less than 10MB","Close",{duration:3e3});return}this.selectedFile.set(i)}clearFile(){this.selectedFile.set(null)}uploadFile(){return H(this,null,function*(){let i=this.selectedFile();if(i)try{this.isUploading.set(!0);let e=yield this.getCurrentPosition(),s=e.coords.latitude,l=e.coords.longitude,C=yield this.firebaseService.uploadPigeonImage(i,s,l);this.snackBar.open("File uploaded successfully! Analysis started.","Close",{duration:3e3}),this.uploadComplete.emit(C),this.selectedFile.set(null)}catch(e){console.error("Upload error:",e),this.snackBar.open("Upload failed. Please try again.","Close",{duration:5e3})}finally{this.isUploading.set(!1)}})}getCurrentPosition(){return new Promise((i,e)=>{if(!navigator.geolocation){e(new Error("Geolocation is not supported by this browser"));return}navigator.geolocation.getCurrentPosition(i,s=>{console.warn("Geolocation failed, using default coordinates:",s),i({coords:{latitude:40.7128,longitude:-74.006,accuracy:0,altitude:null,altitudeAccuracy:null,heading:null,speed:null},timestamp:Date.now()})},{enableHighAccuracy:!0,timeout:1e4,maximumAge:3e5})})}static \u0275fac=function(e){return new(e||o)};static \u0275cmp=v({type:o,selectors:[["app-file-upload"]],outputs:{uploadComplete:"uploadComplete"},decls:9,vars:7,consts:[["fileInput",""],[1,"upload-container"],[1,"drop-zone",3,"dragover","dragleave","drop","click"],["type","file","accept","image/*",2,"display","none",3,"change"],[1,"drop-zone-content"],[1,"file-info"],[1,"location-section"],[1,"upload-icon"],["mode","indeterminate"],["mat-raised-button","","color","primary"],["mat-icon-button","",3,"click","disabled"],["mat-raised-button","","color","accent",3,"click","disabled"]],template:function(e,s){if(e&1){let l=E();r(0,"div",1)(1,"div",2),P("dragover",function(y){return f(l),_(s.onDragOver(y))})("dragleave",function(y){return f(l),_(s.onDragLeave(y))})("drop",function(y){return f(l),_(s.onDrop(y))})("click",function(){f(l);let y=Ee(3);return _(y.click())}),r(2,"input",3,0),P("change",function(y){return f(l),_(s.onFileSelected(y))}),t(),r(4,"div",4),c(5,je,5,0)(6,Xe,10,0),t()(),c(7,Ge,8,2,"div",5),c(8,Le,9,1,"div",6),t()}e&2&&(a(),b("drag-over",s.isDragOver())("uploading",s.isUploading()),a(4),d(s.isUploading()?5:6),a(2),d(s.selectedFile()?7:-1),a(),d(s.selectedFile()&&!s.isUploading()?8:-1))},dependencies:[w,k,O,ke,I,D,ee,Q,q],styles:[".upload-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto;padding:20px}.drop-zone[_ngcontent-%COMP%]{border:2px dashed #ccc;border-radius:12px;padding:40px;text-align:center;cursor:pointer;transition:all .3s ease;background-color:#fafafa}.drop-zone[_ngcontent-%COMP%]:hover{border-color:#2196f3;background-color:#f0f8ff}.drop-zone.drag-over[_ngcontent-%COMP%]{border-color:#4caf50;background-color:#f1f8e9}.drop-zone.uploading[_ngcontent-%COMP%]{border-color:#ff9800;background-color:#fff8e1;cursor:not-allowed}.drop-zone-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px}.upload-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;color:#666}.file-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin-top:16px;padding:12px;background-color:#e3f2fd;border-radius:8px}.location-section[_ngcontent-%COMP%]{margin-top:20px;padding:16px;background-color:#f5f5f5;border-radius:8px;text-align:center}.location-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;color:#333}.location-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 16px;color:#666}mat-progress-bar[_ngcontent-%COMP%]{width:100%;margin-top:16px}"]})};function He(o,i){if(o&1&&(r(0,"div",6),g(1,"mat-progress-bar",7),r(2,"div",8),n(3),t()(),r(4,"div",9)(5,"div",10)(6,"mat-icon"),n(7,"cloud_upload"),t(),r(8,"span"),n(9,"Image Uploaded"),t()(),r(10,"div",10)(11,"mat-icon"),n(12,"psychology"),t(),r(13,"span"),n(14,"AI Analysis"),t()(),r(15,"div",10)(16,"mat-icon"),n(17,"compare"),t(),r(18,"span"),n(19,"Matching Pigeons"),t()(),r(20,"div",10)(21,"mat-icon"),n(22,"location_on"),t(),r(23,"span"),n(24,"Gang Assignment"),t()()()),o&2){let e=m(2);a(),S("value",e.progress.progress),a(2),h(" ",e.progress.progress.toFixed(0),"% Complete "),a(2),b("active",e.progress.progress>=10),a(5),b("active",e.progress.progress>=30),a(5),b("active",e.progress.progress>=60),a(5),b("active",e.progress.progress>=90)}}function We(o,i){o&1&&(r(0,"div",3)(1,"mat-icon",11),n(2,"check_circle"),t(),r(3,"p"),n(4,"Your pigeon has been successfully analyzed and added to your collection!"),t()())}function $e(o,i){if(o&1&&(r(0,"div",13)(1,"strong"),n(2,"Error Code:"),t(),n(3),t()),o&2){let e=m(3);a(3),h(" ",e.progress.error," ")}}function Ye(o,i){if(o&1&&(r(0,"div",4)(1,"mat-icon",12),n(2,"error"),t(),r(3,"p"),n(4),t(),c(5,$e,4,1,"div",13),t()),o&2){let e=m(2);a(4),x(e.progress.message),a(),d(e.progress.error?5:-1)}}function Ze(o,i){if(o&1&&(r(0,"div",14)(1,"strong"),n(2,"Last Updated:"),t(),n(3),t()),o&2){let e=m(3);a(3),h(" ",e.formatDate(e.progress.job.updatedAt)," ")}}function Je(o,i){if(o&1&&(r(0,"div",5)(1,"h4"),n(2,"Analysis Details"),t(),r(3,"div",14)(4,"strong"),n(5,"Capture ID:"),t(),r(6,"code"),n(7),t()(),r(8,"div",14)(9,"strong"),n(10,"Started:"),t(),n(11),t(),c(12,Ze,4,1,"div",14),t()),o&2){let e=m(2);a(7),x(e.progress.job.captureId),a(4),h(" ",e.formatDate(e.progress.job.createdAt)," "),a(),d(e.progress.job.updatedAt?12:-1)}}function qe(o,i){o&1&&(r(0,"mat-card-actions")(1,"button",15)(2,"mat-icon"),n(3,"refresh"),t(),n(4," Try Again "),t()())}function Ke(o,i){if(o&1&&(r(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"div",2)(4,"mat-icon"),n(5),t()(),r(6,"mat-card-title"),n(7),t(),r(8,"mat-card-subtitle"),n(9),t()(),r(10,"mat-card-content"),c(11,He,25,10),c(12,We,5,0,"div",3),c(13,Ye,6,2,"div",4),c(14,Je,13,3,"div",5),t(),c(15,qe,5,0,"mat-card-actions"),t()()),o&2){let e=m();a(3),A(e.getAvatarClass()),a(2),x(e.getStatusIcon()),a(2),x(e.getTitle()),a(2),x(e.progress.message),a(2),d(e.progress.status==="PENDING"?11:-1),a(),d(e.progress.status==="FINISHED"?12:-1),a(),d(e.progress.status==="ERROR"?13:-1),a(),d(e.progress.job?14:-1),a(),d(e.progress.status==="ERROR"?15:-1)}}var ne=class o{progress=null;getTitle(){if(!this.progress)return"";switch(this.progress.status){case"PENDING":return"Analyzing Your Pigeon";case"FINISHED":return"Analysis Complete!";case"ERROR":return"Analysis Failed";default:return"Processing..."}}getStatusIcon(){if(!this.progress)return"help";switch(this.progress.status){case"PENDING":return"hourglass_empty";case"FINISHED":return"check_circle";case"ERROR":return"error";default:return"help"}}getAvatarClass(){if(!this.progress)return"";switch(this.progress.status){case"PENDING":return"avatar-pending";case"FINISHED":return"avatar-finished";case"ERROR":return"avatar-error";default:return""}}formatDate(i){return i?(i instanceof Date?i:new Date(i)).toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):""}static \u0275fac=function(e){return new(e||o)};static \u0275cmp=v({type:o,selectors:[["app-analysis-progress"]],inputs:{progress:"progress"},decls:1,vars:1,consts:[[1,"progress-container"],[1,"progress-card"],["mat-card-avatar",""],[1,"success-section"],[1,"error-section"],[1,"job-details"],[1,"progress-section"],["mode","determinate",3,"value"],[1,"progress-text"],[1,"analysis-steps"],[1,"step"],[1,"success-icon"],[1,"error-icon"],[1,"error-details"],[1,"detail-row"],["mat-button","","color","primary"]],template:function(e,s){e&1&&c(0,Ke,16,10,"div",0),e&2&&d(s.progress?0:-1)},dependencies:[w,V,F,z,U,R,N,B,T,ee,Q,I,D,k,O],styles:[".progress-container[_ngcontent-%COMP%]{max-width:600px;margin:20px auto;padding:0 16px}.progress-card[_ngcontent-%COMP%]{width:100%}.progress-section[_ngcontent-%COMP%]{margin:16px 0}.progress-text[_ngcontent-%COMP%]{text-align:center;margin-top:8px;font-weight:500;color:#666}.analysis-steps[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:16px;margin-top:24px}.step[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:8px;padding:16px;border-radius:8px;background-color:#f5f5f5;transition:all .3s ease;opacity:.5}.step.active[_ngcontent-%COMP%]{background-color:#e3f2fd;opacity:1;transform:scale(1.05)}.step[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;color:#666}.step.active[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#2196f3}.step[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;text-align:center;font-weight:500}.success-section[_ngcontent-%COMP%]{text-align:center;padding:24px}.success-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;color:#4caf50;margin-bottom:16px}.error-section[_ngcontent-%COMP%]{text-align:center;padding:24px}.error-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px;color:#f44336;margin-bottom:16px}.error-details[_ngcontent-%COMP%]{margin-top:16px;padding:12px;background-color:#ffebee;border-radius:4px;font-family:Roboto Mono,monospace;font-size:14px}.job-details[_ngcontent-%COMP%]{margin-top:24px;padding:16px;background-color:#f9f9f9;border-radius:8px}.job-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#333}.detail-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:8px;gap:16px}.detail-row[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background-color:#e0e0e0;padding:2px 6px;border-radius:3px;font-family:Roboto Mono,monospace;font-size:12px;word-break:break-all;flex-shrink:0}.avatar-pending[_ngcontent-%COMP%]{background-color:#ff9800;color:#fff}.avatar-finished[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.avatar-error[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}@media (max-width: 600px){.analysis-steps[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);gap:12px}.step[_ngcontent-%COMP%]{padding:12px}.detail-row[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:4px}}"]})};var Be=(()=>{class o{get vertical(){return this._vertical}set vertical(e){this._vertical=le(e)}_vertical=!1;get inset(){return this._inset}set inset(e){this._inset=le(e)}_inset=!1;static \u0275fac=function(s){return new(s||o)};static \u0275cmp=v({type:o,selectors:[["mat-divider"]],hostAttrs:["role","separator",1,"mat-divider"],hostVars:7,hostBindings:function(s,l){s&2&&(Z("aria-orientation",l.vertical?"vertical":"horizontal"),b("mat-divider-vertical",l.vertical)("mat-divider-horizontal",!l.vertical)("mat-divider-inset",l.inset))},inputs:{vertical:"vertical",inset:"inset"},decls:0,vars:0,template:function(s,l){},styles:[`.mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}
`],encapsulation:2,changeDetection:0})}return o})(),ze=(()=>{class o{static \u0275fac=function(s){return new(s||o)};static \u0275mod=$({type:o});static \u0275inj=W({imports:[G,G]})}return o})();function et(o,i){if(o&1&&(r(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"div",2)(4,"mat-icon"),n(5,"pets"),t()(),r(6,"mat-card-title"),n(7),t(),r(8,"mat-card-subtitle"),n(9),t()(),r(10,"mat-card-content")(11,"div",3)(12,"h3")(13,"mat-icon"),n(14,"group"),t(),n(15," Gang Information "),t(),r(16,"mat-chip-set")(17,"mat-chip"),n(18),t()()(),g(19,"mat-divider"),r(20,"div",3)(21,"h3")(22,"mat-icon"),n(23,"star"),t(),n(24," Distinctiveness "),t(),r(25,"mat-chip-set")(26,"mat-chip"),n(27),t()()(),g(28,"mat-divider"),r(29,"div",3)(30,"h3")(31,"mat-icon"),n(32,"description"),t(),n(33," Description "),t(),r(34,"p",4),n(35),t()(),g(36,"mat-divider"),r(37,"div",3)(38,"h3")(39,"mat-icon"),n(40,"location_on"),t(),n(41," Capture Location "),t(),r(42,"div",5)(43,"div",6)(44,"strong"),n(45,"Latitude:"),t(),n(46),t(),r(47,"div",6)(48,"strong"),n(49,"Longitude:"),t(),n(50),t()()(),g(51,"mat-divider"),r(52,"div",3)(53,"h3")(54,"mat-icon"),n(55,"info"),t(),n(56," Capture Details "),t(),r(57,"div",7)(58,"div",8)(59,"strong"),n(60,"Capture ID:"),t(),r(61,"code"),n(62),t()(),r(63,"div",8)(64,"strong"),n(65,"Base Pigeon ID:"),t(),r(66,"code"),n(67),t()()()()(),r(68,"mat-card-actions")(69,"button",9)(70,"mat-icon"),n(71,"share"),t(),n(72," Share "),t(),r(73,"button",10)(74,"mat-icon"),n(75,"favorite_border"),t(),n(76," Add to Favorites "),t()()()()),o&2){let e=m();a(7),x(e.pigeon.basePigeon.name),a(2),h(" Captured ",e.formatDate(e.pigeon.captureDate)," "),a(8),A("gang-"+e.pigeon.gang.toLowerCase()),a(),h(" ",e.pigeon.gang," Gang "),a(8),A("distinctiveness-"+e.pigeon.basePigeon.distinctiveness.toLowerCase()),a(),h(" ",e.pigeon.basePigeon.distinctiveness," "),a(8),x(e.pigeon.basePigeon.description),a(11),h(" ",e.pigeon.coordinates.latitude.toFixed(6)," "),a(4),h(" ",e.pigeon.coordinates.longitude.toFixed(6)," "),a(12),x(e.pigeon.captureId),a(5),x(e.pigeon.basePigeonId)}}var ie=class o{pigeon=null;formatDate(i){return i?(i instanceof Date?i:new Date(i)).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):""}static \u0275fac=function(e){return new(e||o)};static \u0275cmp=v({type:o,selectors:[["app-pigeon-display"]],inputs:{pigeon:"pigeon"},decls:1,vars:1,consts:[[1,"pigeon-container"],[1,"pigeon-card"],["mat-card-avatar","",1,"pigeon-avatar"],[1,"info-section"],[1,"description-text"],[1,"location-info"],[1,"coordinate"],[1,"capture-details"],[1,"detail-item"],["mat-button","","color","primary"],["mat-button","","color","accent"]],template:function(e,s){e&1&&c(0,et,77,13,"div",0),e&2&&d(s.pigeon?0:-1)},dependencies:[w,V,F,z,U,R,N,B,T,Ae,De,Ie,I,D,k,O,ze,Be],styles:[".pigeon-container[_ngcontent-%COMP%]{max-width:600px;margin:20px auto;padding:0 16px}.pigeon-card[_ngcontent-%COMP%]{width:100%}.pigeon-avatar[_ngcontent-%COMP%]{background-color:#2196f3;color:#fff;display:flex;align-items:center;justify-content:center}.info-section[_ngcontent-%COMP%]{margin:16px 0}.info-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 12px;color:#333;font-size:16px;font-weight:500}.info-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px}.description-text[_ngcontent-%COMP%]{margin:0;line-height:1.6;color:#666}.location-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.coordinate[_ngcontent-%COMP%]{font-family:Roboto Mono,monospace;font-size:14px}.capture-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.detail-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.detail-item[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:4px 8px;border-radius:4px;font-family:Roboto Mono,monospace;font-size:12px;word-break:break-all}.gang-one[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.gang-two[_ngcontent-%COMP%]{background-color:#2196f3;color:#fff}.gang-three[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.gang-four[_ngcontent-%COMP%]{background-color:#ff9800;color:#fff}.gang-five[_ngcontent-%COMP%]{background-color:#9c27b0;color:#fff}.distinctiveness-common[_ngcontent-%COMP%]{background-color:#e0e0e0;color:#333}.distinctiveness-unusual[_ngcontent-%COMP%]{background-color:#fff3e0;color:#f57c00;border:1px solid #ffb74d}.distinctiveness-rare[_ngcontent-%COMP%]{background-color:#fce4ec;color:#c2185b;border:1px solid #f48fb1}mat-divider[_ngcontent-%COMP%]{margin:16px 0}mat-card-actions[_ngcontent-%COMP%]{padding:16px;display:flex;gap:8px}@media (max-width: 600px){.pigeon-container[_ngcontent-%COMP%]{margin:10px auto;padding:0 8px}.location-info[_ngcontent-%COMP%]{font-size:12px}.detail-item[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{font-size:10px}}"]})};function tt(o,i){if(o&1){let e=E();r(0,"div",1)(1,"div",3)(2,"h1",4)(3,"mat-icon"),n(4,"camera_alt"),t(),n(5," Capture a Pigeon "),t(),r(6,"p",5),n(7," Take a photo of a pigeon to analyze its characteristics and add it to your collection. "),t()(),r(8,"app-file-upload",6),P("uploadComplete",function(l){f(e);let C=m();return _(C.onUploadComplete(l))}),t(),r(9,"div",7)(10,"mat-card",8)(11,"mat-card-header")(12,"mat-card-title")(13,"mat-icon"),n(14,"info"),t(),n(15," How it works "),t()(),r(16,"mat-card-content")(17,"ol",9)(18,"li"),n(19,"Take or upload a clear photo of a pigeon"),t(),r(20,"li"),n(21,"Our AI will analyze the pigeon's characteristics"),t(),r(22,"li"),n(23,"The pigeon will be assigned to a gang based on location"),t(),r(24,"li"),n(25,"Add the pigeon to your personal collection"),t()()()()()()}}function nt(o,i){if(o&1){let e=E();r(0,"div",1),g(1,"app-analysis-progress",10),r(2,"div",11)(3,"button",12),P("click",function(){f(e);let l=m();return _(l.startNewAnalysis())}),r(4,"mat-icon"),n(5,"add_a_photo"),t(),n(6," Analyze Another Pigeon "),t()()()}if(o&2){let e=m();a(),S("progress",e.analysisProgress())}}function it(o,i){if(o&1){let e=E();r(0,"div",1)(1,"div",13)(2,"h2")(3,"mat-icon"),n(4,"check_circle"),t(),n(5," Pigeon Captured Successfully! "),t()(),g(6,"app-pigeon-display",14),r(7,"div",11)(8,"button",15),P("click",function(){f(e);let l=m();return _(l.startNewAnalysis())}),r(9,"mat-icon"),n(10,"add_a_photo"),t(),n(11," Capture Another Pigeon "),t(),r(12,"button",16)(13,"mat-icon"),n(14,"collections"),t(),n(15," View My Deck "),t()()()}if(o&2){let e=m();a(6),S("pigeon",e.capturedPigeon())}}function rt(o,i){if(o&1){let e=E();r(0,"div",2)(1,"mat-card",17)(2,"mat-card-header")(3,"div",18)(4,"mat-icon"),n(5,"error"),t()(),r(6,"mat-card-title"),n(7,"Analysis Failed"),t(),r(8,"mat-card-subtitle"),n(9,"Something went wrong during the pigeon analysis"),t()(),r(10,"mat-card-content")(11,"p"),n(12,"We encountered an error while analyzing your pigeon image. Please try again."),t()(),r(13,"mat-card-actions")(14,"button",19),P("click",function(){f(e);let l=m();return _(l.startNewAnalysis())}),r(15,"mat-icon"),n(16,"refresh"),t(),n(17," Try Again "),t()()()()}}var Ne=class o{firebaseService=u(j);analysisPollingService=u(K);snackBar=u(J);destroy$=new pe;currentState=M("upload");analysisProgress=M(null);capturedPigeon=M(null);currentCaptureId=M(null);ngOnInit(){this.signInUser()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete(),this.analysisPollingService.stopAllPolling()}signInUser(){return H(this,null,function*(){try{yield this.firebaseService.signInAnonymously(),console.log("User signed in anonymously")}catch(i){console.error("Failed to sign in:",i),this.snackBar.open("Failed to initialize app. Please refresh the page.","Close",{duration:5e3})}})}onUploadComplete(i){console.log("Upload complete:",i),this.currentCaptureId.set(i.captureId),this.currentState.set("analyzing"),this.startAnalysisPolling(i.captureId)}startAnalysisPolling(i){this.analysisPollingService.startPolling(i).pipe(oe(this.destroy$)).subscribe({next:e=>{console.log("Analysis progress:",e),this.analysisProgress.set(e),e.status==="FINISHED"?this.loadCompletedPigeon(i):e.status==="ERROR"&&this.currentState.set("error")},error:e=>{console.error("Polling error:",e),this.currentState.set("error"),this.snackBar.open("Failed to track analysis progress","Close",{duration:5e3})}})}loadCompletedPigeon(i){this.firebaseService.getPigeonByCaptureId(i).pipe(oe(this.destroy$)).subscribe({next:e=>{console.log("Pigeon loaded:",e),this.capturedPigeon.set(e.pigeon),this.currentState.set("complete")},error:e=>{console.error("Failed to load pigeon:",e),this.currentState.set("error"),this.snackBar.open("Failed to load pigeon data","Close",{duration:5e3})}})}startNewAnalysis(){this.currentState.set("upload"),this.analysisProgress.set(null),this.capturedPigeon.set(null),this.currentCaptureId.set(null),this.analysisPollingService.stopAllPolling()}static \u0275fac=function(e){return new(e||o)};static \u0275cmp=v({type:o,selectors:[["app-capture"]],decls:5,vars:4,consts:[[1,"capture-container"],[1,"state-container"],[1,"state-container","error-state"],[1,"welcome-section"],[1,"capture-title"],[1,"capture-subtitle"],[3,"uploadComplete"],[1,"info-section"],[1,"info-card"],[1,"steps-list"],[3,"progress"],[1,"action-buttons"],["mat-stroked-button","",1,"new-analysis-btn",3,"click"],[1,"success-header"],[3,"pigeon"],["mat-raised-button","","color","primary",1,"new-analysis-btn",3,"click"],["mat-stroked-button","","routerLink","/deck",1,"view-deck-btn"],[1,"error-card"],["mat-card-avatar","",1,"error-avatar"],["mat-raised-button","","color","primary",3,"click"]],template:function(e,s){e&1&&(r(0,"div",0),c(1,tt,26,0,"div",1),c(2,nt,7,1,"div",1),c(3,it,16,1,"div",1),c(4,rt,18,0,"div",2),t()),e&2&&(a(),d(s.currentState()==="upload"?1:-1),a(),d(s.currentState()==="analyzing"?2:-1),a(),d(s.currentState()==="complete"?3:-1),a(),d(s.currentState()==="error"?4:-1))},dependencies:[w,we,k,O,I,D,V,F,z,U,R,N,B,T,q,te,ne,ie],styles:[".capture-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px}.state-container[_ngcontent-%COMP%]{margin-bottom:24px}.welcome-section[_ngcontent-%COMP%]{text-align:center;margin-bottom:32px}.capture-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;margin:0 0 16px;font-size:2.2rem;font-weight:500;color:#333}.capture-subtitle[_ngcontent-%COMP%]{font-size:1.1rem;color:#666;max-width:600px;margin:0 auto;line-height:1.5}.info-section[_ngcontent-%COMP%]{margin-top:48px}.info-card[_ngcontent-%COMP%]{background-color:#f8f9fa}.steps-list[_ngcontent-%COMP%]{margin:0;padding-left:20px}.steps-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px;line-height:1.4}.success-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:24px}.success-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:12px;color:#4caf50;margin:0}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:16px;justify-content:center;margin-top:24px;flex-wrap:wrap}.new-analysis-btn[_ngcontent-%COMP%], .view-deck-btn[_ngcontent-%COMP%]{min-width:200px}.error-state[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:300px}.error-card[_ngcontent-%COMP%]{max-width:400px;text-align:center}.error-avatar[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}@media (max-width: 768px){.capture-container[_ngcontent-%COMP%]{padding:16px}.capture-title[_ngcontent-%COMP%]{font-size:1.8rem}.action-buttons[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.new-analysis-btn[_ngcontent-%COMP%], .view-deck-btn[_ngcontent-%COMP%]{width:100%;max-width:300px}}"]})};export{Ne as CaptureComponent};
