import{a as ri,b as oi}from"./chunk-HXMTDF5Q.js";import{a as Vt,b as Bt,c as Nt,d as Ht,e as Wt,f as jt,g as Gt,h as Kt,i as Xt,j as Oe,k as Pe,l as Jt,m as Te,n as ei,o as Ie,p as ti,q as De,r as Ae,s as Ze,t as Xe,u as Re,v as Fe,w as Q,x as ii,y as ai,z as ni}from"./chunk-V3JWCIZ3.js";import{Aa as Qe,Da as Ye,Ea as $e,Fa as Qt,Ga as Yt,Ha as $t,Ia as Zt,S as ke,V as Ke,X as Se,Y as St,Z as wt,_ as Et,aa as q,b as yt,ba as X,c as xt,ca as Ot,d as Ct,e as Mt,ea as Pt,fa as qe,ha as Ue,i as kt,ia as J,ja as we,la as Tt,na as It,oa as Dt,pa as At,qa as S,ra as Rt,sa as Ft,ta as Ee,ua as zt,va as Lt,ya as qt,za as Ut}from"./chunk-RDAQWP7I.js";import{$a as d,$b as c,Ab as _,Ac as b,Bb as f,Bc as T,Ca as ct,Cb as pt,D as st,Da as dt,Db as ue,Ea as de,Eb as ge,Fb as u,G as oe,Gb as o,H as He,Ha as B,Hb as s,Ib as C,Jb as _e,Kb as z,Lb as fe,Mb as h,N as lt,Nb as p,Ob as be,Pb as Y,Qb as ve,Rb as j,Sb as I,Tb as D,Xb as $,Y as We,Yb as ye,Z as je,Zb as A,_ as W,_b as G,ac as K,bc as R,cb as mt,cc as xe,da as se,db as ee,dc as Ce,ea as y,ec as Me,fc as ht,ga as k,gc as te,hc as ut,ia as l,ic as gt,jc as _t,k as v,kc as ft,lb as O,m as rt,mb as x,na as w,nb as me,oa as E,p as ot,pa as F,pc as bt,qa as le,qb as pe,qc as vt,ra as Ge,sb as N,tb as he,xa as ce,y as Ne,ya as V,yc as Z,zb as g}from"./chunk-OZCVVD7X.js";var Ei=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275cmp=O({type:a,selectors:[["ng-component"]],hostAttrs:["cdk-text-field-style-loader",""],decls:0,vars:0,template:function(t,i){},styles:[`textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}
`],encapsulation:2,changeDetection:0})}return a})(),Oi={passive:!0},si=(()=>{class a{_platform=l(ke);_ngZone=l(he);_renderer=l(mt).createRenderer(null,null);_styleLoader=l(Se);_monitoredElements=new Map;constructor(){}monitor(e){if(!this._platform.isBrowser)return ot;this._styleLoader.load(Ei);let t=Ke(e),i=this._monitoredElements.get(t);if(i)return i.subject;let r=new v,m="cdk-text-field-autofilled",M=re=>{re.animationName==="cdk-text-field-autofill-start"&&!t.classList.contains(m)?(t.classList.add(m),this._ngZone.run(()=>r.next({target:re.target,isAutofilled:!0}))):re.animationName==="cdk-text-field-autofill-end"&&t.classList.contains(m)&&(t.classList.remove(m),this._ngZone.run(()=>r.next({target:re.target,isAutofilled:!1})))},P=this._ngZone.runOutsideAngular(()=>(t.classList.add("cdk-text-field-autofill-monitored"),this._renderer.listen(t,"animationstart",M,Oi)));return this._monitoredElements.set(t,{subject:r,unlisten:P}),r}stopMonitoring(e){let t=Ke(e),i=this._monitoredElements.get(t);i&&(i.unlisten(),i.subject.complete(),t.classList.remove("cdk-text-field-autofill-monitored"),t.classList.remove("cdk-text-field-autofilled"),this._monitoredElements.delete(t))}ngOnDestroy(){this._monitoredElements.forEach((e,t)=>this.stopMonitoring(t))}static \u0275fac=function(t){return new(t||a)};static \u0275prov=se({token:a,factory:a.\u0275fac,providedIn:"root"})}return a})();var li=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=x({type:a});static \u0275inj=y({})}return a})();var ci=new k("MAT_INPUT_VALUE_ACCESSOR");var U=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=x({type:a});static \u0275inj=y({imports:[S,wt,Q,S]})}return a})();var Pi=["button","checkbox","file","hidden","image","radio","range","reset","submit"],Ti=new k("MAT_INPUT_CONFIG"),di=(()=>{class a{_elementRef=l(B);_platform=l(ke);ngControl=l(Pe,{optional:!0,self:!0});_autofillMonitor=l(si);_ngZone=l(he);_formField=l(Fe,{optional:!0});_renderer=l(ee);_uid=l(q).getId("mat-input-");_previousNativeValue;_inputValueAccessor;_signalBasedValueAccessor;_previousPlaceholder;_errorStateTracker;_config=l(Ti,{optional:!0});_cleanupIosKeyup;_cleanupWebkitWheel;_isServer;_isNativeSelect;_isTextarea;_isInFormField;focused=!1;stateChanges=new v;controlType="mat-input";autofilled=!1;get disabled(){return this._disabled}set disabled(e){this._disabled=we(e),this.focused&&(this.focused=!1,this.stateChanges.next())}_disabled=!1;get id(){return this._id}set id(e){this._id=e||this._uid}_id;placeholder;name;get required(){return this._required??this.ngControl?.control?.hasValidator(Oe.required)??!1}set required(e){this._required=we(e)}_required;get type(){return this._type}set type(e){let t=this._type;this._type=e||"text",this._validateType(),!this._isTextarea&&Ue().has(this._type)&&(this._elementRef.nativeElement.type=this._type),this._type!==t&&this._ensureWheelDefaultBehavior()}_type="text";get errorStateMatcher(){return this._errorStateTracker.matcher}set errorStateMatcher(e){this._errorStateTracker.matcher=e}userAriaDescribedBy;get value(){return this._signalBasedValueAccessor?this._signalBasedValueAccessor.value():this._inputValueAccessor.value}set value(e){e!==this.value&&(this._signalBasedValueAccessor?this._signalBasedValueAccessor.value.set(e):this._inputValueAccessor.value=e,this.stateChanges.next())}get readonly(){return this._readonly}set readonly(e){this._readonly=we(e)}_readonly=!1;disabledInteractive;get errorState(){return this._errorStateTracker.errorState}set errorState(e){this._errorStateTracker.errorState=e}_neverEmptyInputTypes=["date","datetime","datetime-local","month","time","week"].filter(e=>Ue().has(e));constructor(){let e=l(Te,{optional:!0}),t=l(Ie,{optional:!0}),i=l(De),r=l(ci,{optional:!0,self:!0}),m=this._elementRef.nativeElement,M=m.nodeName.toLowerCase();r?ce(r.value)?this._signalBasedValueAccessor=r:this._inputValueAccessor=r:this._inputValueAccessor=m,this._previousNativeValue=this.value,this.id=this.id,this._platform.IOS&&this._ngZone.runOutsideAngular(()=>{this._cleanupIosKeyup=this._renderer.listen(m,"keyup",this._iOSKeyupListener)}),this._errorStateTracker=new Ae(i,this.ngControl,t,e,this.stateChanges),this._isServer=!this._platform.isBrowser,this._isNativeSelect=M==="select",this._isTextarea=M==="textarea",this._isInFormField=!!this._formField,this.disabledInteractive=this._config?.disabledInteractive||!1,this._isNativeSelect&&(this.controlType=m.multiple?"mat-native-select-multiple":"mat-native-select"),this._signalBasedValueAccessor&&bt(()=>{this._signalBasedValueAccessor.value(),this.stateChanges.next()})}ngAfterViewInit(){this._platform.isBrowser&&this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(e=>{this.autofilled=e.isAutofilled,this.stateChanges.next()})}ngOnChanges(){this.stateChanges.next()}ngOnDestroy(){this.stateChanges.complete(),this._platform.isBrowser&&this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement),this._cleanupIosKeyup?.(),this._cleanupWebkitWheel?.()}ngDoCheck(){this.ngControl&&(this.updateErrorState(),this.ngControl.disabled!==null&&this.ngControl.disabled!==this.disabled&&(this.disabled=this.ngControl.disabled,this.stateChanges.next())),this._dirtyCheckNativeValue(),this._dirtyCheckPlaceholder()}focus(e){this._elementRef.nativeElement.focus(e)}updateErrorState(){this._errorStateTracker.updateErrorState()}_focusChanged(e){if(e!==this.focused){if(!this._isNativeSelect&&e&&this.disabled&&this.disabledInteractive){let t=this._elementRef.nativeElement;t.type==="number"?(t.type="text",t.setSelectionRange(0,0),t.type="number"):t.setSelectionRange(0,0)}this.focused=e,this.stateChanges.next()}}_onInput(){}_dirtyCheckNativeValue(){let e=this._elementRef.nativeElement.value;this._previousNativeValue!==e&&(this._previousNativeValue=e,this.stateChanges.next())}_dirtyCheckPlaceholder(){let e=this._getPlaceholder();if(e!==this._previousPlaceholder){let t=this._elementRef.nativeElement;this._previousPlaceholder=e,e?t.setAttribute("placeholder",e):t.removeAttribute("placeholder")}}_getPlaceholder(){return this.placeholder||null}_validateType(){Pi.indexOf(this._type)>-1}_isNeverEmpty(){return this._neverEmptyInputTypes.indexOf(this._type)>-1}_isBadInput(){let e=this._elementRef.nativeElement.validity;return e&&e.badInput}get empty(){return!this._isNeverEmpty()&&!this._elementRef.nativeElement.value&&!this._isBadInput()&&!this.autofilled}get shouldLabelFloat(){if(this._isNativeSelect){let e=this._elementRef.nativeElement,t=e.options[0];return this.focused||e.multiple||!this.empty||!!(e.selectedIndex>-1&&t&&t.label)}else return this.focused&&!this.disabled||!this.empty}get describedByIds(){return this._elementRef.nativeElement.getAttribute("aria-describedby")?.split(" ")||[]}setDescribedByIds(e){let t=this._elementRef.nativeElement;e.length?t.setAttribute("aria-describedby",e.join(" ")):t.removeAttribute("aria-describedby")}onContainerClick(){this.focused||this.focus()}_isInlineSelect(){let e=this._elementRef.nativeElement;return this._isNativeSelect&&(e.multiple||e.size>1)}_iOSKeyupListener=e=>{let t=e.target;!t.value&&t.selectionStart===0&&t.selectionEnd===0&&(t.setSelectionRange(1,1),t.setSelectionRange(0,0))};_webkitBlinkWheelListener=()=>{};_ensureWheelDefaultBehavior(){this._cleanupWebkitWheel?.(),this._type==="number"&&(this._platform.BLINK||this._platform.WEBKIT)&&(this._cleanupWebkitWheel=this._renderer.listen(this._elementRef.nativeElement,"wheel",this._webkitBlinkWheelListener))}_getReadonlyAttribute(){return this._isNativeSelect?null:this.readonly||this.disabled&&this.disabledInteractive?"true":null}static \u0275fac=function(t){return new(t||a)};static \u0275dir=me({type:a,selectors:[["input","matInput",""],["textarea","matInput",""],["select","matNativeControl",""],["input","matNativeControl",""],["textarea","matNativeControl",""]],hostAttrs:[1,"mat-mdc-input-element"],hostVars:21,hostBindings:function(t,i){t&1&&h("focus",function(){return i._focusChanged(!0)})("blur",function(){return i._focusChanged(!1)})("input",function(){return i._onInput()}),t&2&&(fe("id",i.id)("disabled",i.disabled&&!i.disabledInteractive)("required",i.required),g("name",i.name||null)("readonly",i._getReadonlyAttribute())("aria-disabled",i.disabled&&i.disabledInteractive?"true":null)("aria-invalid",i.empty&&i.required?null:i.errorState)("aria-required",i.required)("id",i.id),A("mat-input-server",i._isServer)("mat-mdc-form-field-textarea-control",i._isInFormField&&i._isTextarea)("mat-mdc-form-field-input-control",i._isInFormField)("mat-mdc-input-disabled-interactive",i.disabledInteractive)("mdc-text-field__input",i._isInFormField)("mat-mdc-native-select-inline",i._isInlineSelect()))},inputs:{disabled:"disabled",id:"id",placeholder:"placeholder",name:"name",required:"required",type:"type",errorStateMatcher:"errorStateMatcher",userAriaDescribedBy:[0,"aria-describedby","userAriaDescribedBy"],value:"value",readonly:"readonly",disabledInteractive:[2,"disabledInteractive","disabledInteractive",b]},exportAs:["matInput"],features:[te([{provide:Re,useExisting:a}]),de]})}return a})(),mi=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=x({type:a});static \u0275inj=y({imports:[S,U,U,li,S]})}return a})();var pi=(()=>{class a{_animationsDisabled=J();state="unchecked";disabled=!1;appearance="full";constructor(){}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=O({type:a,selectors:[["mat-pseudo-checkbox"]],hostAttrs:[1,"mat-pseudo-checkbox"],hostVars:12,hostBindings:function(t,i){t&2&&A("mat-pseudo-checkbox-indeterminate",i.state==="indeterminate")("mat-pseudo-checkbox-checked",i.state==="checked")("mat-pseudo-checkbox-disabled",i.disabled)("mat-pseudo-checkbox-minimal",i.appearance==="minimal")("mat-pseudo-checkbox-full",i.appearance==="full")("_mat-animation-noopable",i._animationsDisabled)},inputs:{state:"state",disabled:"disabled",appearance:"appearance"},decls:0,vars:0,template:function(t,i){},styles:[`.mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:"";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-minimal-selected-checkmark-color, var(--mat-sys-primary))}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full{border-color:var(--mat-pseudo-checkbox-full-unselected-icon-color, var(--mat-sys-on-surface-variant));border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-pseudo-checkbox-full-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-pseudo-checkbox-full-selected-icon-color, var(--mat-sys-primary));border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-pseudo-checkbox-full-selected-checkmark-color, var(--mat-sys-on-primary))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-pseudo-checkbox-full-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-pseudo-checkbox-full-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}
`],encapsulation:2,changeDetection:0})}return a})();var Di=["text"],Ai=[[["mat-icon"]],"*"],Ri=["mat-icon","*"];function Fi(a,n){if(a&1&&C(0,"mat-pseudo-checkbox",1),a&2){let e=p();u("disabled",e.disabled)("state",e.selected?"checked":"unchecked")}}function zi(a,n){if(a&1&&C(0,"mat-pseudo-checkbox",3),a&2){let e=p();u("disabled",e.disabled)}}function Li(a,n){if(a&1&&(o(0,"span",4),c(1),s()),a&2){let e=p();d(),R("(",e.group.label,")")}}var et=new k("MAT_OPTION_PARENT_COMPONENT"),tt=new k("MatOptgroup");var Je=class{source;isUserInput;constructor(n,e=!1){this.source=n,this.isUserInput=e}},H=(()=>{class a{_element=l(B);_changeDetectorRef=l(Z);_parent=l(et,{optional:!0});group=l(tt,{optional:!0});_signalDisableRipple=!1;_selected=!1;_active=!1;_mostRecentViewValue="";get multiple(){return this._parent&&this._parent.multiple}get selected(){return this._selected}value;id=l(q).getId("mat-option-");get disabled(){return this.group&&this.group.disabled||this._disabled()}set disabled(e){this._disabled.set(e)}_disabled=V(!1);get disableRipple(){return this._signalDisableRipple?this._parent.disableRipple():!!this._parent?.disableRipple}get hideSingleSelectionIndicator(){return!!(this._parent&&this._parent.hideSingleSelectionIndicator)}onSelectionChange=new N;_text;_stateChanges=new v;constructor(){let e=l(Se);e.load(It),e.load(St),this._signalDisableRipple=!!this._parent&&ce(this._parent.disableRipple)}get active(){return this._active}get viewValue(){return(this._text?.nativeElement.textContent||"").trim()}select(e=!0){this._selected||(this._selected=!0,this._changeDetectorRef.markForCheck(),e&&this._emitSelectionChangeEvent())}deselect(e=!0){this._selected&&(this._selected=!1,this._changeDetectorRef.markForCheck(),e&&this._emitSelectionChangeEvent())}focus(e,t){let i=this._getHostElement();typeof i.focus=="function"&&i.focus(t)}setActiveStyles(){this._active||(this._active=!0,this._changeDetectorRef.markForCheck())}setInactiveStyles(){this._active&&(this._active=!1,this._changeDetectorRef.markForCheck())}getLabel(){return this.viewValue}_handleKeydown(e){(e.keyCode===13||e.keyCode===32)&&!X(e)&&(this._selectViaInteraction(),e.preventDefault())}_selectViaInteraction(){this.disabled||(this._selected=this.multiple?!this._selected:!0,this._changeDetectorRef.markForCheck(),this._emitSelectionChangeEvent(!0))}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._element.nativeElement}ngAfterViewChecked(){if(this._selected){let e=this.viewValue;e!==this._mostRecentViewValue&&(this._mostRecentViewValue&&this._stateChanges.next(),this._mostRecentViewValue=e)}}ngOnDestroy(){this._stateChanges.complete()}_emitSelectionChangeEvent(e=!1){this.onSelectionChange.emit(new Je(this,e))}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=O({type:a,selectors:[["mat-option"]],viewQuery:function(t,i){if(t&1&&j(Di,7),t&2){let r;I(r=D())&&(i._text=r.first)}},hostAttrs:["role","option",1,"mat-mdc-option","mdc-list-item"],hostVars:11,hostBindings:function(t,i){t&1&&h("click",function(){return i._selectViaInteraction()})("keydown",function(m){return i._handleKeydown(m)}),t&2&&(fe("id",i.id),g("aria-selected",i.selected)("aria-disabled",i.disabled.toString()),A("mdc-list-item--selected",i.selected)("mat-mdc-option-multiple",i.multiple)("mat-mdc-option-active",i.active)("mdc-list-item--disabled",i.disabled))},inputs:{value:"value",id:"id",disabled:[2,"disabled","disabled",b]},outputs:{onSelectionChange:"onSelectionChange"},exportAs:["matOption"],ngContentSelectors:Ri,decls:8,vars:5,consts:[["text",""],["aria-hidden","true",1,"mat-mdc-option-pseudo-checkbox",3,"disabled","state"],[1,"mdc-list-item__primary-text"],["state","checked","aria-hidden","true","appearance","minimal",1,"mat-mdc-option-pseudo-checkbox",3,"disabled"],[1,"cdk-visually-hidden"],["aria-hidden","true","mat-ripple","",1,"mat-mdc-option-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled"]],template:function(t,i){t&1&&(be(Ai),_(0,Fi,1,2,"mat-pseudo-checkbox",1),Y(1),o(2,"span",2,0),Y(4,1),s(),_(5,zi,1,1,"mat-pseudo-checkbox",3),_(6,Li,2,1,"span",4),C(7,"div",5)),t&2&&(f(i.multiple?0:-1),d(5),f(!i.multiple&&i.selected&&!i.hideSingleSelectionIndicator?5:-1),d(),f(i.group&&i.group._inert?6:-1),d(),u("matRippleTrigger",i._getHostElement())("matRippleDisabled",i.disabled||i.disableRipple))},dependencies:[pi,Tt],styles:[`.mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:"";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mat-list-list-item-selected-container-color: var(--mat-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:""}
`],encapsulation:2,changeDetection:0})}return a})();function hi(a,n,e){if(e.length){let t=n.toArray(),i=e.toArray(),r=0;for(let m=0;m<a+1;m++)t[m].group&&t[m].group===i[r]&&r++;return r}return 0}function ui(a,n,e,t){return a<e?a:a+n>e+t?Math.max(0,a-t+n):e}var ie=class{_multiple;_emitChanges;compareWith;_selection=new Set;_deselectedToEmit=[];_selectedToEmit=[];_selected;get selected(){return this._selected||(this._selected=Array.from(this._selection.values())),this._selected}changed=new v;constructor(n=!1,e,t=!0,i){this._multiple=n,this._emitChanges=t,this.compareWith=i,e&&e.length&&(n?e.forEach(r=>this._markSelected(r)):this._markSelected(e[0]),this._selectedToEmit.length=0)}select(...n){this._verifyValueAssignment(n),n.forEach(t=>this._markSelected(t));let e=this._hasQueuedChanges();return this._emitChangeEvent(),e}deselect(...n){this._verifyValueAssignment(n),n.forEach(t=>this._unmarkSelected(t));let e=this._hasQueuedChanges();return this._emitChangeEvent(),e}setSelection(...n){this._verifyValueAssignment(n);let e=this.selected,t=new Set(n.map(r=>this._getConcreteValue(r)));n.forEach(r=>this._markSelected(r)),e.filter(r=>!t.has(this._getConcreteValue(r,t))).forEach(r=>this._unmarkSelected(r));let i=this._hasQueuedChanges();return this._emitChangeEvent(),i}toggle(n){return this.isSelected(n)?this.deselect(n):this.select(n)}clear(n=!0){this._unmarkAll();let e=this._hasQueuedChanges();return n&&this._emitChangeEvent(),e}isSelected(n){return this._selection.has(this._getConcreteValue(n))}isEmpty(){return this._selection.size===0}hasValue(){return!this.isEmpty()}sort(n){this._multiple&&this.selected&&this._selected.sort(n)}isMultipleSelection(){return this._multiple}_emitChangeEvent(){this._selected=null,(this._selectedToEmit.length||this._deselectedToEmit.length)&&(this.changed.next({source:this,added:this._selectedToEmit,removed:this._deselectedToEmit}),this._deselectedToEmit=[],this._selectedToEmit=[])}_markSelected(n){n=this._getConcreteValue(n),this.isSelected(n)||(this._multiple||this._unmarkAll(),this.isSelected(n)||this._selection.add(n),this._emitChanges&&this._selectedToEmit.push(n))}_unmarkSelected(n){n=this._getConcreteValue(n),this.isSelected(n)&&(this._selection.delete(n),this._emitChanges&&this._deselectedToEmit.push(n))}_unmarkAll(){this.isEmpty()||this._selection.forEach(n=>this._unmarkSelected(n))}_verifyValueAssignment(n){n.length>1&&this._multiple}_hasQueuedChanges(){return!!(this._deselectedToEmit.length||this._selectedToEmit.length)}_getConcreteValue(n,e){if(this.compareWith){e=e??this._selection;for(let t of e)if(this.compareWith(n,t))return t;return n}else return n}};var gi=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=x({type:a});static \u0275inj=y({imports:[S]})}return a})();var it=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=x({type:a});static \u0275inj=y({imports:[Rt,S,gi,H]})}return a})();var Wi=["trigger"],ji=["panel"],Gi=[[["mat-select-trigger"]],"*"],Ki=["mat-select-trigger","*"];function qi(a,n){if(a&1&&(o(0,"span",4),c(1),s()),a&2){let e=p();d(),K(e.placeholder)}}function Ui(a,n){a&1&&Y(0)}function Qi(a,n){if(a&1&&(o(0,"span",11),c(1),s()),a&2){let e=p(2);d(),K(e.triggerValue)}}function Yi(a,n){if(a&1&&(o(0,"span",5),_(1,Ui,1,0)(2,Qi,2,1,"span",11),s()),a&2){let e=p();d(),f(e.customTrigger?1:2)}}function $i(a,n){if(a&1){let e=z();o(0,"div",12,1),h("keydown",function(i){w(e);let r=p();return E(r._handleKeydown(i))}),Y(2,1),s()}if(a&2){let e=p();G(ht("mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open ",e._getPanelTheme())),A("mat-select-panel-animations-enabled",!e._animationsDisabled),u("ngClass",e.panelClass),g("id",e.id+"-panel")("aria-multiselectable",e.multiple)("aria-label",e.ariaLabel||null)("aria-labelledby",e._getPanelAriaLabelledby())}}var at=new k("mat-select-scroll-strategy",{providedIn:"root",factory:()=>{let a=l(Ge);return()=>Qe(a)}});function bi(a){let n=l(Ge);return()=>Qe(n)}var vi=new k("MAT_SELECT_CONFIG"),yi={provide:at,deps:[],useFactory:bi},xi=new k("MatSelectTrigger"),Ve=class{source;value;constructor(n,e){this.source=n,this.value=e}},ae=(()=>{class a{_viewportRuler=l(qt);_changeDetectorRef=l(Z);_elementRef=l(B);_dir=l(At,{optional:!0});_idGenerator=l(q);_renderer=l(ee);_parentFormField=l(Fe,{optional:!0});ngControl=l(Pe,{self:!0,optional:!0});_liveAnnouncer=l(Et);_defaultOptions=l(vi,{optional:!0});_animationsDisabled=J();_initialized=new v;_cleanupDetach;options;optionGroups;customTrigger;_positions=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom",panelClass:"mat-mdc-select-panel-above"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom",panelClass:"mat-mdc-select-panel-above"}];_scrollOptionIntoView(e){let t=this.options.toArray()[e];if(t){let i=this.panel.nativeElement,r=hi(e,this.options,this.optionGroups),m=t._getHostElement();e===0&&r===1?i.scrollTop=0:i.scrollTop=ui(m.offsetTop,m.offsetHeight,i.scrollTop,i.offsetHeight)}}_positioningSettled(){this._scrollOptionIntoView(this._keyManager.activeItemIndex||0)}_getChangeEvent(e){return new Ve(this,e)}_scrollStrategyFactory=l(at);_panelOpen=!1;_compareWith=(e,t)=>e===t;_uid=this._idGenerator.getId("mat-select-");_triggerAriaLabelledBy=null;_previousControl;_destroy=new v;_errorStateTracker;stateChanges=new v;disableAutomaticLabeling=!0;userAriaDescribedBy;_selectionModel;_keyManager;_preferredOverlayOrigin;_overlayWidth;_onChange=()=>{};_onTouched=()=>{};_valueId=this._idGenerator.getId("mat-select-value-");_scrollStrategy;_overlayPanelClass=this._defaultOptions?.overlayPanelClass||"";get focused(){return this._focused||this._panelOpen}_focused=!1;controlType="mat-select";trigger;panel;_overlayDir;panelClass;disabled=!1;get disableRipple(){return this._disableRipple()}set disableRipple(e){this._disableRipple.set(e)}_disableRipple=V(!1);tabIndex=0;get hideSingleSelectionIndicator(){return this._hideSingleSelectionIndicator}set hideSingleSelectionIndicator(e){this._hideSingleSelectionIndicator=e,this._syncParentProperties()}_hideSingleSelectionIndicator=this._defaultOptions?.hideSingleSelectionIndicator??!1;get placeholder(){return this._placeholder}set placeholder(e){this._placeholder=e,this.stateChanges.next()}_placeholder;get required(){return this._required??this.ngControl?.control?.hasValidator(Oe.required)??!1}set required(e){this._required=e,this.stateChanges.next()}_required;get multiple(){return this._multiple}set multiple(e){this._selectionModel,this._multiple=e}_multiple=!1;disableOptionCentering=this._defaultOptions?.disableOptionCentering??!1;get compareWith(){return this._compareWith}set compareWith(e){this._compareWith=e,this._selectionModel&&this._initializeSelection()}get value(){return this._value}set value(e){this._assignValue(e)&&this._onChange(e)}_value;ariaLabel="";ariaLabelledby;get errorStateMatcher(){return this._errorStateTracker.matcher}set errorStateMatcher(e){this._errorStateTracker.matcher=e}typeaheadDebounceInterval;sortComparator;get id(){return this._id}set id(e){this._id=e||this._uid,this.stateChanges.next()}_id;get errorState(){return this._errorStateTracker.errorState}set errorState(e){this._errorStateTracker.errorState=e}panelWidth=this._defaultOptions&&typeof this._defaultOptions.panelWidth<"u"?this._defaultOptions.panelWidth:"auto";canSelectNullableOptions=this._defaultOptions?.canSelectNullableOptions??!1;optionSelectionChanges=st(()=>{let e=this.options;return e?e.changes.pipe(We(e),je(()=>oe(...e.map(t=>t.onSelectionChange)))):this._initialized.pipe(je(()=>this.optionSelectionChanges))});openedChange=new N;_openedStream=this.openedChange.pipe(He(e=>e),Ne(()=>{}));_closedStream=this.openedChange.pipe(He(e=>!e),Ne(()=>{}));selectionChange=new N;valueChange=new N;constructor(){let e=l(De),t=l(Te,{optional:!0}),i=l(Ie,{optional:!0}),r=l(new vt("tabindex"),{optional:!0});this.ngControl&&(this.ngControl.valueAccessor=this),this._defaultOptions?.typeaheadDebounceInterval!=null&&(this.typeaheadDebounceInterval=this._defaultOptions.typeaheadDebounceInterval),this._errorStateTracker=new Ae(e,this.ngControl,i,t,this.stateChanges),this._scrollStrategy=this._scrollStrategyFactory(),this.tabIndex=r==null?0:parseInt(r)||0,this.id=this.id}ngOnInit(){this._selectionModel=new ie(this.multiple),this.stateChanges.next(),this._viewportRuler.change().pipe(W(this._destroy)).subscribe(()=>{this.panelOpen&&(this._overlayWidth=this._getOverlayWidth(this._preferredOverlayOrigin),this._changeDetectorRef.detectChanges())})}ngAfterContentInit(){this._initialized.next(),this._initialized.complete(),this._initKeyManager(),this._selectionModel.changed.pipe(W(this._destroy)).subscribe(e=>{e.added.forEach(t=>t.select()),e.removed.forEach(t=>t.deselect())}),this.options.changes.pipe(We(null),W(this._destroy)).subscribe(()=>{this._resetOptions(),this._initializeSelection()})}ngDoCheck(){let e=this._getTriggerAriaLabelledby(),t=this.ngControl;if(e!==this._triggerAriaLabelledBy){let i=this._elementRef.nativeElement;this._triggerAriaLabelledBy=e,e?i.setAttribute("aria-labelledby",e):i.removeAttribute("aria-labelledby")}t&&(this._previousControl!==t.control&&(this._previousControl!==void 0&&t.disabled!==null&&t.disabled!==this.disabled&&(this.disabled=t.disabled),this._previousControl=t.control),this.updateErrorState())}ngOnChanges(e){(e.disabled||e.userAriaDescribedBy)&&this.stateChanges.next(),e.typeaheadDebounceInterval&&this._keyManager&&this._keyManager.withTypeAhead(this.typeaheadDebounceInterval)}ngOnDestroy(){this._cleanupDetach?.(),this._keyManager?.destroy(),this._destroy.next(),this._destroy.complete(),this.stateChanges.complete(),this._clearFromModal()}toggle(){this.panelOpen?this.close():this.open()}open(){this._canOpen()&&(this._parentFormField&&(this._preferredOverlayOrigin=this._parentFormField.getConnectedOverlayOrigin()),this._cleanupDetach?.(),this._overlayWidth=this._getOverlayWidth(this._preferredOverlayOrigin),this._applyModalPanelOwnership(),this._panelOpen=!0,this._overlayDir.positionChange.pipe(lt(1)).subscribe(()=>{this._changeDetectorRef.detectChanges(),this._positioningSettled()}),this._overlayDir.attachOverlay(),this._keyManager.withHorizontalOrientation(null),this._highlightCorrectOption(),this._changeDetectorRef.markForCheck(),this.stateChanges.next(),Promise.resolve().then(()=>this.openedChange.emit(!0)))}_trackedModal=null;_applyModalPanelOwnership(){let e=this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal="true"]');if(!e)return;let t=`${this.id}-panel`;this._trackedModal&&qe(this._trackedModal,"aria-owns",t),Pt(e,"aria-owns",t),this._trackedModal=e}_clearFromModal(){if(!this._trackedModal)return;let e=`${this.id}-panel`;qe(this._trackedModal,"aria-owns",e),this._trackedModal=null}close(){this._panelOpen&&(this._panelOpen=!1,this._exitAndDetach(),this._keyManager.withHorizontalOrientation(this._isRtl()?"rtl":"ltr"),this._changeDetectorRef.markForCheck(),this._onTouched(),this.stateChanges.next(),Promise.resolve().then(()=>this.openedChange.emit(!1)))}_exitAndDetach(){if(this._animationsDisabled||!this.panel){this._detachOverlay();return}this._cleanupDetach?.(),this._cleanupDetach=()=>{t(),clearTimeout(i),this._cleanupDetach=void 0};let e=this.panel.nativeElement,t=this._renderer.listen(e,"animationend",r=>{r.animationName==="_mat-select-exit"&&(this._cleanupDetach?.(),this._detachOverlay())}),i=setTimeout(()=>{this._cleanupDetach?.(),this._detachOverlay()},200);e.classList.add("mat-select-panel-exit")}_detachOverlay(){this._overlayDir.detachOverlay(),this._changeDetectorRef.markForCheck()}writeValue(e){this._assignValue(e)}registerOnChange(e){this._onChange=e}registerOnTouched(e){this._onTouched=e}setDisabledState(e){this.disabled=e,this._changeDetectorRef.markForCheck(),this.stateChanges.next()}get panelOpen(){return this._panelOpen}get selected(){return this.multiple?this._selectionModel?.selected||[]:this._selectionModel?.selected[0]}get triggerValue(){if(this.empty)return"";if(this._multiple){let e=this._selectionModel.selected.map(t=>t.viewValue);return this._isRtl()&&e.reverse(),e.join(", ")}return this._selectionModel.selected[0].viewValue}updateErrorState(){this._errorStateTracker.updateErrorState()}_isRtl(){return this._dir?this._dir.value==="rtl":!1}_handleKeydown(e){this.disabled||(this.panelOpen?this._handleOpenKeydown(e):this._handleClosedKeydown(e))}_handleClosedKeydown(e){let t=e.keyCode,i=t===40||t===38||t===37||t===39,r=t===13||t===32,m=this._keyManager;if(!m.isTyping()&&r&&!X(e)||(this.multiple||e.altKey)&&i)e.preventDefault(),this.open();else if(!this.multiple){let M=this.selected;m.onKeydown(e);let P=this.selected;P&&M!==P&&this._liveAnnouncer.announce(P.viewValue,1e4)}}_handleOpenKeydown(e){let t=this._keyManager,i=e.keyCode,r=i===40||i===38,m=t.isTyping();if(r&&e.altKey)e.preventDefault(),this.close();else if(!m&&(i===13||i===32)&&t.activeItem&&!X(e))e.preventDefault(),t.activeItem._selectViaInteraction();else if(!m&&this._multiple&&i===65&&e.ctrlKey){e.preventDefault();let M=this.options.some(P=>!P.disabled&&!P.selected);this.options.forEach(P=>{P.disabled||(M?P.select():P.deselect())})}else{let M=t.activeItemIndex;t.onKeydown(e),this._multiple&&r&&e.shiftKey&&t.activeItem&&t.activeItemIndex!==M&&t.activeItem._selectViaInteraction()}}_handleOverlayKeydown(e){e.keyCode===27&&!X(e)&&(e.preventDefault(),this.close())}_onFocus(){this.disabled||(this._focused=!0,this.stateChanges.next())}_onBlur(){this._focused=!1,this._keyManager?.cancelTypeahead(),!this.disabled&&!this.panelOpen&&(this._onTouched(),this._changeDetectorRef.markForCheck(),this.stateChanges.next())}_getPanelTheme(){return this._parentFormField?`mat-${this._parentFormField.color}`:""}get empty(){return!this._selectionModel||this._selectionModel.isEmpty()}_initializeSelection(){Promise.resolve().then(()=>{this.ngControl&&(this._value=this.ngControl.value),this._setSelectionByValue(this._value),this.stateChanges.next()})}_setSelectionByValue(e){if(this.options.forEach(t=>t.setInactiveStyles()),this._selectionModel.clear(),this.multiple&&e)Array.isArray(e),e.forEach(t=>this._selectOptionByValue(t)),this._sortValues();else{let t=this._selectOptionByValue(e);t?this._keyManager.updateActiveItem(t):this.panelOpen||this._keyManager.updateActiveItem(-1)}this._changeDetectorRef.markForCheck()}_selectOptionByValue(e){let t=this.options.find(i=>{if(this._selectionModel.isSelected(i))return!1;try{return(i.value!=null||this.canSelectNullableOptions)&&this._compareWith(i.value,e)}catch{return!1}});return t&&this._selectionModel.select(t),t}_assignValue(e){return e!==this._value||this._multiple&&Array.isArray(e)?(this.options&&this._setSelectionByValue(e),this._value=e,!0):!1}_skipPredicate=e=>this.panelOpen?!1:e.disabled;_getOverlayWidth(e){return this.panelWidth==="auto"?(e instanceof Ye?e.elementRef:e||this._elementRef).nativeElement.getBoundingClientRect().width:this.panelWidth===null?"":this.panelWidth}_syncParentProperties(){if(this.options)for(let e of this.options)e._changeDetectorRef.markForCheck()}_initKeyManager(){this._keyManager=new Ot(this.options).withTypeAhead(this.typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl()?"rtl":"ltr").withHomeAndEnd().withPageUpDown().withAllowedModifierKeys(["shiftKey"]).skipPredicate(this._skipPredicate),this._keyManager.tabOut.subscribe(()=>{this.panelOpen&&(!this.multiple&&this._keyManager.activeItem&&this._keyManager.activeItem._selectViaInteraction(),this.focus(),this.close())}),this._keyManager.change.subscribe(()=>{this._panelOpen&&this.panel?this._scrollOptionIntoView(this._keyManager.activeItemIndex||0):!this._panelOpen&&!this.multiple&&this._keyManager.activeItem&&this._keyManager.activeItem._selectViaInteraction()})}_resetOptions(){let e=oe(this.options.changes,this._destroy);this.optionSelectionChanges.pipe(W(e)).subscribe(t=>{this._onSelect(t.source,t.isUserInput),t.isUserInput&&!this.multiple&&this._panelOpen&&(this.close(),this.focus())}),oe(...this.options.map(t=>t._stateChanges)).pipe(W(e)).subscribe(()=>{this._changeDetectorRef.detectChanges(),this.stateChanges.next()})}_onSelect(e,t){let i=this._selectionModel.isSelected(e);!this.canSelectNullableOptions&&e.value==null&&!this._multiple?(e.deselect(),this._selectionModel.clear(),this.value!=null&&this._propagateChanges(e.value)):(i!==e.selected&&(e.selected?this._selectionModel.select(e):this._selectionModel.deselect(e)),t&&this._keyManager.setActiveItem(e),this.multiple&&(this._sortValues(),t&&this.focus())),i!==this._selectionModel.isSelected(e)&&this._propagateChanges(),this.stateChanges.next()}_sortValues(){if(this.multiple){let e=this.options.toArray();this._selectionModel.sort((t,i)=>this.sortComparator?this.sortComparator(t,i,e):e.indexOf(t)-e.indexOf(i)),this.stateChanges.next()}}_propagateChanges(e){let t;this.multiple?t=this.selected.map(i=>i.value):t=this.selected?this.selected.value:e,this._value=t,this.valueChange.emit(t),this._onChange(t),this.selectionChange.emit(this._getChangeEvent(t)),this._changeDetectorRef.markForCheck()}_highlightCorrectOption(){if(this._keyManager)if(this.empty){let e=-1;for(let t=0;t<this.options.length;t++)if(!this.options.get(t).disabled){e=t;break}this._keyManager.setActiveItem(e)}else this._keyManager.setActiveItem(this._selectionModel.selected[0])}_canOpen(){return!this._panelOpen&&!this.disabled&&this.options?.length>0&&!!this._overlayDir}focus(e){this._elementRef.nativeElement.focus(e)}_getPanelAriaLabelledby(){if(this.ariaLabel)return null;let e=this._parentFormField?.getLabelId()||null,t=e?e+" ":"";return this.ariaLabelledby?t+this.ariaLabelledby:e}_getAriaActiveDescendant(){return this.panelOpen&&this._keyManager&&this._keyManager.activeItem?this._keyManager.activeItem.id:null}_getTriggerAriaLabelledby(){if(this.ariaLabel)return null;let e=this._parentFormField?.getLabelId()||"";return this.ariaLabelledby&&(e+=" "+this.ariaLabelledby),e||(e=this._valueId),e}get describedByIds(){return this._elementRef.nativeElement.getAttribute("aria-describedby")?.split(" ")||[]}setDescribedByIds(e){e.length?this._elementRef.nativeElement.setAttribute("aria-describedby",e.join(" ")):this._elementRef.nativeElement.removeAttribute("aria-describedby")}onContainerClick(){this.focus(),this.open()}get shouldLabelFloat(){return this.panelOpen||!this.empty||this.focused&&!!this.placeholder}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=O({type:a,selectors:[["mat-select"]],contentQueries:function(t,i,r){if(t&1&&(ve(r,xi,5),ve(r,H,5),ve(r,tt,5)),t&2){let m;I(m=D())&&(i.customTrigger=m.first),I(m=D())&&(i.options=m),I(m=D())&&(i.optionGroups=m)}},viewQuery:function(t,i){if(t&1&&(j(Wi,5),j(ji,5),j($e,5)),t&2){let r;I(r=D())&&(i.trigger=r.first),I(r=D())&&(i.panel=r.first),I(r=D())&&(i._overlayDir=r.first)}},hostAttrs:["role","combobox","aria-haspopup","listbox",1,"mat-mdc-select"],hostVars:19,hostBindings:function(t,i){t&1&&h("keydown",function(m){return i._handleKeydown(m)})("focus",function(){return i._onFocus()})("blur",function(){return i._onBlur()}),t&2&&(g("id",i.id)("tabindex",i.disabled?-1:i.tabIndex)("aria-controls",i.panelOpen?i.id+"-panel":null)("aria-expanded",i.panelOpen)("aria-label",i.ariaLabel||null)("aria-required",i.required.toString())("aria-disabled",i.disabled.toString())("aria-invalid",i.errorState)("aria-activedescendant",i._getAriaActiveDescendant()),A("mat-mdc-select-disabled",i.disabled)("mat-mdc-select-invalid",i.errorState)("mat-mdc-select-required",i.required)("mat-mdc-select-empty",i.empty)("mat-mdc-select-multiple",i.multiple))},inputs:{userAriaDescribedBy:[0,"aria-describedby","userAriaDescribedBy"],panelClass:"panelClass",disabled:[2,"disabled","disabled",b],disableRipple:[2,"disableRipple","disableRipple",b],tabIndex:[2,"tabIndex","tabIndex",e=>e==null?0:T(e)],hideSingleSelectionIndicator:[2,"hideSingleSelectionIndicator","hideSingleSelectionIndicator",b],placeholder:"placeholder",required:[2,"required","required",b],multiple:[2,"multiple","multiple",b],disableOptionCentering:[2,"disableOptionCentering","disableOptionCentering",b],compareWith:"compareWith",value:"value",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],errorStateMatcher:"errorStateMatcher",typeaheadDebounceInterval:[2,"typeaheadDebounceInterval","typeaheadDebounceInterval",T],sortComparator:"sortComparator",id:"id",panelWidth:"panelWidth",canSelectNullableOptions:[2,"canSelectNullableOptions","canSelectNullableOptions",b]},outputs:{openedChange:"openedChange",_openedStream:"opened",_closedStream:"closed",selectionChange:"selectionChange",valueChange:"valueChange"},exportAs:["matSelect"],features:[te([{provide:Re,useExisting:a},{provide:et,useExisting:a}]),de],ngContentSelectors:Ki,decls:11,vars:9,consts:[["fallbackOverlayOrigin","cdkOverlayOrigin","trigger",""],["panel",""],["cdk-overlay-origin","",1,"mat-mdc-select-trigger",3,"click"],[1,"mat-mdc-select-value"],[1,"mat-mdc-select-placeholder","mat-mdc-select-min-line"],[1,"mat-mdc-select-value-text"],[1,"mat-mdc-select-arrow-wrapper"],[1,"mat-mdc-select-arrow"],["viewBox","0 0 24 24","width","24px","height","24px","focusable","false","aria-hidden","true"],["d","M7 10l5 5 5-5z"],["cdk-connected-overlay","","cdkConnectedOverlayLockPosition","","cdkConnectedOverlayHasBackdrop","","cdkConnectedOverlayBackdropClass","cdk-overlay-transparent-backdrop",3,"detach","backdropClick","overlayKeydown","cdkConnectedOverlayDisableClose","cdkConnectedOverlayPanelClass","cdkConnectedOverlayScrollStrategy","cdkConnectedOverlayOrigin","cdkConnectedOverlayPositions","cdkConnectedOverlayWidth","cdkConnectedOverlayFlexibleDimensions"],[1,"mat-mdc-select-min-line"],["role","listbox","tabindex","-1",3,"keydown","ngClass"]],template:function(t,i){if(t&1){let r=z();be(Gi),o(0,"div",2,0),h("click",function(){return w(r),E(i.open())}),o(3,"div",3),_(4,qi,2,1,"span",4)(5,Yi,3,1,"span",5),s(),o(6,"div",6)(7,"div",7),F(),o(8,"svg",8),C(9,"path",9),s()()()(),pe(10,$i,3,10,"ng-template",10),h("detach",function(){return w(r),E(i.close())})("backdropClick",function(){return w(r),E(i.close())})("overlayKeydown",function(M){return w(r),E(i._handleOverlayKeydown(M))})}if(t&2){let r=$(1);d(3),g("id",i._valueId),d(),f(i.empty?4:5),d(6),u("cdkConnectedOverlayDisableClose",!0)("cdkConnectedOverlayPanelClass",i._overlayPanelClass)("cdkConnectedOverlayScrollStrategy",i._scrollStrategy)("cdkConnectedOverlayOrigin",i._preferredOverlayOrigin||r)("cdkConnectedOverlayPositions",i._positions)("cdkConnectedOverlayWidth",i._overlayWidth)("cdkConnectedOverlayFlexibleDimensions",!0)}},dependencies:[Ye,$e,yt],styles:[`@keyframes _mat-select-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-select-exit{from{opacity:1}to{opacity:0}}.mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color, var(--mat-sys-on-surface));font-family:var(--mat-select-trigger-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-select-trigger-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-select-trigger-text-size, var(--mat-sys-body-large-size));font-weight:var(--mat-select-trigger-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mat-select-trigger-text-tracking, var(--mat-sys-body-large-tracking))}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-disabled .mat-mdc-select-placeholder{color:var(--mat-select-disabled-trigger-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color, var(--mat-sys-error))}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}@media(forced-colors: active){.mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .mat-mdc-select-arrow svg{fill:GrayText}}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:relative;background-color:var(--mat-select-panel-background-color, var(--mat-sys-surface-container))}@media(forced-colors: active){div.mat-mdc-select-panel{outline:solid 1px}}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}.mat-select-panel-animations-enabled{animation:_mat-select-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-select-panel-animations-enabled.mat-select-panel-exit{animation:_mat-select-exit 100ms linear}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-form-field:not(.mat-form-field-animations-enabled) .mat-mdc-select-placeholder,._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:" ";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform, translateY(-8px))}
`],encapsulation:2,changeDetection:0})}return a})();var ne=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=x({type:a});static \u0275inj=y({providers:[yi],imports:[Qt,it,S,Ut,U,it,S]})}return a})();var Xi=["determinateSpinner"];function Ji(a,n){if(a&1&&(F(),o(0,"svg",11),C(1,"circle",12),s()),a&2){let e=p();g("viewBox",e._viewBox()),d(),ye("stroke-dasharray",e._strokeCircumference(),"px")("stroke-dashoffset",e._strokeCircumference()/2,"px")("stroke-width",e._circleStrokeWidth(),"%"),g("r",e._circleRadius())}}var ea=new k("mat-progress-spinner-default-options",{providedIn:"root",factory:ta});function ta(){return{diameter:Ci}}var Ci=100,ia=10,Mi=(()=>{class a{_elementRef=l(B);_noopAnimations;get color(){return this._color||this._defaultColor}set color(e){this._color=e}_color;_defaultColor="primary";_determinateCircle;constructor(){let e=l(ea);this._noopAnimations=J()&&!!e&&!e._forceAnimations,this.mode=this._elementRef.nativeElement.nodeName.toLowerCase()==="mat-spinner"?"indeterminate":"determinate",e&&(e.color&&(this.color=this._defaultColor=e.color),e.diameter&&(this.diameter=e.diameter),e.strokeWidth&&(this.strokeWidth=e.strokeWidth))}mode;get value(){return this.mode==="determinate"?this._value:0}set value(e){this._value=Math.max(0,Math.min(100,e||0))}_value=0;get diameter(){return this._diameter}set diameter(e){this._diameter=e||0}_diameter=Ci;get strokeWidth(){return this._strokeWidth??this.diameter/10}set strokeWidth(e){this._strokeWidth=e||0}_strokeWidth;_circleRadius(){return(this.diameter-ia)/2}_viewBox(){let e=this._circleRadius()*2+this.strokeWidth;return`0 0 ${e} ${e}`}_strokeCircumference(){return 2*Math.PI*this._circleRadius()}_strokeDashOffset(){return this.mode==="determinate"?this._strokeCircumference()*(100-this._value)/100:null}_circleStrokeWidth(){return this.strokeWidth/this.diameter*100}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=O({type:a,selectors:[["mat-progress-spinner"],["mat-spinner"]],viewQuery:function(t,i){if(t&1&&j(Xi,5),t&2){let r;I(r=D())&&(i._determinateCircle=r.first)}},hostAttrs:["role","progressbar","tabindex","-1",1,"mat-mdc-progress-spinner","mdc-circular-progress"],hostVars:18,hostBindings:function(t,i){t&2&&(g("aria-valuemin",0)("aria-valuemax",100)("aria-valuenow",i.mode==="determinate"?i.value:null)("mode",i.mode),G("mat-"+i.color),ye("width",i.diameter,"px")("height",i.diameter,"px")("--mat-progress-spinner-size",i.diameter+"px")("--mat-progress-spinner-active-indicator-width",i.diameter+"px"),A("_mat-animation-noopable",i._noopAnimations)("mdc-circular-progress--indeterminate",i.mode==="indeterminate"))},inputs:{color:"color",mode:"mode",value:[2,"value","value",T],diameter:[2,"diameter","diameter",T],strokeWidth:[2,"strokeWidth","strokeWidth",T]},exportAs:["matProgressSpinner"],decls:14,vars:11,consts:[["circle",""],["determinateSpinner",""],["aria-hidden","true",1,"mdc-circular-progress__determinate-container"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__determinate-circle-graphic"],["cx","50%","cy","50%",1,"mdc-circular-progress__determinate-circle"],["aria-hidden","true",1,"mdc-circular-progress__indeterminate-container"],[1,"mdc-circular-progress__spinner-layer"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-left"],[3,"ngTemplateOutlet"],[1,"mdc-circular-progress__gap-patch"],[1,"mdc-circular-progress__circle-clipper","mdc-circular-progress__circle-right"],["xmlns","http://www.w3.org/2000/svg","focusable","false",1,"mdc-circular-progress__indeterminate-circle-graphic"],["cx","50%","cy","50%"]],template:function(t,i){if(t&1&&(pe(0,Ji,2,8,"ng-template",null,0,ft),o(2,"div",2,1),F(),o(4,"svg",3),C(5,"circle",4),s()(),le(),o(6,"div",5)(7,"div",6)(8,"div",7),_e(9,8),s(),o(10,"div",9),_e(11,8),s(),o(12,"div",10),_e(13,8),s()()()),t&2){let r=$(1);d(4),g("viewBox",i._viewBox()),d(),ye("stroke-dasharray",i._strokeCircumference(),"px")("stroke-dashoffset",i._strokeDashOffset(),"px")("stroke-width",i._circleStrokeWidth(),"%"),g("r",i._circleRadius()),d(4),u("ngTemplateOutlet",r),d(2),u("ngTemplateOutlet",r),d(2),u("ngTemplateOutlet",r)}},dependencies:[xt],styles:[`.mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mat-progress-spinner-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mat-progress-spinner-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}
`],encapsulation:2,changeDetection:0})}return a})();var ki=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=x({type:a});static \u0275inj=y({imports:[S]})}return a})();function na(a,n){if(a&1&&(o(0,"mat-option",17),c(1),s()),a&2){let e=n.$implicit;u("value",e),d(),R(" ",e," ")}}function ra(a,n){if(a&1){let e=z();o(0,"mat-form-field",14)(1,"mat-select",16,0),h("selectionChange",function(i){w(e);let r=p(2);return E(r._changePageSize(i.value))}),ue(3,na,2,2,"mat-option",17,pt),s(),o(5,"div",18),h("click",function(){w(e);let i=$(2);return E(i.open())}),s()()}if(a&2){let e=p(2);u("appearance",e._formFieldAppearance)("color",e.color),d(),u("value",e.pageSize)("disabled",e.disabled)("aria-labelledby",e._pageSizeLabelId)("panelClass",e.selectConfig.panelClass||"")("disableOptionCentering",e.selectConfig.disableOptionCentering),d(2),ge(e._displayedPageSizeOptions)}}function oa(a,n){if(a&1&&(o(0,"div",15),c(1),s()),a&2){let e=p(2);d(),K(e.pageSize)}}function sa(a,n){if(a&1&&(o(0,"div",3)(1,"div",13),c(2),s(),_(3,ra,6,7,"mat-form-field",14),_(4,oa,2,1,"div",15),s()),a&2){let e=p();d(),g("id",e._pageSizeLabelId),d(),R(" ",e._intl.itemsPerPageLabel," "),d(),f(e._displayedPageSizeOptions.length>1?3:-1),d(),f(e._displayedPageSizeOptions.length<=1?4:-1)}}function la(a,n){if(a&1){let e=z();o(0,"button",19),h("click",function(){w(e);let i=p();return E(i._buttonClicked(0,i._previousButtonsDisabled()))}),F(),o(1,"svg",8),C(2,"path",20),s()()}if(a&2){let e=p();u("matTooltip",e._intl.firstPageLabel)("matTooltipDisabled",e._previousButtonsDisabled())("disabled",e._previousButtonsDisabled())("tabindex",e._previousButtonsDisabled()?-1:null),g("aria-label",e._intl.firstPageLabel)}}function ca(a,n){if(a&1){let e=z();o(0,"button",21),h("click",function(){w(e);let i=p();return E(i._buttonClicked(i.getNumberOfPages()-1,i._nextButtonsDisabled()))}),F(),o(1,"svg",8),C(2,"path",22),s()()}if(a&2){let e=p();u("matTooltip",e._intl.lastPageLabel)("matTooltipDisabled",e._nextButtonsDisabled())("disabled",e._nextButtonsDisabled())("tabindex",e._nextButtonsDisabled()?-1:null),g("aria-label",e._intl.lastPageLabel)}}var Be=(()=>{class a{changes=new v;itemsPerPageLabel="Items per page:";nextPageLabel="Next page";previousPageLabel="Previous page";firstPageLabel="First page";lastPageLabel="Last page";getRangeLabel=(e,t,i)=>{if(i==0||t==0)return`0 of ${i}`;i=Math.max(i,0);let r=e*t,m=r<i?Math.min(r+t,i):r+t;return`${r+1} \u2013 ${m} of ${i}`};static \u0275fac=function(t){return new(t||a)};static \u0275prov=se({token:a,factory:a.\u0275fac,providedIn:"root"})}return a})();function da(a){return a||new Be}var ma={provide:Be,deps:[[new ct,new dt,Be]],useFactory:da},pa=50;var ha=new k("MAT_PAGINATOR_DEFAULT_OPTIONS"),nt=(()=>{class a{_intl=l(Be);_changeDetectorRef=l(Z);_formFieldAppearance;_pageSizeLabelId=l(q).getId("mat-paginator-page-size-label-");_intlChanges;_isInitialized=!1;_initializedStream=new rt(1);color;get pageIndex(){return this._pageIndex}set pageIndex(e){this._pageIndex=Math.max(e||0,0),this._changeDetectorRef.markForCheck()}_pageIndex=0;get length(){return this._length}set length(e){this._length=e||0,this._changeDetectorRef.markForCheck()}_length=0;get pageSize(){return this._pageSize}set pageSize(e){this._pageSize=Math.max(e||0,0),this._updateDisplayedPageSizeOptions()}_pageSize;get pageSizeOptions(){return this._pageSizeOptions}set pageSizeOptions(e){this._pageSizeOptions=(e||[]).map(t=>T(t,0)),this._updateDisplayedPageSizeOptions()}_pageSizeOptions=[];hidePageSize=!1;showFirstLastButtons=!1;selectConfig={};disabled=!1;page=new N;_displayedPageSizeOptions;initialized=this._initializedStream;constructor(){let e=this._intl,t=l(ha,{optional:!0});if(this._intlChanges=e.changes.subscribe(()=>this._changeDetectorRef.markForCheck()),t){let{pageSize:i,pageSizeOptions:r,hidePageSize:m,showFirstLastButtons:M}=t;i!=null&&(this._pageSize=i),r!=null&&(this._pageSizeOptions=r),m!=null&&(this.hidePageSize=m),M!=null&&(this.showFirstLastButtons=M)}this._formFieldAppearance=t?.formFieldAppearance||"outline"}ngOnInit(){this._isInitialized=!0,this._updateDisplayedPageSizeOptions(),this._initializedStream.next()}ngOnDestroy(){this._initializedStream.complete(),this._intlChanges.unsubscribe()}nextPage(){this.hasNextPage()&&this._navigate(this.pageIndex+1)}previousPage(){this.hasPreviousPage()&&this._navigate(this.pageIndex-1)}firstPage(){this.hasPreviousPage()&&this._navigate(0)}lastPage(){this.hasNextPage()&&this._navigate(this.getNumberOfPages()-1)}hasPreviousPage(){return this.pageIndex>=1&&this.pageSize!=0}hasNextPage(){let e=this.getNumberOfPages()-1;return this.pageIndex<e&&this.pageSize!=0}getNumberOfPages(){return this.pageSize?Math.ceil(this.length/this.pageSize):0}_changePageSize(e){let t=this.pageIndex*this.pageSize,i=this.pageIndex;this.pageIndex=Math.floor(t/e)||0,this.pageSize=e,this._emitPageEvent(i)}_nextButtonsDisabled(){return this.disabled||!this.hasNextPage()}_previousButtonsDisabled(){return this.disabled||!this.hasPreviousPage()}_updateDisplayedPageSizeOptions(){this._isInitialized&&(this.pageSize||(this._pageSize=this.pageSizeOptions.length!=0?this.pageSizeOptions[0]:pa),this._displayedPageSizeOptions=this.pageSizeOptions.slice(),this._displayedPageSizeOptions.indexOf(this.pageSize)===-1&&this._displayedPageSizeOptions.push(this.pageSize),this._displayedPageSizeOptions.sort((e,t)=>e-t),this._changeDetectorRef.markForCheck())}_emitPageEvent(e){this.page.emit({previousPageIndex:e,pageIndex:this.pageIndex,pageSize:this.pageSize,length:this.length})}_navigate(e){let t=this.pageIndex;e!==t&&(this.pageIndex=e,this._emitPageEvent(t))}_buttonClicked(e,t){t||this._navigate(e)}static \u0275fac=function(t){return new(t||a)};static \u0275cmp=O({type:a,selectors:[["mat-paginator"]],hostAttrs:["role","group",1,"mat-mdc-paginator"],inputs:{color:"color",pageIndex:[2,"pageIndex","pageIndex",T],length:[2,"length","length",T],pageSize:[2,"pageSize","pageSize",T],pageSizeOptions:"pageSizeOptions",hidePageSize:[2,"hidePageSize","hidePageSize",b],showFirstLastButtons:[2,"showFirstLastButtons","showFirstLastButtons",b],selectConfig:"selectConfig",disabled:[2,"disabled","disabled",b]},outputs:{page:"page"},exportAs:["matPaginator"],decls:14,vars:14,consts:[["selectRef",""],[1,"mat-mdc-paginator-outer-container"],[1,"mat-mdc-paginator-container"],[1,"mat-mdc-paginator-page-size"],[1,"mat-mdc-paginator-range-actions"],["aria-live","polite",1,"mat-mdc-paginator-range-label"],["matIconButton","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-first",3,"matTooltip","matTooltipDisabled","disabled","tabindex"],["matIconButton","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-previous",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["viewBox","0 0 24 24","focusable","false","aria-hidden","true",1,"mat-mdc-paginator-icon"],["d","M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"],["matIconButton","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-next",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["d","M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"],["matIconButton","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-last",3,"matTooltip","matTooltipDisabled","disabled","tabindex"],[1,"mat-mdc-paginator-page-size-label"],[1,"mat-mdc-paginator-page-size-select",3,"appearance","color"],[1,"mat-mdc-paginator-page-size-value"],["hideSingleSelectionIndicator","",3,"selectionChange","value","disabled","aria-labelledby","panelClass","disableOptionCentering"],[3,"value"],[1,"mat-mdc-paginator-touch-target",3,"click"],["matIconButton","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-first",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["d","M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"],["matIconButton","","type","button","matTooltipPosition","above","disabledInteractive","",1,"mat-mdc-paginator-navigation-last",3,"click","matTooltip","matTooltipDisabled","disabled","tabindex"],["d","M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"]],template:function(t,i){t&1&&(o(0,"div",1)(1,"div",2),_(2,sa,5,4,"div",3),o(3,"div",4)(4,"div",5),c(5),s(),_(6,la,3,5,"button",6),o(7,"button",7),h("click",function(){return i._buttonClicked(i.pageIndex-1,i._previousButtonsDisabled())}),F(),o(8,"svg",8),C(9,"path",9),s()(),le(),o(10,"button",10),h("click",function(){return i._buttonClicked(i.pageIndex+1,i._nextButtonsDisabled())}),F(),o(11,"svg",8),C(12,"path",11),s()(),_(13,ca,3,5,"button",12),s()()()),t&2&&(d(2),f(i.hidePageSize?-1:2),d(3),R(" ",i._intl.getRangeLabel(i.pageIndex,i.pageSize,i.length)," "),d(),f(i.showFirstLastButtons?6:-1),d(),u("matTooltip",i._intl.previousPageLabel)("matTooltipDisabled",i._previousButtonsDisabled())("disabled",i._previousButtonsDisabled())("tabindex",i._previousButtonsDisabled()?-1:null),g("aria-label",i._intl.previousPageLabel),d(3),u("matTooltip",i._intl.nextPageLabel)("matTooltipDisabled",i._nextButtonsDisabled())("disabled",i._nextButtonsDisabled())("tabindex",i._nextButtonsDisabled()?-1:null),g("aria-label",i._intl.nextPageLabel),d(3),f(i.showFirstLastButtons?13:-1))},dependencies:[Q,ae,H,Dt,ri],styles:[`.mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color, var(--mat-sys-on-surface));background-color:var(--mat-paginator-container-background-color, var(--mat-sys-surface));font-family:var(--mat-paginator-container-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-paginator-container-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-paginator-container-text-size, var(--mat-sys-body-small-size));font-weight:var(--mat-paginator-container-text-weight, var(--mat-sys-body-small-weight));letter-spacing:var(--mat-paginator-container-text-tracking, var(--mat-sys-body-small-tracking));--mat-form-field-container-height: var(--mat-paginator-form-field-container-height, 40px);--mat-form-field-container-vertical-padding: var(--mat-paginator-form-field-container-vertical-padding, 8px)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size, var(--mat-sys-body-small-size))}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size, 56px)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}@media(forced-colors: active){.mat-mdc-icon-button[aria-disabled] .mat-mdc-paginator-icon,.mat-mdc-paginator-icon{fill:currentColor}.mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}.mat-mdc-paginator-range-actions .mat-mdc-icon-button[aria-disabled]{color:GrayText}}.mat-mdc-paginator-touch-target{display:var(--mat-paginator-touch-target-display, block);position:absolute;top:50%;left:50%;width:84px;height:48px;background-color:rgba(0,0,0,0);transform:translate(-50%, -50%);cursor:pointer}
`],encapsulation:2,changeDetection:0})}return a})(),Si=(()=>{class a{static \u0275fac=function(t){return new(t||a)};static \u0275mod=x({type:a});static \u0275inj=y({providers:[ma],imports:[Ee,ne,oi,nt]})}return a})();var ga=()=>[12,24,48],_a=(a,n)=>n.id;function fa(a,n){if(a&1&&c(0),a&2){let e=p();R(" ",e.totalPigeons()," pigeons captured ")}}function ba(a,n){a&1&&c(0," No pigeons captured yet ")}function va(a,n){a&1&&(o(0,"div",22),C(1,"mat-spinner"),o(2,"p"),c(3,"Loading your pigeon deck..."),s()())}function ya(a,n){a&1&&(o(0,"div",23)(1,"mat-icon",25),c(2,"pets"),s(),o(3,"h2"),c(4,"No Pigeons Yet"),s(),o(5,"p"),c(6,"Start your pigeon collection by capturing your first pigeon!"),s(),o(7,"button",6)(8,"mat-icon"),c(9,"add_a_photo"),s(),c(10," Capture Your First Pigeon "),s()())}function xa(a,n){if(a&1){let e=z();o(0,"div",24)(1,"mat-icon",26),c(2,"search_off"),s(),o(3,"h2"),c(4,"No Pigeons Found"),s(),o(5,"p"),c(6,"Try adjusting your search or filter criteria."),s(),o(7,"button",27),h("click",function(){w(e);let i=p();return E(i.clearFilters())}),o(8,"mat-icon"),c(9,"clear"),s(),c(10," Clear Filters "),s()()}}function Ca(a,n){if(a&1&&(o(0,"mat-card",31)(1,"mat-card-header")(2,"div",32)(3,"mat-icon"),c(4,"pets"),s()(),o(5,"mat-card-title"),c(6),s(),o(7,"mat-card-subtitle")(8,"mat-chip-set")(9,"mat-chip"),c(10),s(),o(11,"mat-chip"),c(12),gt(13,"titlecase"),s()()()(),o(14,"mat-card-content")(15,"p",33),c(16),s(),o(17,"div",34)(18,"small")(19,"mat-icon"),c(20,"schedule"),s(),c(21),s()()(),o(22,"mat-card-actions")(23,"button",35)(24,"mat-icon"),c(25,"visibility"),s(),c(26," View Details "),s(),o(27,"button",36)(28,"mat-icon"),c(29,"share"),s(),c(30," Share "),s()()()),a&2){let e=n.$implicit,t=p(2);G("gang-"+e.gang.toLowerCase()),d(6),K(e.basePigeon.name),d(3),G("gang-chip gang-"+e.gang.toLowerCase()),d(),R(" Gang ",e.gang," "),d(),G("distinctiveness-chip "+e.basePigeon.distinctiveness),d(),R(" ",_t(13,11,e.basePigeon.distinctiveness)," "),d(4),K(e.basePigeon.description),d(5),R(" Captured ",t.formatDate(e.captureDate)," ")}}function Ma(a,n){if(a&1){let e=z();o(0,"mat-paginator",37),h("page",function(i){w(e);let r=p(2);return E(r.onPageChange(i))}),s()}if(a&2){let e=p(2);u("length",e.totalPigeons())("pageSize",e.pageSize)("pageSizeOptions",ut(4,ga))("pageIndex",e.currentPage)}}function ka(a,n){if(a&1&&(o(0,"div",28),ue(1,Ca,31,13,"mat-card",29,_a),s(),_(3,Ma,1,5,"mat-paginator",30)),a&2){let e=p();d(),ge(e.filteredPigeons()),d(2),f(e.totalPigeons()>e.pageSize?3:-1)}}var wi=class a{firebaseService=l(Zt);snackBar=l(Yt);destroy$=new v;pigeons=V([]);filteredPigeons=V([]);totalPigeons=V(0);isLoading=V(!0);searchTerm="";selectedGang="";selectedDistinctiveness="";currentPage=0;pageSize=12;ngOnInit(){this.loadPigeons()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}loadPigeons(){this.isLoading.set(!0),this.firebaseService.getTrainerPigeondex(this.pageSize,this.currentPage*this.pageSize).pipe(W(this.destroy$)).subscribe({next:n=>{this.pigeons.set(n.pigeons),this.totalPigeons.set(n.total),this.applyFilters(),this.isLoading.set(!1)},error:n=>{console.error("Failed to load pigeon deck:",n),this.snackBar.open("Failed to load your pigeon deck","Close",{duration:5e3}),this.isLoading.set(!1)}})}onSearchChange(){setTimeout(()=>this.applyFilters(),300)}onFilterChange(){this.applyFilters()}applyFilters(){let n=[...this.pigeons()];if(this.searchTerm.trim()){let e=this.searchTerm.toLowerCase();n=n.filter(t=>t.basePigeon.name.toLowerCase().includes(e)||t.basePigeon.description.toLowerCase().includes(e)||t.gang.toLowerCase().includes(e))}this.selectedGang&&(n=n.filter(e=>e.gang===this.selectedGang)),this.selectedDistinctiveness&&(n=n.filter(e=>e.basePigeon.distinctiveness===this.selectedDistinctiveness)),this.filteredPigeons.set(n)}clearFilters(){this.searchTerm="",this.selectedGang="",this.selectedDistinctiveness="",this.applyFilters()}onPageChange(n){this.currentPage=n.pageIndex,this.pageSize=n.pageSize,this.loadPigeons()}formatDate(n){return new Date(n).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}static \u0275fac=function(e){return new(e||a)};static \u0275cmp=O({type:a,selectors:[["app-pigeon-deck"]],decls:54,vars:8,consts:[[1,"deck-container"],[1,"deck-header"],[1,"header-content"],[1,"deck-title"],[1,"deck-subtitle"],[1,"header-actions"],["mat-raised-button","","color","primary","routerLink","/"],[1,"filters-section"],["appearance","outline",1,"search-field"],["matInput","","placeholder","Search by name, gang, or characteristics...",3,"ngModelChange","ngModel"],["matSuffix",""],["appearance","outline",1,"filter-field"],[3,"valueChange","selectionChange","value"],["value",""],["value","ONE"],["value","TWO"],["value","THREE"],["value","FOUR"],["value","FIVE"],["value","common"],["value","unusual"],["value","rare"],[1,"loading-container"],[1,"empty-state"],[1,"no-results-state"],[1,"empty-icon"],[1,"no-results-icon"],["mat-button","",3,"click"],[1,"pigeons-grid"],[1,"pigeon-card",3,"class"],["showFirstLastButtons","",3,"length","pageSize","pageSizeOptions","pageIndex"],[1,"pigeon-card"],["mat-card-avatar","",1,"pigeon-avatar"],[1,"pigeon-description"],[1,"capture-info"],["mat-button","","color","primary"],["mat-button",""],["showFirstLastButtons","",3,"page","length","pageSize","pageSizeOptions","pageIndex"]],template:function(e,t){e&1&&(o(0,"div",0)(1,"div",1)(2,"div",2)(3,"h1",3)(4,"mat-icon"),c(5,"collections"),s(),c(6," My Pigeon Deck "),s(),o(7,"p",4),_(8,fa,1,1)(9,ba,1,0),s()(),o(10,"div",5)(11,"button",6)(12,"mat-icon"),c(13,"add_a_photo"),s(),c(14," Capture New Pigeon "),s()()(),o(15,"div",7)(16,"mat-form-field",8)(17,"mat-label"),c(18,"Search pigeons"),s(),o(19,"input",9),Me("ngModelChange",function(r){return Ce(t.searchTerm,r)||(t.searchTerm=r),r}),h("ngModelChange",function(){return t.onSearchChange()}),s(),o(20,"mat-icon",10),c(21,"search"),s()(),o(22,"mat-form-field",11)(23,"mat-label"),c(24,"Filter by Gang"),s(),o(25,"mat-select",12),Me("valueChange",function(r){return Ce(t.selectedGang,r)||(t.selectedGang=r),r}),h("selectionChange",function(){return t.onFilterChange()}),o(26,"mat-option",13),c(27,"All Gangs"),s(),o(28,"mat-option",14),c(29,"Gang One"),s(),o(30,"mat-option",15),c(31,"Gang Two"),s(),o(32,"mat-option",16),c(33,"Gang Three"),s(),o(34,"mat-option",17),c(35,"Gang Four"),s(),o(36,"mat-option",18),c(37,"Gang Five"),s()()(),o(38,"mat-form-field",11)(39,"mat-label"),c(40,"Filter by Distinctiveness"),s(),o(41,"mat-select",12),Me("valueChange",function(r){return Ce(t.selectedDistinctiveness,r)||(t.selectedDistinctiveness=r),r}),h("selectionChange",function(){return t.onFilterChange()}),o(42,"mat-option",13),c(43,"All Types"),s(),o(44,"mat-option",19),c(45,"Common"),s(),o(46,"mat-option",20),c(47,"Unusual"),s(),o(48,"mat-option",21),c(49,"Rare"),s()()()(),_(50,va,4,0,"div",22),_(51,ya,11,0,"div",23),_(52,xa,11,0,"div",24),_(53,ka,4,1),s()),e&2&&(d(8),f(t.totalPigeons()>0?8:9),d(11),xe("ngModel",t.searchTerm),d(6),xe("value",t.selectedGang),d(16),xe("value",t.selectedDistinctiveness),d(9),f(t.isLoading()?50:-1),d(),f(!t.isLoading()&&t.filteredPigeons().length===0&&t.totalPigeons()===0?51:-1),d(),f(!t.isLoading()&&t.filteredPigeons().length===0&&t.totalPigeons()>0?52:-1),d(),f(!t.isLoading()&&t.filteredPigeons().length>0?53:-1))},dependencies:[Mt,Ct,ti,Xt,Jt,ei,kt,Kt,Vt,Wt,Gt,Nt,jt,Ht,Bt,Ee,Ft,Lt,zt,mi,di,Q,Ze,Xe,U,ne,ae,H,ni,ii,ai,ki,Mi,Si,nt,$t],styles:[".deck-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.deck-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:32px;flex-wrap:wrap;gap:16px}.header-content[_ngcontent-%COMP%]{flex:1}.deck-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;margin:0 0 8px;font-size:2rem;font-weight:500;color:#333}.deck-subtitle[_ngcontent-%COMP%]{margin:0;color:#666;font-size:1.1rem}.header-actions[_ngcontent-%COMP%]{display:flex;gap:12px}.filters-section[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:32px;flex-wrap:wrap}.search-field[_ngcontent-%COMP%]{flex:2;min-width:300px}.filter-field[_ngcontent-%COMP%]{flex:1;min-width:150px}.loading-container[_ngcontent-%COMP%], .empty-state[_ngcontent-%COMP%], .no-results-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:60px 20px;text-align:center}.empty-icon[_ngcontent-%COMP%], .no-results-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;color:#ccc;margin-bottom:16px}.pigeons-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(320px,1fr));gap:24px;margin-bottom:32px}.pigeon-card[_ngcontent-%COMP%]{transition:transform .2s ease,box-shadow .2s ease;border-left:4px solid #ddd}.pigeon-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.pigeon-card.gang-one[_ngcontent-%COMP%]{border-left-color:#f44336}.pigeon-card.gang-two[_ngcontent-%COMP%]{border-left-color:#2196f3}.pigeon-card.gang-three[_ngcontent-%COMP%]{border-left-color:#4caf50}.pigeon-card.gang-four[_ngcontent-%COMP%]{border-left-color:#ff9800}.pigeon-card.gang-five[_ngcontent-%COMP%]{border-left-color:#9c27b0}.pigeon-avatar[_ngcontent-%COMP%]{background-color:#e0e0e0}.pigeon-description[_ngcontent-%COMP%]{margin:12px 0;line-height:1.4;color:#555}.capture-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;color:#888;font-size:.9rem}.capture-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;width:1rem;height:1rem}mat-chip-set[_ngcontent-%COMP%]{margin-bottom:8px}.gang-chip.gang-one[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828}.gang-chip.gang-two[_ngcontent-%COMP%]{background-color:#e3f2fd;color:#1565c0}.gang-chip.gang-three[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#2e7d32}.gang-chip.gang-four[_ngcontent-%COMP%]{background-color:#fff3e0;color:#ef6c00}.gang-chip.gang-five[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2}.distinctiveness-chip.common[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#666}.distinctiveness-chip.unusual[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57c00}.distinctiveness-chip.rare[_ngcontent-%COMP%]{background-color:#fce4ec;color:#c2185b}@media (max-width: 768px){.deck-container[_ngcontent-%COMP%]{padding:16px}.deck-header[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filters-section[_ngcontent-%COMP%]{flex-direction:column}.search-field[_ngcontent-%COMP%], .filter-field[_ngcontent-%COMP%]{min-width:unset}.pigeons-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}}"]})};export{wi as PigeonDeckComponent};
