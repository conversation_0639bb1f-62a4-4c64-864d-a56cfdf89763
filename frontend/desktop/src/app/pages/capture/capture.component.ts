import { Component, inject, signal, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Subject, takeUntil } from 'rxjs';

import { FirebaseService, PigeonWithBasePigeon } from '../../services/firebase.service';
import { AnalysisPollingService, AnalysisProgress } from '../../services/analysis-polling.service';
import { FileUploadComponent, UploadResult } from '../../components/file-upload/file-upload.component';
import { AnalysisProgressComponent } from '../../components/analysis-progress/analysis-progress.component';
import { PigeonDisplayComponent } from '../../components/pigeon-display/pigeon-display.component';

type CaptureState = 'upload' | 'analyzing' | 'complete' | 'error';

@Component({
  selector: 'app-capture',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSnackBarModule,
    FileUploadComponent,
    AnalysisProgressComponent,
    PigeonDisplayComponent
  ],
  template: `
    <div class="capture-container">
      <!-- Upload State -->
      @if (currentState() === 'upload') {
        <div class="state-container">
          <div class="welcome-section">
            <h1 class="capture-title">
              <mat-icon>camera_alt</mat-icon>
              Capture a Pigeon
            </h1>
            <p class="capture-subtitle">
              Take a photo of a pigeon to analyze its characteristics and add it to your collection.
            </p>
          </div>
          
          <app-file-upload (uploadComplete)="onUploadComplete($event)"></app-file-upload>
          
          <div class="info-section">
            <mat-card class="info-card">
              <mat-card-header>
                <mat-card-title>
                  <mat-icon>info</mat-icon>
                  How it works
                </mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <ol class="steps-list">
                  <li>Take or upload a clear photo of a pigeon</li>
                  <li>Our AI will analyze the pigeon's characteristics</li>
                  <li>The pigeon will be assigned to a gang based on location</li>
                  <li>Add the pigeon to your personal collection</li>
                </ol>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      }

      <!-- Analysis State -->
      @if (currentState() === 'analyzing') {
        <div class="state-container">
          <app-analysis-progress [progress]="analysisProgress()"></app-analysis-progress>
          
          <div class="action-buttons">
            <button mat-stroked-button (click)="startNewAnalysis()" class="new-analysis-btn">
              <mat-icon>add_a_photo</mat-icon>
              Analyze Another Pigeon
            </button>
          </div>
        </div>
      }

      <!-- Complete State -->
      @if (currentState() === 'complete') {
        <div class="state-container">
          <div class="success-header">
            <h2>
              <mat-icon>check_circle</mat-icon>
              Pigeon Captured Successfully!
            </h2>
          </div>
          
          <app-pigeon-display [pigeon]="capturedPigeon()"></app-pigeon-display>
          
          <div class="action-buttons">
            <button mat-raised-button color="primary" (click)="startNewAnalysis()" class="new-analysis-btn">
              <mat-icon>add_a_photo</mat-icon>
              Capture Another Pigeon
            </button>
            <button mat-stroked-button routerLink="/deck" class="view-deck-btn">
              <mat-icon>collections</mat-icon>
              View My Deck
            </button>
          </div>
        </div>
      }

      <!-- Error State -->
      @if (currentState() === 'error') {
        <div class="state-container error-state">
          <mat-card class="error-card">
            <mat-card-header>
              <div mat-card-avatar class="error-avatar">
                <mat-icon>error</mat-icon>
              </div>
              <mat-card-title>Analysis Failed</mat-card-title>
              <mat-card-subtitle>Something went wrong during the pigeon analysis</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>We encountered an error while analyzing your pigeon image. Please try again.</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-raised-button color="primary" (click)="startNewAnalysis()">
                <mat-icon>refresh</mat-icon>
                Try Again
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      }
    </div>
  `,
  styles: [`
    .capture-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .state-container {
      margin-bottom: 24px;
    }

    .welcome-section {
      text-align: center;
      margin-bottom: 32px;
    }

    .capture-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      margin: 0 0 16px 0;
      font-size: 2.2rem;
      font-weight: 500;
      color: #333;
    }

    .capture-subtitle {
      font-size: 1.1rem;
      color: #666;
      margin: 0;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.5;
    }

    .info-section {
      margin-top: 48px;
    }

    .info-card {
      background-color: #f8f9fa;
    }

    .steps-list {
      margin: 0;
      padding-left: 20px;
    }

    .steps-list li {
      margin-bottom: 8px;
      line-height: 1.4;
    }

    .success-header {
      text-align: center;
      margin-bottom: 24px;
    }

    .success-header h2 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      color: #4caf50;
      margin: 0;
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-top: 24px;
      flex-wrap: wrap;
    }

    .new-analysis-btn,
    .view-deck-btn {
      min-width: 200px;
    }

    .error-state {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
    }

    .error-card {
      max-width: 400px;
      text-align: center;
    }

    .error-avatar {
      background-color: #f44336;
      color: white;
    }

    // Responsive design
    @media (max-width: 768px) {
      .capture-container {
        padding: 16px;
      }
      
      .capture-title {
        font-size: 1.8rem;
      }
      
      .action-buttons {
        flex-direction: column;
        align-items: center;
      }
      
      .new-analysis-btn,
      .view-deck-btn {
        width: 100%;
        max-width: 300px;
      }
    }
  `]
})
export class CaptureComponent implements OnInit, OnDestroy {
  private firebaseService = inject(FirebaseService);
  private analysisPollingService = inject(AnalysisPollingService);
  private snackBar = inject(MatSnackBar);
  private destroy$ = new Subject<void>();

  // Application state
  currentState = signal<CaptureState>('upload');
  analysisProgress = signal<AnalysisProgress | null>(null);
  capturedPigeon = signal<PigeonWithBasePigeon | null>(null);
  currentCaptureId = signal<string | null>(null);

  ngOnInit() {
    // Sign in anonymously when the component starts
    this.signInUser();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.analysisPollingService.stopAllPolling();
  }

  private async signInUser() {
    try {
      await this.firebaseService.signInAnonymously();
      console.log('User signed in anonymously');
    } catch (error) {
      console.error('Failed to sign in:', error);
      this.snackBar.open('Failed to initialize app. Please refresh the page.', 'Close', {
        duration: 5000
      });
    }
  }

  onUploadComplete(result: UploadResult) {
    console.log('Upload complete:', result);
    this.currentCaptureId.set(result.captureId);
    this.currentState.set('analyzing');
    this.startAnalysisPolling(result.captureId);
  }

  private startAnalysisPolling(captureId: string) {
    this.analysisPollingService.startPolling(captureId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (progress) => {
          console.log('Analysis progress:', progress);
          this.analysisProgress.set(progress);

          if (progress.status === 'FINISHED') {
            this.loadCompletedPigeon(captureId);
          } else if (progress.status === 'ERROR') {
            this.currentState.set('error');
          }
        },
        error: (error) => {
          console.error('Polling error:', error);
          this.currentState.set('error');
          this.snackBar.open('Failed to track analysis progress', 'Close', {
            duration: 5000
          });
        }
      });
  }

  private loadCompletedPigeon(captureId: string) {
    this.firebaseService.getPigeonByCaptureId(captureId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          console.log('Pigeon loaded:', result);
          this.capturedPigeon.set(result.pigeon);
          this.currentState.set('complete');
        },
        error: (error) => {
          console.error('Failed to load pigeon:', error);
          this.currentState.set('error');
          this.snackBar.open('Failed to load pigeon data', 'Close', {
            duration: 5000
          });
        }
      });
  }

  startNewAnalysis() {
    // Reset state for new analysis
    this.currentState.set('upload');
    this.analysisProgress.set(null);
    this.capturedPigeon.set(null);
    this.currentCaptureId.set(null);

    // Stop any existing polling
    this.analysisPollingService.stopAllPolling();
  }
}
