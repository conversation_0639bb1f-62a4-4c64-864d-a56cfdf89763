import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { PigeonWithBasePigeon } from '../../services/firebase.service';

@Component({
  selector: 'app-pigeon-display',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatChipsModule,
    MatIconModule,
    MatButtonModule,
    MatDividerModule
  ],
  template: `
    @if (pigeon) {
      <div class="pigeon-container">
        <mat-card class="pigeon-card">
          <mat-card-header>
            <div mat-card-avatar class="pigeon-avatar">
              <mat-icon>pets</mat-icon>
            </div>
            <mat-card-title>{{ pigeon.basePigeon.name }}</mat-card-title>
            <mat-card-subtitle>
              Captured {{ formatDate(pigeon.captureDate) }}
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <!-- Gang Information -->
            <div class="info-section">
              <h3>
                <mat-icon>group</mat-icon>
                Gang Information
              </h3>
              <mat-chip-set>
                <mat-chip [class]="'gang-' + pigeon.gang.toLowerCase()">
                  {{ pigeon.gang }} Gang
                </mat-chip>
              </mat-chip-set>
            </div>

            <mat-divider></mat-divider>

            <!-- Distinctiveness -->
            <div class="info-section">
              <h3>
                <mat-icon>star</mat-icon>
                Distinctiveness
              </h3>
              <mat-chip-set>
                <mat-chip [class]="'distinctiveness-' + pigeon.basePigeon.distinctiveness.toLowerCase()">
                  {{ pigeon.basePigeon.distinctiveness }}
                </mat-chip>
              </mat-chip-set>
            </div>

            <mat-divider></mat-divider>

            <!-- Description -->
            <div class="info-section">
              <h3>
                <mat-icon>description</mat-icon>
                Description
              </h3>
              <p class="description-text">{{ pigeon.basePigeon.description }}</p>
            </div>

            <mat-divider></mat-divider>

            <!-- Location Information -->
            <div class="info-section">
              <h3>
                <mat-icon>location_on</mat-icon>
                Capture Location
              </h3>
              <div class="location-info">
                <div class="coordinate">
                  <strong>Latitude:</strong> {{ pigeon.coordinates.latitude.toFixed(6) }}
                </div>
                <div class="coordinate">
                  <strong>Longitude:</strong> {{ pigeon.coordinates.longitude.toFixed(6) }}
                </div>
              </div>
            </div>

            <mat-divider></mat-divider>

            <!-- Capture Details -->
            <div class="info-section">
              <h3>
                <mat-icon>info</mat-icon>
                Capture Details
              </h3>
              <div class="capture-details">
                <div class="detail-item">
                  <strong>Capture ID:</strong> 
                  <code>{{ pigeon.captureId }}</code>
                </div>
                <div class="detail-item">
                  <strong>Base Pigeon ID:</strong> 
                  <code>{{ pigeon.basePigeonId }}</code>
                </div>
              </div>
            </div>
          </mat-card-content>

          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>share</mat-icon>
              Share
            </button>
            <button mat-button color="accent">
              <mat-icon>favorite_border</mat-icon>
              Add to Favorites
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    }
  `,
  styles: [`
    .pigeon-container {
      max-width: 600px;
      margin: 20px auto;
      padding: 0 16px;
    }

    .pigeon-card {
      width: 100%;
    }

    .pigeon-avatar {
      background-color: #2196f3;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .info-section {
      margin: 16px 0;
    }

    .info-section h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 12px 0;
      color: #333;
      font-size: 16px;
      font-weight: 500;
    }

    .info-section h3 mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    .description-text {
      margin: 0;
      line-height: 1.6;
      color: #666;
    }

    .location-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .coordinate {
      font-family: 'Roboto Mono', monospace;
      font-size: 14px;
    }

    .capture-details {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .detail-item {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .detail-item code {
      background-color: #f5f5f5;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Roboto Mono', monospace;
      font-size: 12px;
      word-break: break-all;
    }

    /* Gang-specific styling */
    .gang-one { background-color: #f44336; color: white; }
    .gang-two { background-color: #2196f3; color: white; }
    .gang-three { background-color: #4caf50; color: white; }
    .gang-four { background-color: #ff9800; color: white; }
    .gang-five { background-color: #9c27b0; color: white; }

    /* Distinctiveness styling */
    .distinctiveness-common { 
      background-color: #e0e0e0; 
      color: #333; 
    }
    .distinctiveness-unusual { 
      background-color: #fff3e0; 
      color: #f57c00; 
      border: 1px solid #ffb74d;
    }
    .distinctiveness-rare { 
      background-color: #fce4ec; 
      color: #c2185b; 
      border: 1px solid #f48fb1;
    }

    mat-divider {
      margin: 16px 0;
    }

    mat-card-actions {
      padding: 16px;
      display: flex;
      gap: 8px;
    }

    @media (max-width: 600px) {
      .pigeon-container {
        margin: 10px auto;
        padding: 0 8px;
      }
      
      .location-info {
        font-size: 12px;
      }
      
      .detail-item code {
        font-size: 10px;
      }
    }
  `]
})
export class PigeonDisplayComponent {
  @Input() pigeon: PigeonWithBasePigeon | null = null;

  formatDate(date: Date): string {
    if (!date) return '';
    
    const dateObj = date instanceof Date ? date : new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
