import { Component, inject, signal, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

import { FirebaseService, PigeonWithBasePigeon } from '../../services/firebase.service';

@Component({
  selector: 'app-pigeon-deck',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterLink,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatSnackBarModule
  ],
  template: `
    <div class="deck-container">
      <!-- Header -->
      <div class="deck-header">
        <div class="header-content">
          <h1 class="deck-title">
            <mat-icon>collections</mat-icon>
            My Pigeon Deck
          </h1>
          <p class="deck-subtitle">
            @if (totalPigeons() > 0) {
              {{ totalPigeons() }} pigeons captured
            } @else {
              No pigeons captured yet
            }
          </p>
        </div>
        
        <div class="header-actions">
          <button mat-raised-button color="primary" routerLink="/">
            <mat-icon>add_a_photo</mat-icon>
            Capture New Pigeon
          </button>
        </div>
      </div>

      <!-- Search and Filters -->
      <div class="filters-section">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search pigeons</mat-label>
          <input matInput 
                 [(ngModel)]="searchTerm" 
                 (ngModelChange)="onSearchChange()"
                 placeholder="Search by name, gang, or characteristics...">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>Filter by Gang</mat-label>
          <mat-select [(value)]="selectedGang" (selectionChange)="onFilterChange()">
            <mat-option value="">All Gangs</mat-option>
            <mat-option value="ONE">Gang One</mat-option>
            <mat-option value="TWO">Gang Two</mat-option>
            <mat-option value="THREE">Gang Three</mat-option>
            <mat-option value="FOUR">Gang Four</mat-option>
            <mat-option value="FIVE">Gang Five</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>Filter by Distinctiveness</mat-label>
          <mat-select [(value)]="selectedDistinctiveness" (selectionChange)="onFilterChange()">
            <mat-option value="">All Types</mat-option>
            <mat-option value="common">Common</mat-option>
            <mat-option value="unusual">Unusual</mat-option>
            <mat-option value="rare">Rare</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Loading State -->
      @if (isLoading()) {
        <div class="loading-container">
          <mat-spinner></mat-spinner>
          <p>Loading your pigeon deck...</p>
        </div>
      }

      <!-- Empty State -->
      @if (!isLoading() && filteredPigeons().length === 0 && totalPigeons() === 0) {
        <div class="empty-state">
          <mat-icon class="empty-icon">pets</mat-icon>
          <h2>No Pigeons Yet</h2>
          <p>Start your pigeon collection by capturing your first pigeon!</p>
          <button mat-raised-button color="primary" routerLink="/">
            <mat-icon>add_a_photo</mat-icon>
            Capture Your First Pigeon
          </button>
        </div>
      }

      <!-- No Results State -->
      @if (!isLoading() && filteredPigeons().length === 0 && totalPigeons() > 0) {
        <div class="no-results-state">
          <mat-icon class="no-results-icon">search_off</mat-icon>
          <h2>No Pigeons Found</h2>
          <p>Try adjusting your search or filter criteria.</p>
          <button mat-button (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            Clear Filters
          </button>
        </div>
      }

      <!-- Pigeon Grid -->
      @if (!isLoading() && filteredPigeons().length > 0) {
        <div class="pigeons-grid">
          @for (pigeon of filteredPigeons(); track pigeon.id) {
            <mat-card class="pigeon-card" [class]="'gang-' + pigeon.gang.toLowerCase()">
              <mat-card-header>
                <div mat-card-avatar class="pigeon-avatar">
                  <mat-icon>pets</mat-icon>
                </div>
                <mat-card-title>{{ pigeon.basePigeon.name }}</mat-card-title>
                <mat-card-subtitle>
                  <mat-chip-set>
                    <mat-chip [class]="'gang-chip gang-' + pigeon.gang.toLowerCase()">
                      Gang {{ pigeon.gang }}
                    </mat-chip>
                    <mat-chip [class]="'distinctiveness-chip ' + pigeon.basePigeon.distinctiveness">
                      {{ pigeon.basePigeon.distinctiveness | titlecase }}
                    </mat-chip>
                  </mat-chip-set>
                </mat-card-subtitle>
              </mat-card-header>

              <mat-card-content>
                <p class="pigeon-description">{{ pigeon.basePigeon.description }}</p>
                <div class="capture-info">
                  <small>
                    <mat-icon>schedule</mat-icon>
                    Captured {{ formatDate(pigeon.captureDate) }}
                  </small>
                </div>
              </mat-card-content>

              <mat-card-actions>
                <button mat-button color="primary">
                  <mat-icon>visibility</mat-icon>
                  View Details
                </button>
                <button mat-button>
                  <mat-icon>share</mat-icon>
                  Share
                </button>
              </mat-card-actions>
            </mat-card>
          }
        </div>

        <!-- Pagination -->
        @if (totalPigeons() > pageSize) {
          <mat-paginator
            [length]="totalPigeons()"
            [pageSize]="pageSize"
            [pageSizeOptions]="[12, 24, 48]"
            [pageIndex]="currentPage"
            (page)="onPageChange($event)"
            showFirstLastButtons>
          </mat-paginator>
        }
      }
    </div>
  `,
  styles: [`
    .deck-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .deck-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 32px;
      flex-wrap: wrap;
      gap: 16px;
    }

    .header-content {
      flex: 1;
    }

    .deck-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 0 8px 0;
      font-size: 2rem;
      font-weight: 500;
      color: #333;
    }

    .deck-subtitle {
      margin: 0;
      color: #666;
      font-size: 1.1rem;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .filters-section {
      display: flex;
      gap: 16px;
      margin-bottom: 32px;
      flex-wrap: wrap;
    }

    .search-field {
      flex: 2;
      min-width: 300px;
    }

    .filter-field {
      flex: 1;
      min-width: 150px;
    }

    .loading-container,
    .empty-state,
    .no-results-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      text-align: center;
    }

    .empty-icon,
    .no-results-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #ccc;
      margin-bottom: 16px;
    }

    .pigeons-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 24px;
      margin-bottom: 32px;
    }

    .pigeon-card {
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      border-left: 4px solid #ddd;
    }

    .pigeon-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .pigeon-card.gang-one { border-left-color: #f44336; }
    .pigeon-card.gang-two { border-left-color: #2196f3; }
    .pigeon-card.gang-three { border-left-color: #4caf50; }
    .pigeon-card.gang-four { border-left-color: #ff9800; }
    .pigeon-card.gang-five { border-left-color: #9c27b0; }

    .pigeon-avatar {
      background-color: #e0e0e0;
    }

    .pigeon-description {
      margin: 12px 0;
      line-height: 1.4;
      color: #555;
    }

    .capture-info {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #888;
      font-size: 0.9rem;
    }

    .capture-info mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }

    mat-chip-set {
      margin-bottom: 8px;
    }

    .gang-chip.gang-one { background-color: #ffebee; color: #c62828; }
    .gang-chip.gang-two { background-color: #e3f2fd; color: #1565c0; }
    .gang-chip.gang-three { background-color: #e8f5e8; color: #2e7d32; }
    .gang-chip.gang-four { background-color: #fff3e0; color: #ef6c00; }
    .gang-chip.gang-five { background-color: #f3e5f5; color: #7b1fa2; }

    .distinctiveness-chip.common { background-color: #f5f5f5; color: #666; }
    .distinctiveness-chip.unusual { background-color: #fff8e1; color: #f57c00; }
    .distinctiveness-chip.rare { background-color: #fce4ec; color: #c2185b; }

    // Responsive design
    @media (max-width: 768px) {
      .deck-container {
        padding: 16px;
      }
      
      .deck-header {
        flex-direction: column;
        align-items: stretch;
      }
      
      .filters-section {
        flex-direction: column;
      }
      
      .search-field,
      .filter-field {
        min-width: unset;
      }
      
      .pigeons-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }
  `]
})
export class PigeonDeckComponent implements OnInit, OnDestroy {
  private firebaseService = inject(FirebaseService);
  private snackBar = inject(MatSnackBar);
  private destroy$ = new Subject<void>();

  // State
  pigeons = signal<PigeonWithBasePigeon[]>([]);
  filteredPigeons = signal<PigeonWithBasePigeon[]>([]);
  totalPigeons = signal<number>(0);
  isLoading = signal<boolean>(true);

  // Filters
  searchTerm = '';
  selectedGang = '';
  selectedDistinctiveness = '';

  // Pagination
  currentPage = 0;
  pageSize = 12;

  ngOnInit() {
    this.loadPigeons();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadPigeons() {
    this.isLoading.set(true);
    
    this.firebaseService.getTrainerPigeondex(this.pageSize, this.currentPage * this.pageSize)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          this.pigeons.set(result.pigeons);
          this.totalPigeons.set(result.total);
          this.applyFilters();
          this.isLoading.set(false);
        },
        error: (error) => {
          console.error('Failed to load pigeon deck:', error);
          this.snackBar.open('Failed to load your pigeon deck', 'Close', {
            duration: 5000
          });
          this.isLoading.set(false);
        }
      });
  }

  onSearchChange() {
    // Debounce search to avoid too many filter operations
    setTimeout(() => this.applyFilters(), 300);
  }

  onFilterChange() {
    this.applyFilters();
  }

  private applyFilters() {
    let filtered = [...this.pigeons()];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(pigeon => 
        pigeon.basePigeon.name.toLowerCase().includes(searchLower) ||
        pigeon.basePigeon.description.toLowerCase().includes(searchLower) ||
        pigeon.gang.toLowerCase().includes(searchLower)
      );
    }

    // Apply gang filter
    if (this.selectedGang) {
      filtered = filtered.filter(pigeon => pigeon.gang === this.selectedGang);
    }

    // Apply distinctiveness filter
    if (this.selectedDistinctiveness) {
      filtered = filtered.filter(pigeon => pigeon.basePigeon.distinctiveness === this.selectedDistinctiveness);
    }

    this.filteredPigeons.set(filtered);
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedGang = '';
    this.selectedDistinctiveness = '';
    this.applyFilters();
  }

  onPageChange(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadPigeons();
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
}
