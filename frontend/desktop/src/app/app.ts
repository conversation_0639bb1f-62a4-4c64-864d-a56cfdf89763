import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, RouterLinkActive } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { FirebaseService } from './services/firebase.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterLink,
    RouterLinkActive,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatSnackBarModule
  ],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class App implements OnInit {
  private firebaseService = inject(FirebaseService);
  private snackBar = inject(MatSnackBar);

  protected title = 'Pigeon Analyzer';

  ngOnInit() {
    // Sign in anonymously when the app starts
    this.signInUser();
  }

  private async signInUser() {
    try {
      await this.firebaseService.signInAnonymously();
      console.log('User signed in anonymously');
    } catch (error) {
      console.error('Failed to sign in:', error);
      this.snackBar.open('Failed to initialize app. Please refresh the page.', 'Close', {
        duration: 5000
      });
    }
  }
}
