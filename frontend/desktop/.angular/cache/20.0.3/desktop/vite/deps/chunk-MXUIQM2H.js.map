{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/error-options-DCNQlTOA.mjs", "../../../../../../node_modules/@angular/material/fesm2022/error-state-Dtb1IHM-.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.dirty || form && form.submitted));\n  }\n  static ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ShowOnDirtyErrorStateMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ShowOnDirtyErrorStateMatcher,\n    factory: ShowOnDirtyErrorStateMatcher.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowOnDirtyErrorStateMatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.touched || form && form.submitted));\n  }\n  static ɵfac = function ErrorStateMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ErrorStateMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ErrorStateMatcher,\n    factory: ErrorStateMatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ErrorStateMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { ErrorStateMatcher as E, ShowOnDirtyErrorStateMatcher as S };\n", "/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nclass _ErrorStateTracker {\n    _defaultMatcher;\n    ngControl;\n    _parentFormGroup;\n    _parentForm;\n    _stateChanges;\n    /** Whether the tracker is currently in an error state. */\n    errorState = false;\n    /** User-defined matcher for the error state. */\n    matcher;\n    constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {\n        this._defaultMatcher = _defaultMatcher;\n        this.ngControl = ngControl;\n        this._parentFormGroup = _parentFormGroup;\n        this._parentForm = _parentForm;\n        this._stateChanges = _stateChanges;\n    }\n    /** Updates the error state based on the provided error state matcher. */\n    updateErrorState() {\n        const oldState = this.errorState;\n        const parent = this._parentFormGroup || this._parentForm;\n        const matcher = this.matcher || this._defaultMatcher;\n        const control = this.ngControl ? this.ngControl.control : null;\n        const newState = matcher?.isErrorState(control, parent) ?? false;\n        if (newState !== oldState) {\n            this.errorState = newState;\n            this._stateChanges.next();\n        }\n    }\n}\n\nexport { _ErrorStateTracker as _ };\n\n"], "mappings": ";;;;;;;AAIA,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,aAAa,SAAS,MAAM;AAC1B,WAAO,CAAC,EAAE,WAAW,QAAQ,YAAY,QAAQ,SAAS,QAAQ,KAAK;AAAA,EACzE;AAAA,EACA,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,8BAA6B;AAAA,EACxC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,aAAa,SAAS,MAAM;AAC1B,WAAO,CAAC,EAAE,WAAW,QAAQ,YAAY,QAAQ,WAAW,QAAQ,KAAK;AAAA,EAC3E;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,mBAAkB;AAAA,IAC3B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACtCH,IAAM,qBAAN,MAAyB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,aAAa;AAAA;AAAA,EAEb;AAAA,EACA,YAAY,iBAAiB,WAAW,kBAAkB,aAAa,eAAe;AAClF,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AAAA,EACzB;AAAA;AAAA,EAEA,mBAAmB;AACf,UAAM,WAAW,KAAK;AACtB,UAAM,SAAS,KAAK,oBAAoB,KAAK;AAC7C,UAAM,UAAU,KAAK,WAAW,KAAK;AACrC,UAAM,UAAU,KAAK,YAAY,KAAK,UAAU,UAAU;AAC1D,UAAM,WAAW,SAAS,aAAa,SAAS,MAAM,KAAK;AAC3D,QAAI,aAAa,UAAU;AACvB,WAAK,aAAa;AAClB,WAAK,cAAc,KAAK;AAAA,IAC5B;AAAA,EACJ;AACJ;", "names": []}