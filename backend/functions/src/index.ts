/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import * as dotenv from "dotenv";
import * as logger from "firebase-functions/logger";
import { onCall } from "firebase-functions/v2/https";
import { onObjectFinalized } from "firebase-functions/v2/storage";
import path from "path";
dotenv.config({ path: path.resolve(__dirname, "../.env") });

import * as admin from "firebase-admin";
admin.initializeApp();

import { FirestoreBasePigeonRepository } from "./infrastructure/firebase/FirestoreBasePigeonRepository";
import { FirestorePigeonRepository } from "./infrastructure/firebase/FirestorePigeonRepository";
import { FirestoreTrainerRepository } from "./infrastructure/firebase/FirestoreTrainerRepository";
import { FirestoreAnalysisJobRepository } from "./infrastructure/firebase/FirestoreAnalysisJobRepository";
import { AdvancedGeoLocationService } from "./infrastructure/geolocation/AdvancedGeoLocationService";
import { OpenAiServiceV2 } from "./infrastructure/openai/OpenAiServiceV2";
import { CapturePigeonUseCase } from "./use-cases/CapturePigeonUseCase";
import { GetTrainerPigeonsUseCase } from "./use-cases/GetTrainerPigeonsUseCase";
import { CreateAnalysisJobUseCase } from "./use-cases/CreateAnalysisJobUseCase";
import { GetAnalysisJobStatusUseCase } from "./use-cases/GetAnalysisJobStatusUseCase";

// Initialisation de l'admin SDK Firebase

const trainerRepo = new FirestoreTrainerRepository();
const pigeonRepo = new FirestorePigeonRepository();
const basePigeonRepo = new FirestoreBasePigeonRepository();
const analysisJobRepo = new FirestoreAnalysisJobRepository();

// Original implementation with OpenAiAiService and CapturePigeonUseCase
const aiService = new OpenAiServiceV2(basePigeonRepo);
const capturePigeonUseCase = new CapturePigeonUseCase(
    trainerRepo,
    pigeonRepo,
    aiService,
    new AdvancedGeoLocationService(),
    analysisJobRepo,
);

// Analysis job use cases
const createAnalysisJobUseCase = new CreateAnalysisJobUseCase(analysisJobRepo, trainerRepo);
const getAnalysisJobStatusUseCase = new GetAnalysisJobStatusUseCase(analysisJobRepo);

const getTrainerPigeonsUseCase = new GetTrainerPigeonsUseCase(trainerRepo, pigeonRepo, basePigeonRepo);

// export const capturePigeon = onRequest(async (req, res) => {
//     try {
//         const {trainerId, pigeonData} = req.body;
//         const newPigeon = await capturePigeonUseCase.execute(trainerId, pigeonData);
//         res.status(200).json(newPigeon);
//     } catch (error: any) {
//         res.status(400).json({error: error.message});
//     }
// });

export const onFileUpload = onObjectFinalized(
    {
        region: "us-east1",
        bucket: "pigeon-gogo.firebasestorage.app",
    },
    async (object) => {
        const { data } = object;
        const filePath = data.name;

        const signedFileUrl = (
            await admin
                .storage()
                .bucket("gs://pigeon-gogo.firebasestorage.app")
                .file(filePath)
                .getSignedUrl({ action: "read", expires: Date.now() + 1000 * 60 * 60 })
        )[0];

        if (filePath.startsWith("shots")) {
            // File path should be in format: shots/<trainerId>/<captureId>/<latitude>_<longitude>_<fileName>.jpg
            logger.info(`Processing file upload: ${filePath}`);

            try {
                const pathParts = filePath.split("/");
                if (pathParts.length < 3) {
                    logger.error("Invalid file path format");
                    return;
                }

                const trainerId = pathParts[1];
                const captureId = pathParts[2];

                // First, create the analysis job if it doesn't exist
                try {
                    await createAnalysisJobUseCase.execute({
                        captureId,
                        trainerId,
                        storageFilePath: filePath,
                    });
                    logger.info(`Created analysis job for captureId: ${captureId}`);
                } catch (error) {
                    // Job might already exist, which is fine
                    if (error instanceof Error && error.message.includes("already exists")) {
                        logger.info(`Analysis job already exists for captureId: ${captureId}`);
                    } else {
                        logger.error("Error creating analysis job:", error);
                        return;
                    }
                }

                // Then process the capture
                capturePigeonUseCase.execute(filePath, signedFileUrl).catch((error: unknown) => {
                    logger.error("Error processing file upload:", error);
                    return;
                });
            } catch (error) {
                logger.error("Error in onFileUpload:", error);
            }
            return;
        }
        return;
    },
);

/**
 * HTTP function to get a trainer's pigeondex (all pigeons owned by the trainer)
 * Requires authentication - the user can only access their own pigeondex
 */
export const getTrainerPigeondex = onCall(
    {
        cors: true,
        region: "us-east1",
    },
    async (request) => {
        try {
            if (!request.auth) {
                throw new Error("Unauthorized - Authentication required");
            }

            const trainerId = request.auth.uid;
            const { limit, offset } = request.data ?? {};
            logger.info(`Getting pigeondex for trainer: ${trainerId}, limit: ${limit}, offset: ${offset}`);

            const pigeondex = await getTrainerPigeonsUseCase.execute(trainerId, limit, offset);
            return pigeondex;
        } catch (error: unknown) {
            logger.error("Error getting trainer pigeondex:", error);
            throw error;
        }
    },
);

// /**
//  * HTTP function to create an analysis job for a pigeon capture
//  * Requires authentication - the user can only create jobs for themselves
//  */
// export const createAnalysisJob = onCall(
//     {
//         region: "us-east1",
//     },
//     async (request) => {
//         try {
//             if (!request.auth) {
//                 throw new Error("Unauthorized - Authentication required");
//             }

//             const trainerId = request.auth.uid;
//             const { captureId, storageFilePath } = request.data ?? {};

//             if (!captureId || !storageFilePath) {
//                 throw new Error("Missing required parameters: captureId and storageFilePath");
//             }

//             logger.info(`Creating analysis job for trainer: ${trainerId}, captureId: ${captureId}`);

//             const analysisJob = await createAnalysisJobUseCase.execute({
//                 captureId,
//                 trainerId,
//                 storageFilePath,
//             });

//             return {
//                 captureId: analysisJob.captureId,
//                 status: analysisJob.status,
//                 createdAt: analysisJob.createdAt,
//             };
//         } catch (error: unknown) {
//             logger.error("Error creating analysis job:", error);
//             throw error;
//         }
//     },
// );

/**
 * HTTP function to get the status of an analysis job
 * Requires authentication - the user can only access their own jobs
 */
export const getAnalysisJobStatus = onCall(
    {
        cors: true,
        region: "us-east1",
    },
    async (request) => {
        try {
            if (!request.auth) {
                throw new Error("Unauthorized - Authentication required");
            }

            const trainerId = request.auth.uid;
            const { captureId } = request.data ?? {};

            if (!captureId) {
                throw new Error("Missing required parameter: captureId");
            }

            logger.info(`Getting analysis job status for trainer: ${trainerId}, captureId: ${captureId}`);

            const analysisJob = await getAnalysisJobStatusUseCase.execute(captureId);

            if (!analysisJob) {
                throw new Error("Analysis job not found");
            }

            // Verify that the job belongs to the authenticated user
            if (analysisJob.trainerId !== trainerId) {
                throw new Error("Unauthorized - You can only access your own analysis jobs");
            }

            return {
                captureId: analysisJob.captureId,
                status: analysisJob.status,
                errorCode: analysisJob.errorCode,
                errorMessage: analysisJob.errorMessage,
                createdAt: analysisJob.createdAt,
                updatedAt: analysisJob.updatedAt,
                pigeonId: analysisJob.pigeonId,
            };
        } catch (error: unknown) {
            logger.error("Error getting analysis job status:", error);
            throw error;
        }
    },
);
